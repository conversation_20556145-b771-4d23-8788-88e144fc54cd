"""
Intelligent Strategy Selection Decision Tree
Based on <PERSON>'s decision-making framework from @AbundantlyErica

This module implements a sophisticated decision tree that weighs multiple market factors
to recommend the single best strategy for each stock with detailed confidence scoring.

Decision Framework:
1. Market Environment Assessment (Bull/Bear/Sideways + Volatility Regime)
2. Stock-Specific Factor Analysis (Technical, Fundamental, Options Flow)
3. <PERSON>'s Strategy Selection Criteria (from YouTube content)
4. Risk Management Overlay (Position sizing, Market conditions)
5. Confidence Scoring (Factor alignment and historical success rates)
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from enum import Enum
import math

from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime, SentimentLevel
from daily_outline import StrategyType, TradingSignal
from enhanced_strategies import STOCK_RULES

class StrategyConfidence(Enum):
    VERY_HIGH = "very_high"  # 85%+ confidence
    HIGH = "high"           # 70-84% confidence
    MODERATE = "moderate"   # 55-69% confidence
    LOW = "low"            # 40-54% confidence
    VERY_LOW = "very_low"  # <40% confidence

@dataclass
class StrategyRecommendation:
    """Complete strategy recommendation with detailed analysis"""
    symbol: str
    primary_strategy: StrategyType
    confidence: float  # 0-1 scale
    confidence_level: StrategyConfidence
    
    # Detailed reasoning
    market_environment_score: float
    stock_specific_score: float
    erica_criteria_score: float
    risk_adjusted_score: float
    
    # Factor contributions
    key_supporting_factors: List[str]
    key_risk_factors: List[str]
    
    # Alternative strategies
    alternative_strategies: List[Tuple[StrategyType, float]]  # (strategy, confidence)
    
    # Execution details
    recommended_dte: Tuple[int, int]  # (min, max) days to expiration
    recommended_delta: Tuple[float, float]  # (min, max) delta range
    position_size_multiplier: float  # Adjustment to base position size
    
    # Market condition triggers
    invalidation_triggers: List[str]  # Conditions that would invalidate this recommendation
    upgrade_triggers: List[str]      # Conditions that would increase confidence

class StrategyDecisionTree:
    """Intelligent strategy selection decision tree"""
    
    def __init__(self):
        self.decision_weights = self._initialize_decision_weights()
        self.erica_criteria = self._load_erica_criteria()
        
    def recommend_strategy(self, symbol: str, market_factors: MarketFactors, 
                          stock_factors: StockSpecificFactors) -> StrategyRecommendation:
        """
        Main decision tree logic to recommend optimal strategy
        
        Decision Process:
        1. Assess market environment compatibility for each strategy
        2. Evaluate stock-specific factors for strategy suitability
        3. Apply Erica's specific criteria and preferences
        4. Calculate risk-adjusted confidence scores
        5. Select highest scoring strategy with detailed reasoning
        """
        
        # Calculate strategy scores for each possible strategy
        strategy_scores = {}
        
        for strategy in StrategyType:
            score = self._calculate_strategy_score(
                strategy, symbol, market_factors, stock_factors
            )
            strategy_scores[strategy] = score
        
        # Select primary strategy (highest score)
        primary_strategy = max(strategy_scores.keys(), key=lambda s: strategy_scores[s]['total_score'])
        primary_score = strategy_scores[primary_strategy]
        
        # Calculate confidence level
        confidence = primary_score['total_score']
        confidence_level = self._classify_confidence(confidence)
        
        # Generate alternative strategies
        alternatives = []
        for strategy, score_data in strategy_scores.items():
            if strategy != primary_strategy and score_data['total_score'] > 0.4:
                alternatives.append((strategy, score_data['total_score']))
        
        # Sort alternatives by score
        alternatives.sort(key=lambda x: x[1], reverse=True)
        alternatives = alternatives[:2]  # Top 2 alternatives
        
        # Generate execution parameters
        dte_range = self._get_recommended_dte(primary_strategy, symbol, market_factors, stock_factors)
        delta_range = self._get_recommended_delta(primary_strategy, symbol, market_factors, stock_factors)
        position_multiplier = self._calculate_position_multiplier(primary_strategy, market_factors, stock_factors)
        
        # Generate supporting factors and risks
        supporting_factors = self._identify_supporting_factors(
            primary_strategy, symbol, market_factors, stock_factors, primary_score
        )
        risk_factors = self._identify_risk_factors(
            primary_strategy, symbol, market_factors, stock_factors, primary_score
        )
        
        # Generate triggers
        invalidation_triggers = self._generate_invalidation_triggers(
            primary_strategy, market_factors, stock_factors
        )
        upgrade_triggers = self._generate_upgrade_triggers(
            primary_strategy, market_factors, stock_factors
        )
        
        return StrategyRecommendation(
            symbol=symbol,
            primary_strategy=primary_strategy,
            confidence=confidence,
            confidence_level=confidence_level,
            market_environment_score=primary_score['market_score'],
            stock_specific_score=primary_score['stock_score'],
            erica_criteria_score=primary_score['erica_score'],
            risk_adjusted_score=primary_score['risk_score'],
            key_supporting_factors=supporting_factors,
            key_risk_factors=risk_factors,
            alternative_strategies=alternatives,
            recommended_dte=dte_range,
            recommended_delta=delta_range,
            position_size_multiplier=position_multiplier,
            invalidation_triggers=invalidation_triggers,
            upgrade_triggers=upgrade_triggers
        )
    
    def _calculate_strategy_score(self, strategy: StrategyType, symbol: str,
                                market_factors: MarketFactors, 
                                stock_factors: StockSpecificFactors) -> Dict:
        """Calculate comprehensive score for a strategy"""
        
        # 1. Market Environment Score (30% weight)
        market_score = self._score_market_environment(strategy, market_factors)
        
        # 2. Stock-Specific Score (25% weight)
        stock_score = self._score_stock_factors(strategy, symbol, stock_factors)
        
        # 3. Erica's Criteria Score (30% weight)
        erica_score = self._score_erica_criteria(strategy, symbol, market_factors, stock_factors)
        
        # 4. Risk-Adjusted Score (15% weight)
        risk_score = self._score_risk_factors(strategy, market_factors, stock_factors)
        
        # Calculate weighted total
        total_score = (
            market_score * 0.30 +
            stock_score * 0.25 +
            erica_score * 0.30 +
            risk_score * 0.15
        )
        
        return {
            'total_score': total_score,
            'market_score': market_score,
            'stock_score': stock_score,
            'erica_score': erica_score,
            'risk_score': risk_score
        }
    
    def _score_market_environment(self, strategy: StrategyType, market_factors: MarketFactors) -> float:
        """Score strategy based on current market environment"""
        score = 0.5  # Base score
        
        # Market regime compatibility
        if strategy == StrategyType.COVERED_CALL:
            # Covered calls work well in sideways to moderately bullish markets
            if market_factors.market_regime == MarketRegime.SIDEWAYS_MARKET:
                score += 0.3
            elif market_factors.market_regime == MarketRegime.BULL_MARKET and market_factors.trend_strength < 0.7:
                score += 0.2
            elif market_factors.market_regime == MarketRegime.BEAR_MARKET:
                score -= 0.2
                
        elif strategy == StrategyType.CREDIT_SPREAD:
            # Credit spreads work well in bullish to neutral markets with high volatility
            if market_factors.market_regime == MarketRegime.BULL_MARKET:
                score += 0.3
            elif market_factors.market_regime == MarketRegime.SIDEWAYS_MARKET:
                score += 0.2
            elif market_factors.market_regime == MarketRegime.BEAR_MARKET:
                score -= 0.3
                
        elif strategy == StrategyType.LEAPS:
            # LEAPS work best in strong bull markets with low volatility
            if market_factors.market_regime == MarketRegime.BULL_MARKET and market_factors.trend_strength > 0.6:
                score += 0.4
            elif market_factors.market_regime == MarketRegime.BEAR_MARKET:
                score -= 0.4
                
        elif strategy == StrategyType.PREMIUM_SELLING:
            # Premium selling works best in high volatility environments
            if market_factors.volatility_regime in [VolatilityRegime.HIGH_VOL, VolatilityRegime.EXTREME_VOL]:
                score += 0.3
            elif market_factors.volatility_regime == VolatilityRegime.LOW_VOL:
                score -= 0.2
        
        # Volatility regime adjustments
        if market_factors.volatility_factors > 0.7:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Premium selling strategies benefit from high volatility
            elif strategy == StrategyType.LEAPS:
                score -= 0.1  # LEAPS prefer lower volatility
        
        # Sentiment adjustments
        if market_factors.sentiment_level == SentimentLevel.EXTREME_FEAR:
            if strategy in [StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Good time to sell premium when fear is high
        elif market_factors.sentiment_level == SentimentLevel.EXTREME_GREED:
            if strategy == StrategyType.COVERED_CALL:
                score += 0.1  # Good time to sell calls when greed is high
        
        return min(max(score, 0.0), 1.0)
    
    def _score_stock_factors(self, strategy: StrategyType, symbol: str, 
                           stock_factors: StockSpecificFactors) -> float:
        """Score strategy based on stock-specific factors"""
        score = 0.5  # Base score
        
        # Technical confluence
        if stock_factors.technical_confluence_score > 0.7:
            if strategy in [StrategyType.LEAPS, StrategyType.CREDIT_SPREAD]:
                score += 0.2  # Strong technicals favor directional strategies
        elif stock_factors.technical_confluence_score < 0.3:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Weak technicals favor neutral strategies
        
        # IV Rank considerations
        if stock_factors.iv_rank > 0.7:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
                score += 0.2  # High IV favors premium selling
            elif strategy == StrategyType.LEAPS:
                score -= 0.1  # High IV makes LEAPS expensive
        elif stock_factors.iv_rank < 0.3:
            if strategy == StrategyType.LEAPS:
                score += 0.2  # Low IV makes LEAPS cheaper
            elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score -= 0.1  # Low IV reduces premium collection
        
        # Earnings proximity
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 14:
            if strategy == StrategyType.COVERED_CALL:
                score += 0.1  # CCs can benefit from earnings IV crush
            elif strategy == StrategyType.LEAPS:
                score -= 0.1  # LEAPS avoid short-term earnings volatility
        
        # News sentiment
        if stock_factors.news_sentiment_score > 0.3:
            if strategy in [StrategyType.LEAPS, StrategyType.CREDIT_SPREAD]:
                score += 0.1  # Positive news favors bullish strategies
        elif stock_factors.news_sentiment_score < -0.3:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Negative news favors neutral/defensive strategies
        
        # Unusual options activity
        if stock_factors.unusual_options_activity:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Unusual activity often means higher premiums
        
        return min(max(score, 0.0), 1.0)
    
    def _score_erica_criteria(self, strategy: StrategyType, symbol: str,
                            market_factors: MarketFactors, 
                            stock_factors: StockSpecificFactors) -> float:
        """Score based on Erica's specific criteria from her content"""
        
        # Get stock-specific rules
        if symbol not in STOCK_RULES:
            return 0.5
        
        rules = STOCK_RULES[symbol]
        score = 0.5
        
        if strategy == StrategyType.COVERED_CALL:
            # Erica's CC criteria: High IV, own the stock, 30-45 DTE, 0.30 delta
            if stock_factors.iv_rank > 0.6:
                score += 0.2
            if market_factors.volatility_factors > 0.6:
                score += 0.1
            # Stock-specific adjustments
            if symbol == "AAPL" and market_factors.market_regime == MarketRegime.BULL_MARKET:
                score += 0.1  # AAPL excellent for CCs in bull markets
            elif symbol == "NVDA":
                score -= 0.1  # NVDA has gap risk
                
        elif strategy == StrategyType.CREDIT_SPREAD:
            # Erica's CS criteria: Bullish outlook, high IV, 30-45 DTE, 0.15-0.20 delta
            if market_factors.bullish_factors > 0.6:
                score += 0.2
            if stock_factors.iv_rank > 0.6:
                score += 0.2
            if stock_factors.technical_confluence_score > 0.6:
                score += 0.1
                
        elif strategy == StrategyType.LEAPS:
            # Erica's LEAPS criteria: Strong bullish outlook, 12+ months, 0.70-0.80 delta
            if market_factors.market_regime == MarketRegime.BULL_MARKET and market_factors.trend_strength > 0.6:
                score += 0.3
            if stock_factors.relative_strength_vs_spy > 0.2:
                score += 0.1
            if stock_factors.iv_rank < 0.4:  # Prefer lower IV for buying options
                score += 0.1
                
        elif strategy == StrategyType.PREMIUM_SELLING:
            # Erica's premium selling criteria: High IV rank, systematic approach
            if market_factors.volatility_factors > 0.7:
                score += 0.3
            if stock_factors.iv_rank > 0.7:
                score += 0.2
            # Use Erica's specific factors
            if hasattr(stock_factors, 'wheel_suitability_score'):
                score += stock_factors.wheel_suitability_score * 0.2
        
        return min(max(score, 0.0), 1.0)
    
    def _score_risk_factors(self, strategy: StrategyType, market_factors: MarketFactors,
                          stock_factors: StockSpecificFactors) -> float:
        """Score based on risk management considerations"""
        score = 0.5
        
        # Market uncertainty penalty
        if market_factors.uncertainty_factors > 0.7:
            if strategy == StrategyType.LEAPS:
                score -= 0.2  # LEAPS most affected by uncertainty
            elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Premium selling benefits from uncertainty
        
        # Earnings risk
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 7:
            if strategy == StrategyType.LEAPS:
                score -= 0.1  # Avoid LEAPS right before earnings
            elif strategy == StrategyType.COVERED_CALL:
                score += 0.1  # CCs can benefit from earnings IV crush
        
        # Volatility risk
        if market_factors.volatility_regime == VolatilityRegime.EXTREME_VOL:
            if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                score += 0.1  # Premium selling benefits from extreme volatility
            elif strategy == StrategyType.LEAPS:
                score -= 0.1  # LEAPS expensive in extreme volatility
        
        return min(max(score, 0.0), 1.0)
    
    def _classify_confidence(self, score: float) -> StrategyConfidence:
        """Classify confidence level based on score"""
        if score >= 0.85:
            return StrategyConfidence.VERY_HIGH
        elif score >= 0.70:
            return StrategyConfidence.HIGH
        elif score >= 0.55:
            return StrategyConfidence.MODERATE
        elif score >= 0.40:
            return StrategyConfidence.LOW
        else:
            return StrategyConfidence.VERY_LOW
    
    def _get_recommended_dte(self, strategy: StrategyType, symbol: str,
                           market_factors: MarketFactors, 
                           stock_factors: StockSpecificFactors) -> Tuple[int, int]:
        """Get recommended DTE range for strategy"""
        
        if symbol in STOCK_RULES:
            rules = STOCK_RULES[symbol]
            if strategy == StrategyType.COVERED_CALL:
                return rules.cc_preferred_dte
        
        # Default DTE ranges by strategy
        dte_defaults = {
            StrategyType.COVERED_CALL: (21, 45),
            StrategyType.CREDIT_SPREAD: (30, 45),
            StrategyType.LEAPS: (365, 730),
            StrategyType.PREMIUM_SELLING: (21, 45)
        }
        
        base_dte = dte_defaults.get(strategy, (30, 45))
        
        # Adjust based on market conditions
        if market_factors.uncertainty_factors > 0.7:
            # Reduce DTE in uncertain markets
            return (max(7, base_dte[0] - 7), max(base_dte[0], base_dte[1] - 7))
        
        return base_dte
    
    def _get_recommended_delta(self, strategy: StrategyType, symbol: str,
                             market_factors: MarketFactors,
                             stock_factors: StockSpecificFactors) -> Tuple[float, float]:
        """Get recommended delta range for strategy"""
        
        if symbol in STOCK_RULES:
            rules = STOCK_RULES[symbol]
            if strategy == StrategyType.COVERED_CALL:
                return rules.cc_delta_range
        
        # Default delta ranges by strategy
        delta_defaults = {
            StrategyType.COVERED_CALL: (0.20, 0.30),
            StrategyType.CREDIT_SPREAD: (0.15, 0.25),
            StrategyType.LEAPS: (0.70, 0.80),
            StrategyType.PREMIUM_SELLING: (0.15, 0.30)
        }
        
        base_delta = delta_defaults.get(strategy, (0.20, 0.30))
        
        # Adjust based on volatility
        if market_factors.volatility_regime == VolatilityRegime.HIGH_VOL:
            # Use more conservative deltas in high volatility
            if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD]:
                return (base_delta[0] - 0.05, base_delta[1] - 0.05)
        
        return base_delta
    
    def _calculate_position_multiplier(self, strategy: StrategyType, 
                                     market_factors: MarketFactors,
                                     stock_factors: StockSpecificFactors) -> float:
        """Calculate position size multiplier based on conditions"""
        multiplier = 1.0
        
        # Reduce size in high uncertainty
        if market_factors.uncertainty_factors > 0.7:
            multiplier *= 0.8
        
        # Reduce size for high volatility stocks
        if stock_factors.iv_rank > 0.8:
            multiplier *= 0.9
        
        # Increase size for high confidence setups
        if market_factors.bullish_factors > 0.8 and strategy in [StrategyType.LEAPS, StrategyType.CREDIT_SPREAD]:
            multiplier *= 1.1
        
        return max(0.5, min(1.5, multiplier))
    
    def _identify_supporting_factors(self, strategy: StrategyType, symbol: str,
                                   market_factors: MarketFactors,
                                   stock_factors: StockSpecificFactors,
                                   score_data: Dict) -> List[str]:
        """Identify key factors supporting this strategy recommendation"""
        factors = []
        
        if score_data['market_score'] > 0.7:
            factors.append(f"Favorable market environment ({market_factors.market_regime.value})")
        
        if score_data['erica_score'] > 0.7:
            factors.append("Aligns with Erica's strategy criteria")
        
        if stock_factors.iv_rank > 0.7 and strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            factors.append(f"High IV rank ({stock_factors.iv_rank:.0%}) favors premium selling")
        
        if market_factors.volatility_factors > 0.7:
            factors.append("High volatility environment supports premium collection")
        
        if stock_factors.technical_confluence_score > 0.7:
            factors.append("Strong technical setup supports directional bias")
        
        return factors[:5]  # Top 5 factors
    
    def _identify_risk_factors(self, strategy: StrategyType, symbol: str,
                             market_factors: MarketFactors,
                             stock_factors: StockSpecificFactors,
                             score_data: Dict) -> List[str]:
        """Identify key risk factors for this strategy"""
        risks = []
        
        if market_factors.uncertainty_factors > 0.6:
            risks.append("Elevated market uncertainty")
        
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 14:
            risks.append(f"Earnings in {stock_factors.earnings_days_away} days")
        
        if symbol == "NVDA" and strategy != StrategyType.PREMIUM_SELLING:
            risks.append("NVDA gap risk - monitor closely")
        
        if market_factors.volatility_regime == VolatilityRegime.EXTREME_VOL:
            risks.append("Extreme volatility environment")
        
        if stock_factors.news_sentiment_score < -0.3:
            risks.append("Negative news sentiment")
        
        return risks[:3]  # Top 3 risks
    
    def _generate_invalidation_triggers(self, strategy: StrategyType,
                                      market_factors: MarketFactors,
                                      stock_factors: StockSpecificFactors) -> List[str]:
        """Generate conditions that would invalidate this recommendation"""
        triggers = []
        
        if strategy in [StrategyType.LEAPS, StrategyType.CREDIT_SPREAD]:
            triggers.append("Market regime shifts to bear market")
            triggers.append("VIX spikes above 30")
        
        if strategy == StrategyType.COVERED_CALL:
            triggers.append("Strong breakout above resistance")
            triggers.append("Unusual bullish options activity")
        
        triggers.append("Major negative news or events")
        
        return triggers
    
    def _generate_upgrade_triggers(self, strategy: StrategyType,
                                 market_factors: MarketFactors,
                                 stock_factors: StockSpecificFactors) -> List[str]:
        """Generate conditions that would increase confidence"""
        triggers = []
        
        if strategy == StrategyType.COVERED_CALL:
            triggers.append("IV rank increases above 70%")
            triggers.append("Market enters sideways consolidation")
        
        if strategy in [StrategyType.LEAPS, StrategyType.CREDIT_SPREAD]:
            triggers.append("Technical breakout confirmation")
            triggers.append("Positive earnings surprise")
        
        triggers.append("Sector rotation into favorable themes")
        
        return triggers
    
    def _initialize_decision_weights(self) -> Dict:
        """Initialize decision tree weights"""
        return {
            'market_environment': 0.30,
            'stock_factors': 0.25,
            'erica_criteria': 0.30,
            'risk_factors': 0.15
        }
    
    def _load_erica_criteria(self) -> Dict:
        """Load Erica's specific strategy criteria"""
        return {
            'covered_calls': {
                'min_iv_rank': 0.5,
                'preferred_dte': (30, 45),
                'target_delta': 0.30,
                'min_premium': 0.30
            },
            'credit_spreads': {
                'min_iv_rank': 0.6,
                'preferred_dte': (30, 45),
                'target_delta': 0.20,
                'bullish_bias_required': True
            },
            'leaps': {
                'min_timeframe_months': 12,
                'target_delta': 0.75,
                'strong_trend_required': True,
                'max_iv_rank': 0.6
            },
            'premium_selling': {
                'min_iv_rank': 0.7,
                'systematic_approach': True,
                'high_vol_preferred': True
            }
        }
