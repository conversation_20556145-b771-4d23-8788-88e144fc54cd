"""
AI-Powered Analysis Engine
Enhanced Natural Language Analysis using OpenAI API

This module provides AI-powered analysis capabilities for:
1. News sentiment analysis and market commentary interpretation
2. <PERSON><PERSON>ning<PERSON> call transcripts analysis
3. Pattern recognition for similar historical market conditions
4. Plain-English explanations of complex market conditions
5. Strategy rationale generation
6. Market narrative synthesis

Integration with OpenAI GPT models for sophisticated natural language understanding
and generation of actionable trading insights.
"""

import openai
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
import re
import time

from market_analysis_engine import MarketFactors, StockSpecificFactors
from intelligent_strategy_engine import StrategyOfTheDay

class AnalysisType(Enum):
    NEWS_SENTIMENT = "news_sentiment"
    EARNINGS_ANALYSIS = "earnings_analysis"
    MARKET_COMMENTARY = "market_commentary"
    PATTERN_RECOGNITION = "pattern_recognition"
    STRATEGY_EXPLANATION = "strategy_explanation"
    RISK_ASSESSMENT = "risk_assessment"

@dataclass
class AIAnalysisResult:
    """Result from AI analysis"""
    analysis_type: AnalysisType
    symbol: Optional[str]
    confidence_score: float  # 0-1
    key_insights: List[str]
    sentiment_score: float  # -1 to 1
    plain_english_summary: str
    actionable_recommendations: List[str]
    risk_factors: List[str]
    supporting_evidence: List[str]
    timestamp: datetime

@dataclass
class MarketNarrative:
    """Comprehensive market narrative generated by AI"""
    overall_market_sentiment: str
    key_themes: List[str]
    sector_rotation_narrative: str
    volatility_explanation: str
    trading_opportunities: List[str]
    risk_warnings: List[str]
    plain_english_summary: str
    confidence_level: str

class AIAnalysisEngine:
    """AI-powered analysis engine using OpenAI API"""
    
    def __init__(self, openai_api_key: str):
        """Initialize with OpenAI API key"""
        self.openai_api_key = openai_api_key
        openai.api_key = openai_api_key
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.last_api_call = 0
        self.min_call_interval = 1.0  # Minimum seconds between API calls
        
        # Cache for repeated analyses
        self.analysis_cache = {}
        self.cache_expiry = {}
        
    def analyze_news_sentiment(self, news_articles: List[Dict], symbol: str = None) -> AIAnalysisResult:
        """
        Analyze sentiment and extract insights from news articles
        
        Args:
            news_articles: List of news articles with title, content, source, date
            symbol: Optional stock symbol for focused analysis
            
        Returns:
            AIAnalysisResult with sentiment analysis and insights
        """
        if not news_articles:
            return self._create_empty_result(AnalysisType.NEWS_SENTIMENT, symbol)
        
        # Prepare news content for analysis
        news_content = self._prepare_news_content(news_articles, symbol)
        
        # Create analysis prompt
        prompt = self._create_news_analysis_prompt(news_content, symbol)
        
        # Get AI analysis
        ai_response = self._call_openai_api(prompt, max_tokens=800)
        
        # Parse and structure the response
        return self._parse_news_analysis_response(ai_response, symbol)
    
    def analyze_earnings_impact(self, earnings_data: Dict, symbol: str) -> AIAnalysisResult:
        """
        Analyze earnings data and predict market impact
        
        Args:
            earnings_data: Earnings data including transcripts, guidance, metrics
            symbol: Stock symbol
            
        Returns:
            AIAnalysisResult with earnings impact analysis
        """
        # Create earnings analysis prompt
        prompt = self._create_earnings_analysis_prompt(earnings_data, symbol)
        
        # Get AI analysis
        ai_response = self._call_openai_api(prompt, max_tokens=1000)
        
        # Parse and structure the response
        return self._parse_earnings_analysis_response(ai_response, symbol)
    
    def generate_strategy_explanation(self, strategy: StrategyOfTheDay, 
                                    market_factors: MarketFactors,
                                    stock_factors: StockSpecificFactors) -> str:
        """
        Generate plain-English explanation of strategy recommendation
        
        Args:
            strategy: Strategy recommendation
            market_factors: Current market factors
            stock_factors: Stock-specific factors
            
        Returns:
            Plain-English explanation of the strategy and reasoning
        """
        # Create strategy explanation prompt
        prompt = self._create_strategy_explanation_prompt(strategy, market_factors, stock_factors)
        
        # Get AI explanation
        ai_response = self._call_openai_api(prompt, max_tokens=600)
        
        return self._clean_ai_response(ai_response)
    
    def identify_market_patterns(self, current_conditions: MarketFactors,
                               historical_data: List[Dict]) -> AIAnalysisResult:
        """
        Identify similar historical market conditions and their outcomes
        
        Args:
            current_conditions: Current market factors
            historical_data: Historical market data and outcomes
            
        Returns:
            AIAnalysisResult with pattern recognition insights
        """
        # Create pattern recognition prompt
        prompt = self._create_pattern_recognition_prompt(current_conditions, historical_data)
        
        # Get AI analysis
        ai_response = self._call_openai_api(prompt, max_tokens=1000)
        
        # Parse and structure the response
        return self._parse_pattern_analysis_response(ai_response)
    
    def generate_market_narrative(self, market_factors: MarketFactors,
                                stock_analyses: List[StrategyOfTheDay],
                                news_sentiment: List[AIAnalysisResult]) -> MarketNarrative:
        """
        Generate comprehensive market narrative combining all analysis
        
        Args:
            market_factors: Current market factors
            stock_analyses: Strategy recommendations for all stocks
            news_sentiment: News sentiment analyses
            
        Returns:
            MarketNarrative with comprehensive market story
        """
        # Create market narrative prompt
        prompt = self._create_market_narrative_prompt(market_factors, stock_analyses, news_sentiment)
        
        # Get AI narrative
        ai_response = self._call_openai_api(prompt, max_tokens=1200)
        
        # Parse and structure the response
        return self._parse_market_narrative_response(ai_response)
    
    def _call_openai_api(self, prompt: str, max_tokens: int = 500, temperature: float = 0.3) -> str:
        """
        Make API call to OpenAI with rate limiting and error handling
        
        Args:
            prompt: The prompt to send to OpenAI
            max_tokens: Maximum tokens in response
            temperature: Creativity level (0-1)
            
        Returns:
            AI response text
        """
        # Rate limiting
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call
        if time_since_last_call < self.min_call_interval:
            time.sleep(self.min_call_interval - time_since_last_call)
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert financial analyst and trading strategist with deep knowledge of options trading, market analysis, and risk management. Provide clear, actionable insights."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9
            )
            
            self.last_api_call = time.time()
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"OpenAI API call failed: {e}")
            return f"AI analysis unavailable: {str(e)}"
    
    def _prepare_news_content(self, news_articles: List[Dict], symbol: str = None) -> str:
        """Prepare news content for analysis"""
        content_parts = []
        
        for article in news_articles[:10]:  # Limit to 10 most recent articles
            title = article.get('title', '')
            content = article.get('content', article.get('summary', ''))
            source = article.get('source', '')
            date = article.get('date', '')
            
            article_text = f"[{source} - {date}] {title}\n{content[:500]}..."
            content_parts.append(article_text)
        
        return "\n\n".join(content_parts)
    
    def _create_news_analysis_prompt(self, news_content: str, symbol: str = None) -> str:
        """Create prompt for news sentiment analysis"""
        symbol_focus = f" focusing on {symbol}" if symbol else ""
        
        return f"""
Analyze the following financial news articles{symbol_focus} and provide:

1. Overall sentiment score (-1 to 1, where -1 is very bearish, 0 is neutral, 1 is very bullish)
2. Key insights (3-5 bullet points)
3. Actionable recommendations for traders
4. Risk factors to monitor
5. Supporting evidence for your analysis
6. Plain English summary (2-3 sentences)

News Articles:
{news_content}

Format your response as JSON with the following structure:
{{
    "sentiment_score": <number>,
    "key_insights": [<list of strings>],
    "actionable_recommendations": [<list of strings>],
    "risk_factors": [<list of strings>],
    "supporting_evidence": [<list of strings>],
    "plain_english_summary": "<string>",
    "confidence_score": <number 0-1>
}}
"""

    def _create_earnings_analysis_prompt(self, earnings_data: Dict, symbol: str) -> str:
        """Create prompt for earnings analysis"""
        return f"""
Analyze the following earnings data for {symbol} and predict market impact:

Earnings Data:
- Revenue: {earnings_data.get('revenue', 'N/A')}
- EPS: {earnings_data.get('eps', 'N/A')}
- Guidance: {earnings_data.get('guidance', 'N/A')}
- Key Metrics: {earnings_data.get('key_metrics', 'N/A')}
- Management Commentary: {earnings_data.get('commentary', 'N/A')}

Provide:
1. Expected stock price impact (bullish/bearish/neutral)
2. Key factors driving the impact
3. Options trading implications
4. Risk factors
5. Time horizon for impact
6. Confidence level

Format as JSON with sentiment_score, key_insights, actionable_recommendations, risk_factors, supporting_evidence, plain_english_summary, and confidence_score.
"""

    def _create_strategy_explanation_prompt(self, strategy: StrategyOfTheDay,
                                          market_factors: MarketFactors,
                                          stock_factors: StockSpecificFactors) -> str:
        """Create prompt for strategy explanation"""
        return f"""
Explain the following trading strategy recommendation in plain English:

Strategy: {strategy.strategy_type}
Symbol: {strategy.symbol}
Confidence: {strategy.confidence}
Entry Price: {strategy.entry_price}
Target Price: {strategy.target_price}
Stop Loss: {strategy.stop_loss}

Market Conditions:
- VIX: {market_factors.vix_level}
- Market Regime: {market_factors.market_regime}
- Volatility Regime: {market_factors.volatility_regime}

Stock Factors:
- IV Rank: {stock_factors.iv_rank}
- Technical Confluence: {stock_factors.technical_confluence_score}
- News Sentiment: {stock_factors.news_sentiment_score}

Explain in 2-3 paragraphs why this strategy makes sense given current conditions, what the trader should expect, and what could go wrong. Use simple language that a beginner could understand.
"""

    def _create_pattern_recognition_prompt(self, current_conditions: MarketFactors,
                                         historical_data: List[Dict]) -> str:
        """Create prompt for pattern recognition"""
        return f"""
Analyze current market conditions and identify similar historical patterns:

Current Conditions:
- VIX: {current_conditions.vix_level}
- Market Regime: {current_conditions.market_regime}
- Volatility Regime: {current_conditions.volatility_regime}
- Bullish Factors: {current_conditions.bullish_factor_score}
- Bearish Factors: {current_conditions.bearish_factor_score}

Find similar historical periods and their outcomes. Provide:
1. Most similar historical periods (dates and conditions)
2. What happened next in those periods
3. Success rates of different strategies
4. Key differences from current situation
5. Actionable insights for current trading

Format as JSON with key_insights, actionable_recommendations, risk_factors, supporting_evidence, plain_english_summary, and confidence_score.
"""

    def _create_market_narrative_prompt(self, market_factors: MarketFactors,
                                      stock_analyses: List[StrategyOfTheDay],
                                      news_sentiment: List[AIAnalysisResult]) -> str:
        """Create prompt for market narrative"""
        strategies_summary = ", ".join([f"{s.symbol}: {s.strategy_type}" for s in stock_analyses[:5]])
        avg_sentiment = sum([n.sentiment_score for n in news_sentiment]) / len(news_sentiment) if news_sentiment else 0

        return f"""
Create a comprehensive market narrative based on current analysis:

Market Overview:
- VIX: {market_factors.vix_level}
- Market Regime: {market_factors.market_regime}
- Recommended Strategies: {strategies_summary}
- Average News Sentiment: {avg_sentiment:.2f}

Generate a market narrative including:
1. Overall market sentiment and key themes
2. Sector rotation story
3. Volatility environment explanation
4. Top trading opportunities
5. Key risk warnings
6. Plain English summary for traders

Format as JSON with: overall_market_sentiment, key_themes, sector_rotation_narrative, volatility_explanation, trading_opportunities, risk_warnings, plain_english_summary, confidence_level.
"""

    def _parse_news_analysis_response(self, ai_response: str, symbol: str) -> AIAnalysisResult:
        """Parse AI response for news analysis"""
        try:
            data = json.loads(ai_response)
            return AIAnalysisResult(
                analysis_type=AnalysisType.NEWS_SENTIMENT,
                symbol=symbol,
                confidence_score=data.get('confidence_score', 0.5),
                key_insights=data.get('key_insights', []),
                sentiment_score=data.get('sentiment_score', 0.0),
                plain_english_summary=data.get('plain_english_summary', ''),
                actionable_recommendations=data.get('actionable_recommendations', []),
                risk_factors=data.get('risk_factors', []),
                supporting_evidence=data.get('supporting_evidence', []),
                timestamp=datetime.now()
            )
        except json.JSONDecodeError:
            return self._create_fallback_result(ai_response, AnalysisType.NEWS_SENTIMENT, symbol)

    def _parse_earnings_analysis_response(self, ai_response: str, symbol: str) -> AIAnalysisResult:
        """Parse AI response for earnings analysis"""
        try:
            data = json.loads(ai_response)
            return AIAnalysisResult(
                analysis_type=AnalysisType.EARNINGS_ANALYSIS,
                symbol=symbol,
                confidence_score=data.get('confidence_score', 0.5),
                key_insights=data.get('key_insights', []),
                sentiment_score=data.get('sentiment_score', 0.0),
                plain_english_summary=data.get('plain_english_summary', ''),
                actionable_recommendations=data.get('actionable_recommendations', []),
                risk_factors=data.get('risk_factors', []),
                supporting_evidence=data.get('supporting_evidence', []),
                timestamp=datetime.now()
            )
        except json.JSONDecodeError:
            return self._create_fallback_result(ai_response, AnalysisType.EARNINGS_ANALYSIS, symbol)

    def _parse_pattern_analysis_response(self, ai_response: str) -> AIAnalysisResult:
        """Parse AI response for pattern analysis"""
        try:
            data = json.loads(ai_response)
            return AIAnalysisResult(
                analysis_type=AnalysisType.PATTERN_RECOGNITION,
                symbol=None,
                confidence_score=data.get('confidence_score', 0.5),
                key_insights=data.get('key_insights', []),
                sentiment_score=0.0,  # Not applicable for pattern recognition
                plain_english_summary=data.get('plain_english_summary', ''),
                actionable_recommendations=data.get('actionable_recommendations', []),
                risk_factors=data.get('risk_factors', []),
                supporting_evidence=data.get('supporting_evidence', []),
                timestamp=datetime.now()
            )
        except json.JSONDecodeError:
            return self._create_fallback_result(ai_response, AnalysisType.PATTERN_RECOGNITION, None)

    def _parse_market_narrative_response(self, ai_response: str) -> MarketNarrative:
        """Parse AI response for market narrative"""
        try:
            data = json.loads(ai_response)
            return MarketNarrative(
                overall_market_sentiment=data.get('overall_market_sentiment', 'Neutral'),
                key_themes=data.get('key_themes', []),
                sector_rotation_narrative=data.get('sector_rotation_narrative', ''),
                volatility_explanation=data.get('volatility_explanation', ''),
                trading_opportunities=data.get('trading_opportunities', []),
                risk_warnings=data.get('risk_warnings', []),
                plain_english_summary=data.get('plain_english_summary', ''),
                confidence_level=data.get('confidence_level', 'Medium')
            )
        except json.JSONDecodeError:
            return MarketNarrative(
                overall_market_sentiment="Analysis unavailable",
                key_themes=[],
                sector_rotation_narrative="",
                volatility_explanation="",
                trading_opportunities=[],
                risk_warnings=["AI analysis failed"],
                plain_english_summary=ai_response[:200] + "..." if len(ai_response) > 200 else ai_response,
                confidence_level="Low"
            )

    def _create_empty_result(self, analysis_type: AnalysisType, symbol: str) -> AIAnalysisResult:
        """Create empty result when no data available"""
        return AIAnalysisResult(
            analysis_type=analysis_type,
            symbol=symbol,
            confidence_score=0.0,
            key_insights=["No data available for analysis"],
            sentiment_score=0.0,
            plain_english_summary="No data available for analysis",
            actionable_recommendations=[],
            risk_factors=["Insufficient data"],
            supporting_evidence=[],
            timestamp=datetime.now()
        )

    def _create_fallback_result(self, ai_response: str, analysis_type: AnalysisType, symbol: str) -> AIAnalysisResult:
        """Create fallback result when JSON parsing fails"""
        return AIAnalysisResult(
            analysis_type=analysis_type,
            symbol=symbol,
            confidence_score=0.3,
            key_insights=[ai_response[:100] + "..." if len(ai_response) > 100 else ai_response],
            sentiment_score=0.0,
            plain_english_summary=ai_response[:200] + "..." if len(ai_response) > 200 else ai_response,
            actionable_recommendations=[],
            risk_factors=["Analysis format error"],
            supporting_evidence=[],
            timestamp=datetime.now()
        )

    def _clean_ai_response(self, response: str) -> str:
        """Clean and format AI response"""
        # Remove any JSON formatting if present
        response = re.sub(r'^```json\s*', '', response)
        response = re.sub(r'\s*```$', '', response)

        # Clean up extra whitespace
        response = re.sub(r'\n\s*\n', '\n\n', response)
        response = response.strip()

        return response
