"""
Quick Launcher for Desktop Application
Sets up the API key and launches the GUI
"""

import os
import tkinter as tk
from tkinter import messagebox, simpledialog

def setup_api_key():
    """Setup API key before launching the main app"""
    
    # Check if API key already exists
    api_key = None
    
    # Method 1: Check environment variable
    api_key = os.getenv("FMP_API_KEY")
    if api_key:
        print(f"Found API key in environment: {api_key[:10]}...")
        return api_key
    
    # Method 2: Check fmp.key file
    try:
        if os.path.exists("fmp.key"):
            with open("fmp.key", "r") as f:
                api_key = f.read().strip()
            if api_key:
                print(f"Found API key in fmp.key file: {api_key[:10]}...")
                return api_key
    except:
        pass
    
    # Method 3: Use the embedded key from your message
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    print(f"Using provided API key: {api_key[:10]}...")
    
    # Save it to environment for the session
    os.environ["FMP_API_KEY"] = api_key
    
    # Also save to fmp.key file for future use
    try:
        with open("fmp.key", "w") as f:
            f.write(api_key)
        print("API key saved to fmp.key file")
    except:
        print("Could not save API key to file")
    
    return api_key

def test_api_connection(api_key):
    """Test if the API key works"""
    try:
        from daily_outline import fmp_quote
        result = fmp_quote("AAPL", api_key)
        if result and 'price' in result:
            print(f"✅ API connection successful! AAPL price: ${result['price']}")
            return True
        else:
            print("❌ API connection failed - no data returned")
            return False
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return False

def main():
    """Main launcher function"""
    print("🚀 Launching AI-Powered Daily Stock Investment Planning System")
    print("=" * 60)
    
    # Setup API key
    print("Setting up API key...")
    api_key = setup_api_key()
    
    if not api_key:
        print("❌ No API key found!")
        print("Please:")
        print("1. Set environment variable: export FMP_API_KEY='your_key'")
        print("2. Or create fmp.key file with your key")
        print("3. Or get a free key from https://financialmodelingprep.com/")
        return
    
    # Test API connection
    print("Testing API connection...")
    if test_api_connection(api_key):
        print("✅ API connection verified!")
    else:
        print("⚠️  API connection failed, but launching app anyway...")
        print("You can test the connection in the app's Settings tab")
    
    print("\n🎯 Launching desktop application...")
    print("Features available:")
    print("• Real-time market data dashboard")
    print("• Erica's trading strategies analysis")
    print("• Daily investment recommendations")
    print("• Risk management tools")
    print("• Settings and configuration")
    
    # Launch the simplified minimal app by default
    try:
        try:
            from simple_desktop_app import MinimalApp as _Minimal
        except Exception:
            from minimal_gui import MinimalEricaGUI as _Minimal  # fallback
        app = _Minimal()
        # Pre-populate API key if app supports it via env only; ensure env is set
        os.environ["FMP_API_KEY"] = api_key
        print("✅ Minimal desktop application launched successfully!")
        print("This simplified interface focuses on the four core questions.")
        app.mainloop() if hasattr(app, 'mainloop') else app.run()
    except Exception as e:
        print(f"❌ Failed to launch minimal app: {e}")
        print("You can still use the command line version:")
        print(f"python daily_outline.py --fmp-key {api_key} --analysis-mode full")

if __name__ == "__main__":
    main()
