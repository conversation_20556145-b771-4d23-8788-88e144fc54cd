"""
Comprehensive Testing Suite for Erica's Trading System

Tests all components of the AI-powered daily stock investment planning system:
1. Technical analysis calculations
2. Strategy signal generation
3. Risk management calculations
4. Position sizing algorithms
5. Daily recommendation generation
6. Integration tests with mock data

Run with: python test_suite.py
"""

import unittest
import math
from datetime import datetime, timedelta
from typing import List, Dict

# Import modules to test
from daily_outline import (
    approx_atr14_from_daily, pct, pos_in_range, days_until,
    StrategyType, MarketCondition, TradingSignal, MarketAnalysis
)
from technical_analysis import TechnicalAnalyzer, TechnicalIndicators
from erica_strategies import EricaStrategies, OptionData, OptionType, StrategyParameters
from risk_management import RiskManager, RiskParameters, PositionRisk
from strategy_engine import StrategyAnalysisEngine
from daily_recommendations import DailyRecommendationGenerator

class TestTechnicalAnalysis(unittest.TestCase):
    """Test technical analysis calculations"""
    
    def setUp(self):
        self.analyzer = TechnicalAnalyzer()
        
        # Create sample price data
        self.sample_data = []
        base_price = 100.0
        for i in range(50):
            # Create trending data with some volatility
            trend_factor = 1 + (i * 0.002)  # 0.2% daily trend
            volatility = 2.0 * math.sin(i * 0.3)  # Some volatility
            
            price = base_price * trend_factor + volatility
            self.sample_data.append({
                'date': f'2024-01-{i+1:02d}',
                'open': price - 0.5,
                'high': price + 1.0,
                'low': price - 1.0,
                'close': price,
                'volume': 1000000 + i * 10000
            })
    
    def test_rsi_calculation(self):
        """Test RSI calculation"""
        rsi = self.analyzer._calculate_rsi(self.sample_data)
        self.assertIsNotNone(rsi)
        self.assertGreaterEqual(rsi, 0)
        self.assertLessEqual(rsi, 100)
    
    def test_moving_averages(self):
        """Test moving average calculations"""
        ma_20 = self.analyzer._calculate_sma(self.sample_data, 20)
        ma_50 = self.analyzer._calculate_sma(self.sample_data, 50)

        self.assertIsNotNone(ma_20)
        self.assertIsNotNone(ma_50)  # We have 50 data points, so MA_50 should work
        self.assertGreater(ma_20, 0)
        self.assertGreater(ma_50, 0)
    
    def test_atr_calculation(self):
        """Test ATR calculation"""
        atr = self.analyzer._calculate_atr(self.sample_data)
        self.assertIsNotNone(atr)
        self.assertGreater(atr, 0)
    
    def test_volatility_calculation(self):
        """Test historical volatility calculation"""
        vol = self.analyzer._calculate_historical_volatility(self.sample_data, 30)
        self.assertIsNotNone(vol)
        self.assertGreater(vol, 0)
        self.assertLess(vol, 2.0)  # Reasonable volatility range
    
    def test_full_analysis(self):
        """Test complete technical analysis"""
        indicators = self.analyzer.analyze("AAPL", self.sample_data, 105.0)
        
        self.assertIsInstance(indicators, TechnicalIndicators)
        self.assertIsNotNone(indicators.rsi)
        self.assertIsNotNone(indicators.ma_20)
        self.assertIn(indicators.rsi_signal, ["OVERSOLD", "OVERBOUGHT", "NEUTRAL"])
        self.assertIn(indicators.ma_signal, ["BULLISH", "BEARISH", "NEUTRAL"])

class TestEricaStrategies(unittest.TestCase):
    """Test Erica's trading strategies"""
    
    def setUp(self):
        self.strategies = EricaStrategies()
        
        # Create sample market analysis
        self.market_analysis = MarketAnalysis(
            symbol="AAPL",
            current_price=150.0,
            trend="UPTREND",
            volatility=0.25,
            rsi=65.0,
            ma_20=148.0,
            ma_50=145.0,
            support_level=145.0,
            resistance_level=155.0,
            market_condition=MarketCondition.BULLISH
        )
        
        # Create sample options chain
        self.options_chain = [
            OptionData(
                symbol="AAPL",
                strike=155.0,
                expiration="2024-03-15",
                option_type=OptionType.CALL,
                bid=2.50,
                ask=2.70,
                volume=1000,
                open_interest=5000,
                implied_volatility=0.25,
                delta=0.30
            ),
            OptionData(
                symbol="AAPL",
                strike=145.0,
                expiration="2024-03-15",
                option_type=OptionType.PUT,
                bid=1.80,
                ask=2.00,
                volume=800,
                open_interest=3000,
                implied_volatility=0.22,
                delta=-0.20
            )
        ]
    
    def test_covered_call_analysis(self):
        """Test covered call opportunity analysis"""
        signal = self.strategies.analyze_covered_call_opportunity(
            self.market_analysis, self.options_chain, 100000
        )
        
        if signal:  # May be None if no suitable opportunities
            self.assertIsInstance(signal, TradingSignal)
            self.assertEqual(signal.strategy, StrategyType.COVERED_CALL)
            self.assertIn(signal.action, ["BUY", "SELL", "HOLD"])
            self.assertGreaterEqual(signal.confidence, 0)
            self.assertLessEqual(signal.confidence, 1)
    
    def test_credit_spread_analysis(self):
        """Test credit spread opportunity analysis"""
        signal = self.strategies.analyze_credit_spread_opportunity(
            self.market_analysis, self.options_chain, 100000
        )
        
        if signal:
            self.assertIsInstance(signal, TradingSignal)
            self.assertEqual(signal.strategy, StrategyType.CREDIT_SPREAD)
            self.assertIn(signal.action, ["BUY", "SELL", "HOLD"])
    
    def test_leaps_analysis(self):
        """Test LEAPS opportunity analysis"""
        # Create long-term option for LEAPS test
        leaps_option = OptionData(
            symbol="AAPL",
            strike=150.0,
            expiration="2025-01-17",  # Long-term expiration
            option_type=OptionType.CALL,
            bid=15.0,
            ask=15.50,
            volume=100,
            open_interest=1000,
            implied_volatility=0.30,
            delta=0.75
        )
        
        signal = self.strategies.analyze_leaps_opportunity(
            self.market_analysis, [leaps_option], 100000
        )
        
        if signal:
            self.assertIsInstance(signal, TradingSignal)
            self.assertEqual(signal.strategy, StrategyType.LEAPS)

class TestRiskManagement(unittest.TestCase):
    """Test risk management calculations"""
    
    def setUp(self):
        self.risk_manager = RiskManager(100000)  # $100k account
        
        # Create sample trading signal
        self.sample_signal = TradingSignal(
            symbol="AAPL",
            strategy=StrategyType.COVERED_CALL,
            action="SELL",
            confidence=0.75,
            reasoning="Test signal",
            entry_price=2.50,
            target_price=1.25,
            risk_reward_ratio=2.0
        )
    
    def test_position_sizing(self):
        """Test position sizing calculation"""
        position_size, reasoning = self.risk_manager.calculate_position_size(
            self.sample_signal, 0.25  # 25% volatility
        )
        
        self.assertGreater(position_size, 0)
        self.assertLess(position_size, self.risk_manager.account_size * 0.2)  # Max 20%
        self.assertIsInstance(reasoning, str)
        self.assertGreater(len(reasoning), 10)
    
    def test_position_risk_assessment(self):
        """Test position risk assessment"""
        position_risk = self.risk_manager.assess_position_risk(
            self.sample_signal, 5000, 0.25
        )
        
        self.assertIsInstance(position_risk, PositionRisk)
        self.assertEqual(position_risk.symbol, "AAPL")
        self.assertEqual(position_risk.strategy, StrategyType.COVERED_CALL)
        self.assertGreater(position_risk.max_gain, 0)
        self.assertGreater(position_risk.max_loss, 0)
    
    def test_win_probability_estimation(self):
        """Test win probability estimation"""
        prob = self.risk_manager._estimate_win_probability(self.sample_signal)
        
        self.assertGreaterEqual(prob, 0.4)
        self.assertLessEqual(prob, 0.9)

class TestStrategyEngine(unittest.TestCase):
    """Test strategy analysis engine"""
    
    def setUp(self):
        self.engine = StrategyAnalysisEngine(100000)
        
        # Create sample market data
        self.market_data = {
            "quote": {
                "symbol": "AAPL",
                "price": 150.0,
                "change": 2.5,
                "changesPercentage": 1.67,
                "volume": 50000000
            }
        }
        
        # Create sample historical data
        self.historical_data = []
        for i in range(30):
            self.historical_data.append({
                'date': f'2024-01-{i+1:02d}',
                'open': 148.0 + i * 0.1,
                'high': 152.0 + i * 0.1,
                'low': 146.0 + i * 0.1,
                'close': 150.0 + i * 0.1,
                'volume': 45000000 + i * 100000
            })
    
    def test_market_analysis_creation(self):
        """Test market analysis creation"""
        analysis = self.engine._create_market_analysis(
            "AAPL", self.market_data, self.historical_data
        )
        
        self.assertIsInstance(analysis, MarketAnalysis)
        self.assertEqual(analysis.symbol, "AAPL")
        self.assertEqual(analysis.current_price, 150.0)
        self.assertGreater(analysis.volatility, 0)
    
    def test_symbol_analysis(self):
        """Test complete symbol analysis"""
        recommendation = self.engine.analyze_symbol(
            "AAPL", self.market_data, self.historical_data
        )
        
        self.assertEqual(recommendation.symbol, "AAPL")
        self.assertIsInstance(recommendation.technical_indicators, TechnicalIndicators)
        self.assertIsInstance(recommendation.confidence_score, float)
        self.assertGreaterEqual(recommendation.confidence_score, 0)
        self.assertLessEqual(recommendation.confidence_score, 1)

class TestDailyRecommendations(unittest.TestCase):
    """Test daily recommendation generation"""
    
    def setUp(self):
        self.generator = DailyRecommendationGenerator(100000, ["AAPL", "NVDA"])
        
        # Create sample data for multiple symbols
        self.market_data = {
            "AAPL": {
                "quote": {"symbol": "AAPL", "price": 150.0, "volume": 50000000}
            },
            "NVDA": {
                "quote": {"symbol": "NVDA", "price": 800.0, "volume": 30000000}
            }
        }
        
        self.historical_data = {
            "AAPL": [{"date": f"2024-01-{i:02d}", "open": 149+i*0.1, "high": 151+i*0.1, 
                     "low": 148+i*0.1, "close": 150+i*0.1, "volume": 45000000} for i in range(1, 21)],
            "NVDA": [{"date": f"2024-01-{i:02d}", "open": 799+i*0.5, "high": 801+i*0.5, 
                     "low": 798+i*0.5, "close": 800+i*0.5, "volume": 25000000} for i in range(1, 21)]
        }
    
    def test_market_environment_assessment(self):
        """Test market environment assessment"""
        environment = self.generator._assess_market_environment(
            self.market_data, self.historical_data
        )
        
        self.assertIn(environment.market_trend, ["BULL_MARKET", "BEAR_MARKET", "NEUTRAL_MARKET"])
        self.assertIn(environment.volatility_regime, ["HIGH_VOL", "LOW_VOL", "NORMAL_VOL"])
        self.assertIn(environment.risk_sentiment, ["RISK_ON", "RISK_OFF", "NEUTRAL"])
        self.assertIsInstance(environment.preferred_strategies, list)
    
    def test_daily_report_generation(self):
        """Test complete daily report generation"""
        report = self.generator.generate_daily_report(
            self.market_data, self.historical_data
        )
        
        self.assertIsNotNone(report.date)
        self.assertIsInstance(report.executive_summary, str)
        self.assertEqual(len(report.stock_recommendations), 2)  # AAPL and NVDA
        self.assertIsInstance(report.action_items, list)
        self.assertIsInstance(report.key_insights, list)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def test_end_to_end_analysis(self):
        """Test complete end-to-end analysis workflow"""
        # This test simulates the complete workflow from data input to recommendations
        
        # Create comprehensive test data
        symbols = ["AAPL", "NVDA"]
        market_data = {}
        historical_data = {}
        
        for symbol in symbols:
            market_data[symbol] = {
                "quote": {
                    "symbol": symbol,
                    "price": 150.0 if symbol == "AAPL" else 800.0,
                    "change": 2.5,
                    "changesPercentage": 1.67,
                    "volume": 50000000,
                    "dayLow": 148.0 if symbol == "AAPL" else 795.0,
                    "dayHigh": 152.0 if symbol == "AAPL" else 805.0,
                    "yearLow": 120.0 if symbol == "AAPL" else 600.0,
                    "yearHigh": 180.0 if symbol == "AAPL" else 900.0
                }
            }
            
            # Generate 60 days of historical data
            base_price = 150.0 if symbol == "AAPL" else 800.0
            historical_data[symbol] = []
            for i in range(60):
                price = base_price + i * 0.1 + math.sin(i * 0.1) * 2
                historical_data[symbol].append({
                    'date': f'2024-{(i//30)+1:02d}-{(i%30)+1:02d}',
                    'open': price - 0.5,
                    'high': price + 1.0,
                    'low': price - 1.0,
                    'close': price,
                    'volume': 45000000 + i * 100000
                })
        
        # Run complete analysis
        generator = DailyRecommendationGenerator(100000, symbols)
        report = generator.generate_daily_report(market_data, historical_data)
        
        # Verify report completeness
        self.assertEqual(len(report.stock_recommendations), 2)
        self.assertIsInstance(report.executive_summary, str)
        self.assertGreater(len(report.executive_summary), 50)
        
        # Verify each stock has analysis
        for rec in report.stock_recommendations:
            self.assertIn(rec.symbol, symbols)
            self.assertIsInstance(rec.technical_indicators, TechnicalIndicators)
            self.assertIsInstance(rec.confidence_score, float)
            self.assertGreaterEqual(rec.confidence_score, 0)
            self.assertLessEqual(rec.confidence_score, 1)
        
        # Verify action items are generated
        self.assertIsInstance(report.action_items, list)
        
        # Test report formatting
        formatted_report = generator.format_daily_report(report)
        self.assertIsInstance(formatted_report, str)
        self.assertGreater(len(formatted_report), 500)
        self.assertIn("DAILY INVESTMENT REPORT", formatted_report)

def run_all_tests():
    """Run all test suites"""
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestTechnicalAnalysis,
        TestEricaStrategies,
        TestRiskManagement,
        TestStrategyEngine,
        TestDailyRecommendations,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Error:')[-1].strip()}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
