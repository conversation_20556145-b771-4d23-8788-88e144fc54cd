# AI-Powered Daily Stock Investment Planning System

An intelligent daily investment advisor that applies **<PERSON>'s proven trading strategies** from [@AbundantlyErica](https://www.youtube.com/@AbundantlyErica) YouTube channel to analyze AAPL, NVDA, GOOGL, AMZN, and AMD.

## 🎯 Overview

This system transforms raw market data into actionable investment recommendations using <PERSON>'s systematic approach to options trading. It combines real-time market analysis, technical indicators, and proven strategies to generate daily investment plans with specific entry/exit signals and risk management guidance.

## 🚀 Key Features

### **<PERSON>'s Trading Strategies Implemented**
- **Covered Calls**: Generate income from stock holdings with systematic call selling
- **Credit Spreads**: Profit from time decay and limited price movement
- **LEAPS**: Long-term equity anticipation securities for leveraged growth
- **Premium Selling**: Systematic premium collection strategies

### **Advanced Analysis Components**
- **Technical Analysis**: RSI, Moving Averages (20/50/200), ATR, Volatility Analysis
- **Risk Management**: Position sizing, portfolio risk assessment, correlation analysis
- **Market Timing**: Volatility regime detection, trend analysis, market condition assessment
- **Daily Recommendations**: Structured action items with priority and timing

### **Real-Time Data Integration**
- Financial Modeling Prep (FMP) API for market data
- Live options chain analysis (when available)
- News sentiment integration
- Earnings calendar tracking

## 📋 Requirements

```bash
pip install requests python-dateutil
```

Optional for enhanced features:
```bash
pip install python-dotenv  # For .env file support
```

## 🔧 Installation & Setup

1. **Clone or download the system files**
2. **Get your FMP API key** from [Financial Modeling Prep](https://financialmodelingprep.com/)
3. **Set up your API key** (choose one method):
   ```bash
   # Method 1: Environment variable
   export FMP_API_KEY="your_api_key_here"
   
   # Method 2: Command line argument
   python daily_outline.py --fmp-key your_api_key_here
   
   # Method 3: .env file
   echo "FMP_API_KEY=your_api_key_here" > .env
   
   # Method 4: Key file
   echo "your_api_key_here" > fmp.key
   ```

## 🎮 Usage

### **Basic Market Analysis**
```bash
# Quick market overview
python daily_outline.py

# Specific symbols with timezone
python daily_outline.py --symbols AAPL,NVDA,GOOGL --tz America/New_York
```

### **Full Strategy Analysis** (Recommended)
```bash
# Complete daily investment report with Erica's strategies
python daily_outline.py --analysis-mode full --account-size 100000

# Custom account size and symbols
python daily_outline.py --analysis-mode full --account-size 250000 --symbols AAPL,NVDA,AMD
```

### **Testing & Validation**
```bash
# Run unit tests
python daily_outline.py --unit-test

# Run live API tests
python daily_outline.py --self-test

# Run comprehensive test suite
python test_suite.py
```

## 📊 Sample Output

### **Executive Summary**
```
DAILY INVESTMENT REPORT - 2024-01-15
====================================

EXECUTIVE SUMMARY
Market Environment: Bullish trend with moderate volatility characteristics
High-confidence opportunities in: AAPL, NVDA
Portfolio risk manageable at 8.5%
Focus on covered calls and credit spreads strategies

PRIORITY ACTION ITEMS
[HIGH] AAPL: SELL covered_call - Premium: $2.45, Delta: 0.28
[HIGH] NVDA: SELL credit_spread - Put credit spread: Max profit: $1.25
[MEDIUM] MARKET: Focus on premium selling strategies
```

### **Individual Stock Analysis**
```
AAPL - Confidence: 75%
  Market Outlook: Bullish trend with positive technical setup
  Primary Signal: SELL covered_call
  Reasoning: Covered call on 155.0 strike, 2024-03-15 exp. Premium: $2.45, Delta: 0.28
  Risk: MODERATE RISK - Elevated volatility
```

## 🧠 Erica's Strategy Implementation

### **1. Covered Calls**
- **Rules**: Own 100 shares, sell calls 30-45 DTE, target 0.30 delta
- **Income Focus**: Minimum $30 premium per contract
- **Management**: Close at 50% profit or 21 DTE

### **2. Credit Spreads**
- **Setup**: Put credit spreads in bullish/neutral markets
- **Risk Management**: Sell put at 0.15-0.20 delta, buy protection 5-10 strikes lower
- **Timing**: 30-45 DTE, close at 50% profit

### **3. LEAPS**
- **Strategy**: Buy calls with 12+ months expiration, 0.70-0.80 delta
- **Use Case**: Stock replacement with leverage on strong growth stocks
- **Holding Period**: 6-12 months typically

### **4. Premium Selling**
- **Approach**: Systematic premium collection based on high IV rank
- **Delta Targets**: 15-30 delta options
- **Management**: 50% profit target or 21 DTE

## ⚖️ Risk Management

### **Position Sizing Rules**
- **Maximum Risk**: 2% of account per trade
- **Portfolio Limit**: 20% of account at risk
- **Strategy Limits**: 
  - Covered Calls: 15% max
  - Credit Spreads: 8% max
  - LEAPS: 5% max
  - Premium Selling: 10% max

### **Volatility Adjustments**
- **High Volatility**: Reduce position size by 30%
- **Low Volatility**: Can increase size by 20%
- **Dynamic Sizing**: Based on Kelly Criterion with safety factors

## 📁 File Structure

```
├── daily_outline.py           # Main application with enhanced features
├── erica_strategies.py        # Erica's trading strategy implementations
├── technical_analysis.py     # Technical indicators and analysis
├── strategy_engine.py        # Core analysis engine
├── risk_management.py        # Position sizing and risk assessment
├── daily_recommendations.py  # Daily report generation
├── test_suite.py             # Comprehensive testing suite
└── README.md                 # This documentation
```

## 🔍 Technical Indicators

### **Trend Analysis**
- **Moving Averages**: 20, 50, 200-day SMAs
- **Trend Strength**: 5-level classification system
- **Momentum**: Recent price action analysis

### **Volatility Metrics**
- **ATR(14)**: Average True Range for volatility measurement
- **Historical Volatility**: 30-day annualized volatility
- **Volatility Rank**: Current vs 1-year range (0-100%)

### **Oscillators**
- **RSI(14)**: Relative Strength Index with overbought/oversold signals
- **Support/Resistance**: Dynamic level calculation
- **Market Condition**: Bullish/Bearish/Neutral/High Vol/Low Vol

## 🎯 Strategy Selection Logic

The system automatically selects optimal strategies based on:

1. **Market Environment**
   - Bull Market → LEAPS, Credit Spreads
   - Bear Market → Covered Calls, Defensive strategies
   - Neutral Market → Premium Selling, Income strategies

2. **Volatility Regime**
   - High Volatility → Premium Selling strategies
   - Low Volatility → Directional strategies (LEAPS)
   - Normal Volatility → Balanced approach

3. **Technical Setup**
   - Overbought → Covered Calls
   - Oversold → Cash-Secured Puts, LEAPS
   - Trending → Directional strategies

## 📈 Performance Tracking

The system provides:
- **Confidence Scores**: 0-100% for each recommendation
- **Risk/Reward Ratios**: Expected return vs maximum loss
- **Win Probability**: Based on historical strategy performance
- **Position Sizing**: Optimal allocation based on Kelly Criterion

## 🛠️ Customization

### **Account Settings**
```python
# Modify risk parameters
risk_params = RiskParameters(
    max_account_risk=0.015,      # 1.5% max risk per trade
    max_portfolio_risk=0.15,     # 15% max portfolio risk
    volatility_adjustment=True    # Enable volatility-based sizing
)
```

### **Strategy Parameters**
```python
# Customize strategy settings
strategy_params = StrategyParameters(
    min_premium=0.25,            # Minimum $0.25 premium
    max_dte=60,                  # Maximum 60 days to expiration
    target_delta=0.25            # Target 0.25 delta
)
```

## 🚨 Important Notes

### **Educational Purpose**
This system is for educational and research purposes. All trading involves risk, and past performance doesn't guarantee future results.

### **API Limitations**
- FMP API has rate limits (300 requests/minute for free tier)
- Options data may require premium FMP subscription
- Real-time data has slight delays

### **Risk Disclaimer**
- Never risk more than you can afford to lose
- Always verify signals with your own analysis
- Consider paper trading before live implementation
- Consult with financial advisors for personalized advice

## 🤝 Contributing

This system implements Erica's publicly shared strategies from her YouTube channel. For improvements or bug fixes:

1. Test thoroughly with the included test suite
2. Maintain compatibility with existing interfaces
3. Document any new features or changes
4. Follow Erica's risk management principles

## 📞 Support

For questions about:
- **Erica's Strategies**: Visit [@AbundantlyErica](https://www.youtube.com/@AbundantlyErica) YouTube channel
- **Technical Issues**: Check the test suite and error logs
- **API Problems**: Verify your FMP API key and rate limits

## 🚀 Quick Start Guide

### **1. Get Started in 5 Minutes**
```bash
# 1. Download the system
git clone <repository> # or download files

# 2. Install dependencies
pip install requests python-dateutil

# 3. Get FMP API key (free at financialmodelingprep.com)

# 4. Run basic analysis
python daily_outline.py --fmp-key YOUR_API_KEY

# 5. Run full strategy analysis
python daily_outline.py --analysis-mode full --fmp-key YOUR_API_KEY --account-size 100000
```

### **2. Daily Workflow**
1. **Morning**: Run full analysis to get daily recommendations
2. **Review**: Check priority action items and risk assessment
3. **Execute**: Implement high-confidence signals based on your risk tolerance
4. **Monitor**: Track positions and adjust based on system updates
5. **Evening**: Review performance and prepare for next day

### **3. Example Daily Routine**
```bash
# Morning market analysis (before market open)
python daily_outline.py --analysis-mode full --account-size 100000 > daily_report.txt

# Quick check during market hours
python daily_outline.py --symbols AAPL,NVDA --analysis-mode basic

# End of day review
python daily_outline.py --analysis-mode full --account-size 100000
```

## 📚 Learning Resources

### **Erica's Content**
- **YouTube Channel**: [@AbundantlyErica](https://www.youtube.com/@AbundantlyErica)
- **Website**: [abundantly-erica.mykajabi.com](https://abundantly-erica.mykajabi.com/)
- **Courses**: Millionaire Mentorship Program, Options Kickstarter

### **Strategy Deep Dives**
- **Covered Calls**: Income generation from stock holdings
- **Credit Spreads**: High-probability income strategies
- **LEAPS**: Leveraged long-term growth plays
- **Premium Selling**: Systematic volatility harvesting

## 🔧 Troubleshooting

### **Common Issues**

**"Missing FMP API key"**
```bash
# Solution: Set your API key
export FMP_API_KEY="your_key_here"
# or use --fmp-key argument
```

**"No strategy analysis available"**
```bash
# Solution: Use full analysis mode
python daily_outline.py --analysis-mode full
```

**"HTTP 429 errors"**
```bash
# Solution: You're hitting rate limits
# Wait a minute or upgrade your FMP plan
```

**"Module not found errors"**
```bash
# Solution: Ensure all files are in the same directory
# Check that you have all required files
```

### **Performance Tips**
- Use `--analysis-mode basic` for quick checks
- Cache historical data to reduce API calls
- Run full analysis once per day, basic checks throughout day
- Monitor your FMP API usage to avoid rate limits

## 🎓 Advanced Usage

### **Custom Strategy Parameters**
Create a `config.py` file:
```python
from erica_strategies import StrategyParameters
from risk_management import RiskParameters

# Custom strategy settings
STRATEGY_PARAMS = StrategyParameters(
    min_premium=0.35,        # Higher minimum premium
    max_dte=30,              # Shorter expiration preference
    target_delta=0.25        # More conservative delta
)

# Custom risk settings
RISK_PARAMS = RiskParameters(
    max_account_risk=0.015,  # More conservative 1.5%
    max_portfolio_risk=0.15, # Lower portfolio risk
    cash_reserve=0.25        # Keep 25% cash
)
```

### **Backtesting Framework**
```python
# Example backtesting setup
from daily_recommendations import DailyRecommendationGenerator
import pandas as pd

def backtest_strategy(start_date, end_date, symbols, initial_capital):
    """
    Backtest the strategy over a date range
    """
    # Implementation would go here
    # This is a framework for future development
    pass
```

### **Integration with Trading Platforms**
The system outputs structured data that can be integrated with:
- **Paper Trading**: ThinkorSwim, Interactive Brokers
- **Live Trading**: Alpaca, TD Ameritrade APIs
- **Portfolio Tracking**: Personal Capital, Mint
- **Spreadsheets**: Export to Excel/Google Sheets

---

**Built with respect for Erica's systematic approach to options trading and commitment to helping traders achieve consistent results through disciplined strategy execution.**
