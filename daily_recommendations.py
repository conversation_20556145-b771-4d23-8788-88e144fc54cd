"""
Daily Recommendation Generator for Erica's Trading System

Generates comprehensive daily investment recommendations including:
1. Market analysis and outlook
2. Specific trading signals with entry/exit points
3. Risk assessment and position sizing
4. Action items and priority rankings
5. Portfolio management guidance
6. Market timing recommendations

Output Format:
- Executive Summary
- Individual Stock Analysis
- Trading Recommendations
- Risk Management Report
- Daily Action Plan
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

from daily_outline import TradingSignal, StrategyType, MarketCondition
from strategy_engine import StrategyAnalysisEngine, DailyRecommendation, PortfolioPosition
from risk_management import RiskManager, PortfolioRisk, PositionRisk
from technical_analysis import TechnicalAnalyzer

@dataclass
class MarketEnvironment:
    """Overall market environment assessment"""
    market_trend: str  # BULL_MARKET, BEAR_MARKET, NEUTRAL_MARKET
    volatility_regime: str  # HIGH_VOL, LOW_VOL, NORMAL_VOL
    risk_sentiment: str  # RISK_ON, RISK_OFF, NEUTRAL
    preferred_strategies: List[StrategyType]
    market_outlook: str
    key_levels: Dict[str, float]  # Support/resistance for major indices

@dataclass
class DailyActionItem:
    """Individual action item with priority and timing"""
    symbol: str
    action: str
    priority: str  # HIGH, MEDIUM, LOW
    timing: str  # IMMEDIATE, TODAY, THIS_WEEK
    reasoning: str
    expected_outcome: str

@dataclass
class DailyReport:
    """Complete daily investment report"""
    date: datetime
    market_environment: MarketEnvironment
    executive_summary: str
    stock_recommendations: List[DailyRecommendation]
    portfolio_risk: PortfolioRisk
    action_items: List[DailyActionItem]
    key_insights: List[str]
    tomorrow_watchlist: List[str]

class DailyRecommendationGenerator:
    """Generates comprehensive daily investment recommendations"""
    
    def __init__(self, account_size: float = 100000, symbols: List[str] = None):
        self.account_size = account_size
        self.symbols = symbols or ["AAPL", "NVDA", "GOOGL", "AMZN", "AMD"]
        
        self.strategy_engine = StrategyAnalysisEngine(account_size)
        self.risk_manager = RiskManager(account_size)
        self.technical_analyzer = TechnicalAnalyzer()
        self.logger = logging.getLogger(__name__)
    
    def generate_daily_report(self, market_data: Dict[str, dict],
                            historical_data: Dict[str, List[dict]],
                            options_data: Dict[str, List[dict]] = None,
                            existing_positions: List[PortfolioPosition] = None) -> DailyReport:
        """
        Generate comprehensive daily investment report
        
        Args:
            market_data: Current market data for each symbol
            historical_data: Historical price data for each symbol
            options_data: Options chain data (optional)
            existing_positions: Current portfolio positions
            
        Returns:
            Complete DailyReport with all recommendations and analysis
        """
        
        report_date = datetime.now()
        existing_positions = existing_positions or []
        options_data = options_data or {}
        
        # Analyze market environment
        market_environment = self._assess_market_environment(market_data, historical_data)
        
        # Generate recommendations for each symbol
        stock_recommendations = []
        for symbol in self.symbols:
            if symbol in market_data and symbol in historical_data:
                recommendation = self.strategy_engine.analyze_symbol(
                    symbol,
                    market_data[symbol],
                    historical_data[symbol],
                    options_data.get(symbol)
                )
                stock_recommendations.append(recommendation)
        
        # Assess portfolio risk
        portfolio_risk = self.risk_manager.assess_portfolio_risk(
            existing_positions, stock_recommendations
        )
        
        # Generate action items
        action_items = self._generate_action_items(
            stock_recommendations, portfolio_risk, market_environment
        )
        
        # Create executive summary
        executive_summary = self._create_executive_summary(
            market_environment, stock_recommendations, portfolio_risk
        )
        
        # Generate key insights
        key_insights = self._generate_key_insights(
            stock_recommendations, market_environment, portfolio_risk
        )
        
        # Create tomorrow's watchlist
        tomorrow_watchlist = self._create_watchlist(stock_recommendations)
        
        return DailyReport(
            date=report_date,
            market_environment=market_environment,
            executive_summary=executive_summary,
            stock_recommendations=stock_recommendations,
            portfolio_risk=portfolio_risk,
            action_items=action_items,
            key_insights=key_insights,
            tomorrow_watchlist=tomorrow_watchlist
        )
    
    def _assess_market_environment(self, market_data: Dict[str, dict],
                                 historical_data: Dict[str, List[dict]]) -> MarketEnvironment:
        """Assess overall market environment"""
        
        # Analyze trend across all symbols
        bullish_count = 0
        bearish_count = 0
        high_vol_count = 0
        
        for symbol in self.symbols:
            if symbol in market_data and symbol in historical_data:
                # Quick technical analysis
                indicators = self.technical_analyzer.analyze(
                    symbol, historical_data[symbol], 
                    float(market_data[symbol].get('quote', {}).get('price', 0))
                )
                
                if indicators.trend_strength in ["STRONG_UPTREND", "UPTREND"]:
                    bullish_count += 1
                elif indicators.trend_strength in ["STRONG_DOWNTREND", "DOWNTREND"]:
                    bearish_count += 1
                
                if indicators.volatility_rank and indicators.volatility_rank > 0.7:
                    high_vol_count += 1
        
        # Determine market trend
        if bullish_count >= 3:
            market_trend = "BULL_MARKET"
        elif bearish_count >= 3:
            market_trend = "BEAR_MARKET"
        else:
            market_trend = "NEUTRAL_MARKET"
        
        # Determine volatility regime
        if high_vol_count >= 3:
            volatility_regime = "HIGH_VOL"
        elif high_vol_count <= 1:
            volatility_regime = "LOW_VOL"
        else:
            volatility_regime = "NORMAL_VOL"
        
        # Determine risk sentiment
        if market_trend == "BULL_MARKET" and volatility_regime != "HIGH_VOL":
            risk_sentiment = "RISK_ON"
        elif market_trend == "BEAR_MARKET" or volatility_regime == "HIGH_VOL":
            risk_sentiment = "RISK_OFF"
        else:
            risk_sentiment = "NEUTRAL"
        
        # Preferred strategies based on environment
        preferred_strategies = self._get_preferred_strategies(market_trend, volatility_regime)
        
        # Market outlook
        outlook_components = [market_trend.replace("_", " ").lower()]
        if volatility_regime != "NORMAL_VOL":
            outlook_components.append(volatility_regime.replace("_", " ").lower())
        
        market_outlook = f"Market showing {' with '.join(outlook_components)} characteristics"
        
        return MarketEnvironment(
            market_trend=market_trend,
            volatility_regime=volatility_regime,
            risk_sentiment=risk_sentiment,
            preferred_strategies=preferred_strategies,
            market_outlook=market_outlook,
            key_levels={}  # Would need index data for this
        )
    
    def _get_preferred_strategies(self, market_trend: str, volatility_regime: str) -> List[StrategyType]:
        """Determine preferred strategies based on market environment"""
        
        strategies = []
        
        if volatility_regime == "HIGH_VOL":
            # High volatility favors premium selling
            strategies.extend([StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, 
                             StrategyType.PREMIUM_SELLING])
        
        if market_trend == "BULL_MARKET":
            # Bull market favors directional strategies
            strategies.extend([StrategyType.LEAPS, StrategyType.CREDIT_SPREAD])
        
        if market_trend == "NEUTRAL_MARKET":
            # Neutral market favors income strategies
            strategies.extend([StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING])
        
        if volatility_regime == "LOW_VOL":
            # Low volatility favors directional strategies
            strategies.append(StrategyType.LEAPS)
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(strategies))
    
    def _generate_action_items(self, recommendations: List[DailyRecommendation],
                              portfolio_risk: PortfolioRisk,
                              market_environment: MarketEnvironment) -> List[DailyActionItem]:
        """Generate prioritized action items"""
        
        action_items = []
        
        # High priority trading signals
        for rec in recommendations:
            if rec.primary_signal and rec.primary_signal.action in ["BUY", "SELL"]:
                if rec.confidence_score >= 0.7:
                    priority = "HIGH"
                    timing = "TODAY"
                elif rec.confidence_score >= 0.6:
                    priority = "MEDIUM"
                    timing = "THIS_WEEK"
                else:
                    priority = "LOW"
                    timing = "THIS_WEEK"
                
                action_items.append(DailyActionItem(
                    symbol=rec.symbol,
                    action=f"{rec.primary_signal.action} {rec.primary_signal.strategy.value}",
                    priority=priority,
                    timing=timing,
                    reasoning=rec.primary_signal.reasoning,
                    expected_outcome=f"Target: ${rec.primary_signal.target_price:.2f}" if rec.primary_signal.target_price else "Income generation"
                ))
        
        # Risk management actions
        for risk_rec in portfolio_risk.recommendations:
            action_items.append(DailyActionItem(
                symbol="PORTFOLIO",
                action=risk_rec,
                priority="HIGH",
                timing="IMMEDIATE",
                reasoning="Risk management requirement",
                expected_outcome="Reduced portfolio risk"
            ))
        
        # Market environment actions
        if market_environment.volatility_regime == "HIGH_VOL":
            action_items.append(DailyActionItem(
                symbol="MARKET",
                action="Focus on premium selling strategies",
                priority="MEDIUM",
                timing="TODAY",
                reasoning="High volatility environment favors premium collection",
                expected_outcome="Enhanced income from elevated premiums"
            ))
        
        # Sort by priority
        priority_order = {"HIGH": 0, "MEDIUM": 1, "LOW": 2}
        action_items.sort(key=lambda x: priority_order.get(x.priority, 3))
        
        return action_items
    
    def _create_executive_summary(self, market_environment: MarketEnvironment,
                                 recommendations: List[DailyRecommendation],
                                 portfolio_risk: PortfolioRisk) -> str:
        """Create executive summary of daily analysis"""
        
        summary_parts = []
        
        # Market overview
        summary_parts.append(f"Market Environment: {market_environment.market_outlook}")
        
        # Key opportunities
        high_confidence_signals = [
            rec for rec in recommendations 
            if rec.primary_signal and rec.confidence_score >= 0.7
        ]
        
        if high_confidence_signals:
            symbols = [rec.symbol for rec in high_confidence_signals]
            summary_parts.append(f"High-confidence opportunities in: {', '.join(symbols)}")
        
        # Risk status
        if portfolio_risk.risk_percentage > 0.15:
            summary_parts.append(f"Portfolio risk elevated at {portfolio_risk.risk_percentage:.1%}")
        else:
            summary_parts.append(f"Portfolio risk manageable at {portfolio_risk.risk_percentage:.1%}")
        
        # Strategy focus
        if market_environment.preferred_strategies:
            strategy_names = [s.value.replace('_', ' ') for s in market_environment.preferred_strategies[:2]]
            summary_parts.append(f"Focus on {' and '.join(strategy_names)} strategies")
        
        return ". ".join(summary_parts) + "."
    
    def _generate_key_insights(self, recommendations: List[DailyRecommendation],
                              market_environment: MarketEnvironment,
                              portfolio_risk: PortfolioRisk) -> List[str]:
        """Generate key insights from analysis"""
        
        insights = []
        
        # Technical insights
        oversold_symbols = [
            rec.symbol for rec in recommendations
            if rec.technical_indicators.rsi_signal == "OVERSOLD"
        ]
        if oversold_symbols:
            insights.append(f"Oversold conditions in {', '.join(oversold_symbols)} - potential bounce candidates")
        
        overbought_symbols = [
            rec.symbol for rec in recommendations
            if rec.technical_indicators.rsi_signal == "OVERBOUGHT"
        ]
        if overbought_symbols:
            insights.append(f"Overbought conditions in {', '.join(overbought_symbols)} - consider covered calls")
        
        # Volatility insights
        high_vol_symbols = [
            rec.symbol for rec in recommendations
            if rec.technical_indicators.volatility_rank and rec.technical_indicators.volatility_rank > 0.8
        ]
        if high_vol_symbols:
            insights.append(f"Elevated volatility in {', '.join(high_vol_symbols)} - premium selling opportunities")
        
        # Strategy insights
        if market_environment.volatility_regime == "HIGH_VOL":
            insights.append("High volatility environment favors systematic premium selling approaches")
        
        if market_environment.market_trend == "BULL_MARKET":
            insights.append("Bull market conditions support LEAPS and bullish credit spreads")
        
        # Risk insights
        if portfolio_risk.diversification_score < 0.6:
            insights.append("Portfolio concentration risk - consider diversifying across strategies")
        
        return insights
    
    def _create_watchlist(self, recommendations: List[DailyRecommendation]) -> List[str]:
        """Create tomorrow's watchlist based on analysis"""
        
        watchlist = []
        
        # Symbols with pending signals
        for rec in recommendations:
            if rec.primary_signal and rec.primary_signal.action == "HOLD" and rec.confidence_score >= 0.6:
                watchlist.append(f"{rec.symbol} - Monitor for {rec.primary_signal.strategy.value} setup")
        
        # Symbols near key levels
        for rec in recommendations:
            if rec.technical_indicators.support_level or rec.technical_indicators.resistance_level:
                current_price = rec.current_analysis.current_price
                
                if rec.technical_indicators.support_level:
                    distance_to_support = (current_price - rec.technical_indicators.support_level) / current_price
                    if 0 < distance_to_support < 0.05:  # Within 5% of support
                        watchlist.append(f"{rec.symbol} - Near support at ${rec.technical_indicators.support_level:.2f}")
                
                if rec.technical_indicators.resistance_level:
                    distance_to_resistance = (rec.technical_indicators.resistance_level - current_price) / current_price
                    if 0 < distance_to_resistance < 0.05:  # Within 5% of resistance
                        watchlist.append(f"{rec.symbol} - Near resistance at ${rec.technical_indicators.resistance_level:.2f}")
        
        return watchlist
    
    def format_daily_report(self, report: DailyReport) -> str:
        """Format daily report for display"""
        
        lines = []
        lines.append("=" * 60)
        lines.append(f"DAILY INVESTMENT REPORT - {report.date.strftime('%Y-%m-%d')}")
        lines.append("=" * 60)
        lines.append("")
        
        # Executive Summary
        lines.append("EXECUTIVE SUMMARY")
        lines.append("-" * 20)
        lines.append(report.executive_summary)
        lines.append("")
        
        # Market Environment
        lines.append("MARKET ENVIRONMENT")
        lines.append("-" * 20)
        lines.append(f"Trend: {report.market_environment.market_trend}")
        lines.append(f"Volatility: {report.market_environment.volatility_regime}")
        lines.append(f"Risk Sentiment: {report.market_environment.risk_sentiment}")
        lines.append(f"Preferred Strategies: {', '.join([s.value for s in report.market_environment.preferred_strategies])}")
        lines.append("")
        
        # Action Items
        lines.append("PRIORITY ACTION ITEMS")
        lines.append("-" * 25)
        for item in report.action_items[:5]:  # Top 5 items
            lines.append(f"[{item.priority}] {item.symbol}: {item.action}")
            lines.append(f"    Timing: {item.timing} | {item.reasoning}")
        lines.append("")
        
        # Stock Recommendations
        lines.append("STOCK ANALYSIS & RECOMMENDATIONS")
        lines.append("-" * 35)
        for rec in report.stock_recommendations:
            lines.append(f"{rec.symbol} - Confidence: {rec.confidence_score:.0%}")
            lines.append(f"  Market Outlook: {rec.market_outlook}")
            if rec.primary_signal:
                lines.append(f"  Primary Signal: {rec.primary_signal.action} {rec.primary_signal.strategy.value}")
                lines.append(f"  Reasoning: {rec.primary_signal.reasoning}")
            lines.append(f"  Risk: {rec.risk_assessment}")
            lines.append("")
        
        # Key Insights
        if report.key_insights:
            lines.append("KEY INSIGHTS")
            lines.append("-" * 15)
            for insight in report.key_insights:
                lines.append(f"• {insight}")
            lines.append("")
        
        # Tomorrow's Watchlist
        if report.tomorrow_watchlist:
            lines.append("TOMORROW'S WATCHLIST")
            lines.append("-" * 22)
            for item in report.tomorrow_watchlist:
                lines.append(f"• {item}")
        
        lines.append("")
        lines.append("=" * 60)
        
        return "\n".join(lines)
