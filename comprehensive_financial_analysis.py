"""
Comprehensive Financial Analysis Engine
Analyst-style reviews with fundamental analysis, technical outlook, and earnings impact

This module provides comprehensive financial analysis capabilities including:
1. Fundamental analysis with P/E ratios, growth rates, and financial health
2. Technical outlook with support/resistance levels and trend analysis
3. Earnings impact assessment and guidance analysis
4. Price predictions with confidence intervals and timeframes
5. Sector analysis and relative strength comparisons
6. Integration with AI-powered insights and market sentiment

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import statistics

from ai_analysis_engine import AIAnalysisEngine, AIAnalysisResult
from market_analysis_engine import MarketFactors, StockSpecificFactors
from intelligent_strategy_engine import StrategyOfTheDay

class AnalystRating(Enum):
    STRONG_BUY = "Strong Buy"
    BUY = "Buy"
    HOLD = "Hold"
    SELL = "Sell"
    STRONG_SELL = "Strong Sell"

class TechnicalTrend(Enum):
    STRONG_BULLISH = "Strong Bullish"
    BULLISH = "Bullish"
    NEUTRAL = "Neutral"
    BEARISH = "Bearish"
    STRONG_BEARISH = "Strong Bearish"

@dataclass
class FundamentalMetrics:
    """Fundamental analysis metrics"""
    symbol: str
    pe_ratio: float
    forward_pe: float
    peg_ratio: float
    price_to_book: float
    price_to_sales: float
    debt_to_equity: float
    current_ratio: float
    roe: float  # Return on Equity
    roa: float  # Return on Assets
    gross_margin: float
    operating_margin: float
    net_margin: float
    revenue_growth_yoy: float
    earnings_growth_yoy: float
    free_cash_flow: float
    dividend_yield: float
    payout_ratio: float

@dataclass
class TechnicalAnalysis:
    """Technical analysis results"""
    symbol: str
    current_price: float
    trend_direction: TechnicalTrend
    trend_strength: float  # 0-1
    
    # Moving averages
    sma_20: float
    sma_50: float
    sma_200: float
    ema_12: float
    ema_26: float
    
    # Technical indicators
    rsi: float
    macd: float
    macd_signal: float
    bollinger_upper: float
    bollinger_lower: float
    bollinger_position: float  # 0-1 (position within bands)
    
    # Support and resistance
    support_levels: List[float]
    resistance_levels: List[float]
    key_support: float
    key_resistance: float
    
    # Volume analysis
    volume_trend: str
    volume_confirmation: bool
    relative_volume: float

@dataclass
class EarningsAnalysis:
    """Earnings and guidance analysis"""
    symbol: str
    next_earnings_date: Optional[datetime]
    days_to_earnings: int
    
    # Historical performance
    earnings_surprise_history: List[float]  # Last 4 quarters
    revenue_surprise_history: List[float]
    beat_rate: float  # Percentage of beats
    
    # Current quarter estimates
    eps_estimate: float
    eps_estimate_range: Tuple[float, float]
    revenue_estimate: float
    revenue_estimate_range: Tuple[float, float]
    
    # Guidance and outlook
    management_guidance: str
    analyst_revisions_trend: str  # UP, DOWN, STABLE
    guidance_confidence: float  # 0-1
    
    # Impact assessment
    earnings_volatility_expected: float
    post_earnings_move_estimate: float
    options_activity_unusual: bool

@dataclass
class SectorAnalysis:
    """Sector and relative strength analysis"""
    symbol: str
    sector: str
    industry: str
    
    # Relative performance
    sector_performance_1m: float
    sector_performance_3m: float
    sector_performance_ytd: float
    relative_strength_vs_sector: float
    relative_strength_vs_market: float
    
    # Sector trends
    sector_rotation_score: float  # How favored is this sector
    sector_momentum: str  # ACCELERATING, STABLE, DECELERATING
    peer_comparison_rank: int  # Rank within sector (1 = best)
    total_peers: int
    
    # Institutional activity
    institutional_ownership: float
    insider_ownership: float
    recent_institutional_changes: str

@dataclass
class PricePrediction:
    """Price prediction with confidence intervals"""
    symbol: str
    current_price: float
    timeframe_days: int
    
    # Price targets
    target_price: float
    bull_case_target: float
    bear_case_target: float
    
    # Confidence intervals
    confidence_level: float  # e.g., 0.68 for 1 standard deviation
    price_range_low: float
    price_range_high: float
    
    # Probability distributions
    probability_up_10pct: float
    probability_up_20pct: float
    probability_down_10pct: float
    probability_down_20pct: float
    
    # Risk metrics
    volatility_estimate: float
    max_drawdown_estimate: float
    sharpe_ratio_estimate: float

@dataclass
class ComprehensiveAnalysis:
    """Complete comprehensive financial analysis"""
    symbol: str
    analysis_date: datetime
    
    # Core analysis components
    fundamental_metrics: FundamentalMetrics
    technical_analysis: TechnicalAnalysis
    earnings_analysis: EarningsAnalysis
    sector_analysis: SectorAnalysis
    price_prediction: PricePrediction
    
    # AI-powered insights
    ai_sentiment_analysis: Optional[AIAnalysisResult]
    ai_fundamental_review: str
    ai_technical_summary: str
    ai_risk_assessment: str
    
    # Overall assessment
    analyst_rating: AnalystRating
    confidence_score: float  # 0-1
    investment_thesis: str
    key_catalysts: List[str]
    key_risks: List[str]
    
    # Strategy alignment
    strategy_fit_score: float  # How well current strategies align
    recommended_strategies: List[str]
    strategy_timing_score: float

class ComprehensiveFinancialAnalyzer:
    """Comprehensive financial analysis engine"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.ai_analyzer = AIAnalysisEngine(api_key) if api_key else None
        self.logger = logging.getLogger(__name__)
        
        # Analysis cache
        self.analysis_cache: Dict[str, ComprehensiveAnalysis] = {}
        self.cache_expiry: Dict[str, datetime] = {}
        self.cache_duration = timedelta(hours=1)  # Cache for 1 hour
    
    def analyze_stock(self, symbol: str, market_factors: MarketFactors = None,
                     stock_factors: StockSpecificFactors = None) -> ComprehensiveAnalysis:
        """
        Generate comprehensive financial analysis for a stock
        
        Args:
            symbol: Stock symbol to analyze
            market_factors: Current market environment factors
            stock_factors: Stock-specific factors
            
        Returns:
            Complete comprehensive analysis
        """
        
        # Check cache first
        if self._is_cached_and_valid(symbol):
            return self.analysis_cache[symbol]
        
        try:
            # Generate all analysis components
            fundamental_metrics = self._analyze_fundamentals(symbol)
            technical_analysis = self._analyze_technicals(symbol, stock_factors)
            earnings_analysis = self._analyze_earnings(symbol, stock_factors)
            sector_analysis = self._analyze_sector(symbol)
            price_prediction = self._generate_price_prediction(symbol, fundamental_metrics, technical_analysis)
            
            # AI-powered insights
            ai_sentiment = None
            ai_fundamental_review = ""
            ai_technical_summary = ""
            ai_risk_assessment = ""
            
            if self.ai_analyzer:
                ai_sentiment = self._get_ai_sentiment_analysis(symbol)
                ai_fundamental_review = self._get_ai_fundamental_review(symbol, fundamental_metrics)
                ai_technical_summary = self._get_ai_technical_summary(symbol, technical_analysis)
                ai_risk_assessment = self._get_ai_risk_assessment(symbol, market_factors)
            
            # Overall assessment
            analyst_rating = self._determine_analyst_rating(fundamental_metrics, technical_analysis, price_prediction)
            confidence_score = self._calculate_confidence_score(fundamental_metrics, technical_analysis, earnings_analysis)
            investment_thesis = self._generate_investment_thesis(symbol, fundamental_metrics, technical_analysis, sector_analysis)
            key_catalysts = self._identify_key_catalysts(symbol, earnings_analysis, sector_analysis)
            key_risks = self._identify_key_risks(symbol, fundamental_metrics, technical_analysis, market_factors)
            
            # Strategy alignment
            strategy_fit_score = self._calculate_strategy_fit(symbol, technical_analysis, earnings_analysis)
            recommended_strategies = self._recommend_strategies(symbol, fundamental_metrics, technical_analysis, market_factors)
            strategy_timing_score = self._calculate_strategy_timing(symbol, technical_analysis, earnings_analysis)
            
            # Create comprehensive analysis
            analysis = ComprehensiveAnalysis(
                symbol=symbol,
                analysis_date=datetime.now(),
                fundamental_metrics=fundamental_metrics,
                technical_analysis=technical_analysis,
                earnings_analysis=earnings_analysis,
                sector_analysis=sector_analysis,
                price_prediction=price_prediction,
                ai_sentiment_analysis=ai_sentiment,
                ai_fundamental_review=ai_fundamental_review,
                ai_technical_summary=ai_technical_summary,
                ai_risk_assessment=ai_risk_assessment,
                analyst_rating=analyst_rating,
                confidence_score=confidence_score,
                investment_thesis=investment_thesis,
                key_catalysts=key_catalysts,
                key_risks=key_risks,
                strategy_fit_score=strategy_fit_score,
                recommended_strategies=recommended_strategies,
                strategy_timing_score=strategy_timing_score
            )
            
            # Cache the analysis
            self.analysis_cache[symbol] = analysis
            self.cache_expiry[symbol] = datetime.now() + self.cache_duration
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return self._create_fallback_analysis(symbol)
    
    def generate_analyst_report(self, analysis: ComprehensiveAnalysis) -> str:
        """
        Generate a formatted analyst-style report
        
        Args:
            analysis: Comprehensive analysis results
            
        Returns:
            Formatted analyst report
        """
        
        report = f"""
COMPREHENSIVE FINANCIAL ANALYSIS REPORT
{analysis.symbol} - {analysis.analysis_date.strftime('%B %d, %Y')}
{'='*60}

INVESTMENT RATING: {analysis.analyst_rating.value}
CONFIDENCE SCORE: {analysis.confidence_score:.0%}
CURRENT PRICE: ${analysis.technical_analysis.current_price:.2f}
PRICE TARGET: ${analysis.price_prediction.target_price:.2f}

INVESTMENT THESIS
{'-'*20}
{analysis.investment_thesis}

FUNDAMENTAL ANALYSIS
{'-'*20}
P/E Ratio: {analysis.fundamental_metrics.pe_ratio:.1f}
PEG Ratio: {analysis.fundamental_metrics.peg_ratio:.2f}
Revenue Growth (YoY): {analysis.fundamental_metrics.revenue_growth_yoy:.1%}
Earnings Growth (YoY): {analysis.fundamental_metrics.earnings_growth_yoy:.1%}
ROE: {analysis.fundamental_metrics.roe:.1%}
Debt/Equity: {analysis.fundamental_metrics.debt_to_equity:.2f}

{analysis.ai_fundamental_review}

TECHNICAL ANALYSIS
{'-'*20}
Trend: {analysis.technical_analysis.trend_direction.value}
Trend Strength: {analysis.technical_analysis.trend_strength:.0%}
RSI: {analysis.technical_analysis.rsi:.1f}
Key Support: ${analysis.technical_analysis.key_support:.2f}
Key Resistance: ${analysis.technical_analysis.key_resistance:.2f}

{analysis.ai_technical_summary}

EARNINGS OUTLOOK
{'-'*20}
Next Earnings: {analysis.earnings_analysis.next_earnings_date.strftime('%B %d, %Y') if analysis.earnings_analysis.next_earnings_date else 'TBD'}
Days to Earnings: {analysis.earnings_analysis.days_to_earnings}
Beat Rate: {analysis.earnings_analysis.beat_rate:.0%}
EPS Estimate: ${analysis.earnings_analysis.eps_estimate:.2f}
Expected Volatility: {analysis.earnings_analysis.earnings_volatility_expected:.1%}

SECTOR ANALYSIS
{'-'*20}
Sector: {analysis.sector_analysis.sector}
Sector Performance (YTD): {analysis.sector_analysis.sector_performance_ytd:.1%}
Relative Strength vs Sector: {analysis.sector_analysis.relative_strength_vs_sector:.1%}
Peer Rank: {analysis.sector_analysis.peer_comparison_rank} of {analysis.sector_analysis.total_peers}

PRICE PREDICTION ({analysis.price_prediction.timeframe_days} days)
{'-'*20}
Target Price: ${analysis.price_prediction.target_price:.2f}
Bull Case: ${analysis.price_prediction.bull_case_target:.2f}
Bear Case: ${analysis.price_prediction.bear_case_target:.2f}
Confidence Range: ${analysis.price_prediction.price_range_low:.2f} - ${analysis.price_prediction.price_range_high:.2f}

KEY CATALYSTS
{'-'*20}
"""
        
        for catalyst in analysis.key_catalysts:
            report += f"• {catalyst}\n"
        
        report += f"""
KEY RISKS
{'-'*20}
"""
        
        for risk in analysis.key_risks:
            report += f"• {risk}\n"
        
        report += f"""
STRATEGY RECOMMENDATIONS
{'-'*20}
Strategy Fit Score: {analysis.strategy_fit_score:.0%}
Strategy Timing Score: {analysis.strategy_timing_score:.0%}

Recommended Strategies:
"""
        
        for strategy in analysis.recommended_strategies:
            report += f"• {strategy}\n"
        
        if analysis.ai_risk_assessment:
            report += f"""
AI RISK ASSESSMENT
{'-'*20}
{analysis.ai_risk_assessment}
"""
        
        report += f"""
{'-'*60}
Analysis generated by Comprehensive Financial Analysis Engine
August 18, 2025
"""
        
        return report

    # Implementation methods

    def _is_cached_and_valid(self, symbol: str) -> bool:
        """Check if analysis is cached and still valid"""
        if symbol not in self.analysis_cache:
            return False

        if symbol not in self.cache_expiry:
            return False

        return datetime.now() < self.cache_expiry[symbol]

    def _analyze_fundamentals(self, symbol: str) -> FundamentalMetrics:
        """Analyze fundamental metrics (mock implementation)"""
        # In production, would fetch real financial data from APIs

        # Mock fundamental data based on symbol
        mock_data = {
            "AAPL": {
                "pe_ratio": 28.5, "forward_pe": 25.2, "peg_ratio": 1.8,
                "price_to_book": 8.2, "price_to_sales": 7.1, "debt_to_equity": 1.73,
                "current_ratio": 1.07, "roe": 0.365, "roa": 0.223,
                "gross_margin": 0.381, "operating_margin": 0.297, "net_margin": 0.253,
                "revenue_growth_yoy": 0.081, "earnings_growth_yoy": 0.135,
                "free_cash_flow": 99.584e9, "dividend_yield": 0.0044, "payout_ratio": 0.15
            },
            "NVDA": {
                "pe_ratio": 65.8, "forward_pe": 45.3, "peg_ratio": 1.2,
                "price_to_book": 12.8, "price_to_sales": 22.4, "debt_to_equity": 0.42,
                "current_ratio": 3.92, "roe": 0.485, "roa": 0.298,
                "gross_margin": 0.732, "operating_margin": 0.321, "net_margin": 0.289,
                "revenue_growth_yoy": 1.262, "earnings_growth_yoy": 2.814,
                "free_cash_flow": 26.946e9, "dividend_yield": 0.0029, "payout_ratio": 0.08
            }
        }

        # Use AAPL data as default for unknown symbols
        data = mock_data.get(symbol, mock_data["AAPL"])

        return FundamentalMetrics(
            symbol=symbol,
            pe_ratio=data["pe_ratio"],
            forward_pe=data["forward_pe"],
            peg_ratio=data["peg_ratio"],
            price_to_book=data["price_to_book"],
            price_to_sales=data["price_to_sales"],
            debt_to_equity=data["debt_to_equity"],
            current_ratio=data["current_ratio"],
            roe=data["roe"],
            roa=data["roa"],
            gross_margin=data["gross_margin"],
            operating_margin=data["operating_margin"],
            net_margin=data["net_margin"],
            revenue_growth_yoy=data["revenue_growth_yoy"],
            earnings_growth_yoy=data["earnings_growth_yoy"],
            free_cash_flow=data["free_cash_flow"],
            dividend_yield=data["dividend_yield"],
            payout_ratio=data["payout_ratio"]
        )

    def _analyze_technicals(self, symbol: str, stock_factors: StockSpecificFactors = None) -> TechnicalAnalysis:
        """Analyze technical indicators"""

        # Mock current price and technical data
        current_price = 155.0  # Would fetch real price

        # Use stock_factors if available, otherwise mock data
        if stock_factors:
            rsi = stock_factors.rsi
            bollinger_position = stock_factors.bollinger_position
        else:
            rsi = 58.5
            bollinger_position = 0.7

        # Calculate moving averages (mock)
        sma_20 = current_price * 0.98
        sma_50 = current_price * 0.95
        sma_200 = current_price * 0.88

        # Determine trend
        if current_price > sma_20 > sma_50 > sma_200:
            trend_direction = TechnicalTrend.STRONG_BULLISH
            trend_strength = 0.9
        elif current_price > sma_20 > sma_50:
            trend_direction = TechnicalTrend.BULLISH
            trend_strength = 0.7
        elif current_price > sma_50:
            trend_direction = TechnicalTrend.NEUTRAL
            trend_strength = 0.5
        else:
            trend_direction = TechnicalTrend.BEARISH
            trend_strength = 0.3

        return TechnicalAnalysis(
            symbol=symbol,
            current_price=current_price,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            sma_20=sma_20,
            sma_50=sma_50,
            sma_200=sma_200,
            ema_12=current_price * 0.99,
            ema_26=current_price * 0.97,
            rsi=rsi,
            macd=2.5,
            macd_signal=2.1,
            bollinger_upper=current_price * 1.05,
            bollinger_lower=current_price * 0.95,
            bollinger_position=bollinger_position,
            support_levels=[145.0, 140.0, 135.0],
            resistance_levels=[165.0, 170.0, 175.0],
            key_support=145.0,
            key_resistance=165.0,
            volume_trend="Increasing",
            volume_confirmation=True,
            relative_volume=1.25
        )

    def _analyze_earnings(self, symbol: str, stock_factors: StockSpecificFactors = None) -> EarningsAnalysis:
        """Analyze earnings and guidance"""

        # Use stock_factors if available
        if stock_factors:
            days_to_earnings = stock_factors.earnings_days_away
            next_earnings_date = datetime.now() + timedelta(days=days_to_earnings) if days_to_earnings > 0 else None
        else:
            days_to_earnings = 35
            next_earnings_date = datetime.now() + timedelta(days=35)

        return EarningsAnalysis(
            symbol=symbol,
            next_earnings_date=next_earnings_date,
            days_to_earnings=days_to_earnings,
            earnings_surprise_history=[0.05, 0.12, -0.02, 0.08],  # Last 4 quarters
            revenue_surprise_history=[0.03, 0.07, 0.01, 0.05],
            beat_rate=0.75,  # 75% beat rate
            eps_estimate=6.50,
            eps_estimate_range=(6.25, 6.75),
            revenue_estimate=125.5e9,
            revenue_estimate_range=(123.0e9, 128.0e9),
            management_guidance="Positive outlook with continued growth expected",
            analyst_revisions_trend="UP",
            guidance_confidence=0.8,
            earnings_volatility_expected=0.08,  # 8% expected move
            post_earnings_move_estimate=0.06,  # 6% average move
            options_activity_unusual=False
        )

    def _analyze_sector(self, symbol: str) -> SectorAnalysis:
        """Analyze sector and relative performance"""

        # Mock sector data
        sector_map = {
            "AAPL": ("Technology", "Consumer Electronics"),
            "NVDA": ("Technology", "Semiconductors"),
            "AMD": ("Technology", "Semiconductors"),
            "GOOGL": ("Technology", "Internet Services"),
            "AMZN": ("Consumer Discretionary", "E-commerce")
        }

        sector, industry = sector_map.get(symbol, ("Technology", "Software"))

        return SectorAnalysis(
            symbol=symbol,
            sector=sector,
            industry=industry,
            sector_performance_1m=0.045,
            sector_performance_3m=0.128,
            sector_performance_ytd=0.234,
            relative_strength_vs_sector=1.15,  # 15% outperformance
            relative_strength_vs_market=1.22,  # 22% outperformance
            sector_rotation_score=0.75,
            sector_momentum="ACCELERATING",
            peer_comparison_rank=3,
            total_peers=25,
            institutional_ownership=0.68,
            insider_ownership=0.02,
            recent_institutional_changes="NET_BUYING"
        )

    def _generate_price_prediction(self, symbol: str, fundamentals: FundamentalMetrics,
                                 technicals: TechnicalAnalysis) -> PricePrediction:
        """Generate price prediction with confidence intervals"""

        current_price = technicals.current_price
        timeframe_days = 45  # 45-day prediction

        # Calculate target based on fundamentals and technicals
        fundamental_multiplier = 1.0 + (fundamentals.earnings_growth_yoy * 0.5)  # Growth impact
        technical_multiplier = 1.0 + (technicals.trend_strength - 0.5) * 0.2  # Trend impact

        target_price = current_price * fundamental_multiplier * technical_multiplier

        # Bull and bear cases
        bull_case_target = target_price * 1.25
        bear_case_target = target_price * 0.80

        # Confidence intervals (simplified)
        volatility = 0.25  # 25% annual volatility assumption
        daily_vol = volatility / (252 ** 0.5)
        period_vol = daily_vol * (timeframe_days ** 0.5)

        confidence_level = 0.68  # 1 standard deviation
        price_range_low = current_price * (1 - period_vol)
        price_range_high = current_price * (1 + period_vol)

        return PricePrediction(
            symbol=symbol,
            current_price=current_price,
            timeframe_days=timeframe_days,
            target_price=target_price,
            bull_case_target=bull_case_target,
            bear_case_target=bear_case_target,
            confidence_level=confidence_level,
            price_range_low=price_range_low,
            price_range_high=price_range_high,
            probability_up_10pct=0.35,
            probability_up_20pct=0.20,
            probability_down_10pct=0.25,
            probability_down_20pct=0.10,
            volatility_estimate=volatility,
            max_drawdown_estimate=0.15,
            sharpe_ratio_estimate=1.2
        )

    def _get_ai_sentiment_analysis(self, symbol: str) -> Optional[AIAnalysisResult]:
        """Get AI-powered sentiment analysis"""
        if not self.ai_analyzer:
            return None

        try:
            # Mock news articles for AI analysis
            mock_articles = [
                {"title": f"{symbol} Reports Strong Quarterly Results", "content": "Beats expectations", "source": "Reuters"},
                {"title": f"{symbol} Announces Strategic Initiative", "content": "Growth prospects", "source": "Bloomberg"}
            ]

            return self.ai_analyzer.analyze_news_sentiment(mock_articles, symbol)
        except Exception as e:
            self.logger.error(f"AI sentiment analysis failed for {symbol}: {e}")
            return None

    def _get_ai_fundamental_review(self, symbol: str, fundamentals: FundamentalMetrics) -> str:
        """Get AI-powered fundamental analysis review"""
        if not self.ai_analyzer:
            return f"{symbol} shows solid fundamental metrics with P/E of {fundamentals.pe_ratio:.1f} and growth rate of {fundamentals.earnings_growth_yoy:.1%}."

        try:
            # Create prompt for fundamental analysis
            prompt = f"""Analyze the fundamental metrics for {symbol}:
            P/E Ratio: {fundamentals.pe_ratio}
            Revenue Growth: {fundamentals.revenue_growth_yoy:.1%}
            Earnings Growth: {fundamentals.earnings_growth_yoy:.1%}
            ROE: {fundamentals.roe:.1%}
            Debt/Equity: {fundamentals.debt_to_equity}

            Provide a 2-3 sentence fundamental analysis summary."""

            return self.ai_analyzer._call_openai_api(prompt, max_tokens=200)
        except Exception as e:
            return f"Fundamental analysis shows {symbol} with reasonable valuation metrics and growth prospects."

    def _get_ai_technical_summary(self, symbol: str, technicals: TechnicalAnalysis) -> str:
        """Get AI-powered technical analysis summary"""
        if not self.ai_analyzer:
            return f"{symbol} shows {technicals.trend_direction.value.lower()} trend with RSI at {technicals.rsi:.1f}."

        try:
            prompt = f"""Analyze the technical setup for {symbol}:
            Current Price: ${technicals.current_price}
            Trend: {technicals.trend_direction.value}
            RSI: {technicals.rsi}
            Support: ${technicals.key_support}
            Resistance: ${technicals.key_resistance}

            Provide a 2-3 sentence technical analysis summary."""

            return self.ai_analyzer._call_openai_api(prompt, max_tokens=200)
        except Exception as e:
            return f"Technical analysis shows {symbol} in {technicals.trend_direction.value.lower()} trend with key levels identified."

    def _get_ai_risk_assessment(self, symbol: str, market_factors: MarketFactors = None) -> str:
        """Get AI-powered risk assessment"""
        if not self.ai_analyzer or not market_factors:
            return f"Standard market risks apply to {symbol} including volatility and sector-specific factors."

        try:
            prompt = f"""Assess the risks for {symbol} given current market conditions:
            VIX Level: {market_factors.vix_level}
            Market Regime: {market_factors.market_regime.value}
            Uncertainty Score: {market_factors.uncertainty_factor_score:.1%}

            Identify key risks and provide risk mitigation suggestions."""

            return self.ai_analyzer._call_openai_api(prompt, max_tokens=300)
        except Exception as e:
            return f"Risk assessment indicates standard market volatility and sector-specific risks for {symbol}."

    def _determine_analyst_rating(self, fundamentals: FundamentalMetrics,
                                technicals: TechnicalAnalysis,
                                prediction: PricePrediction) -> AnalystRating:
        """Determine overall analyst rating"""

        # Score based on multiple factors
        score = 0

        # Fundamental scoring
        if fundamentals.pe_ratio < 20:
            score += 2
        elif fundamentals.pe_ratio < 30:
            score += 1

        if fundamentals.earnings_growth_yoy > 0.15:
            score += 2
        elif fundamentals.earnings_growth_yoy > 0.05:
            score += 1

        # Technical scoring
        if technicals.trend_direction in [TechnicalTrend.STRONG_BULLISH, TechnicalTrend.BULLISH]:
            score += 2
        elif technicals.trend_direction == TechnicalTrend.NEUTRAL:
            score += 1

        # Price prediction scoring
        upside = (prediction.target_price - prediction.current_price) / prediction.current_price
        if upside > 0.20:
            score += 2
        elif upside > 0.10:
            score += 1

        # Convert score to rating
        if score >= 6:
            return AnalystRating.STRONG_BUY
        elif score >= 4:
            return AnalystRating.BUY
        elif score >= 2:
            return AnalystRating.HOLD
        elif score >= 0:
            return AnalystRating.SELL
        else:
            return AnalystRating.STRONG_SELL

    def _calculate_confidence_score(self, fundamentals: FundamentalMetrics,
                                  technicals: TechnicalAnalysis,
                                  earnings: EarningsAnalysis) -> float:
        """Calculate overall confidence score"""

        scores = []

        # Fundamental confidence
        if fundamentals.peg_ratio < 2.0 and fundamentals.debt_to_equity < 2.0:
            scores.append(0.8)
        else:
            scores.append(0.6)

        # Technical confidence
        scores.append(technicals.trend_strength)

        # Earnings confidence
        scores.append(earnings.guidance_confidence)

        return statistics.mean(scores)

    def _generate_investment_thesis(self, symbol: str, fundamentals: FundamentalMetrics,
                                  technicals: TechnicalAnalysis, sector: SectorAnalysis) -> str:
        """Generate investment thesis"""

        thesis = f"{symbol} presents a compelling investment opportunity in the {sector.sector} sector. "

        if fundamentals.earnings_growth_yoy > 0.10:
            thesis += f"Strong earnings growth of {fundamentals.earnings_growth_yoy:.1%} demonstrates operational excellence. "

        if technicals.trend_direction in [TechnicalTrend.STRONG_BULLISH, TechnicalTrend.BULLISH]:
            thesis += f"Technical momentum is {technicals.trend_direction.value.lower()} with trend strength of {technicals.trend_strength:.0%}. "

        if sector.relative_strength_vs_sector > 1.1:
            thesis += f"The stock shows strong relative performance, outperforming its sector by {(sector.relative_strength_vs_sector-1)*100:.0f}%. "

        thesis += "Current market conditions and company fundamentals support continued outperformance."

        return thesis

    def _identify_key_catalysts(self, symbol: str, earnings: EarningsAnalysis,
                               sector: SectorAnalysis) -> List[str]:
        """Identify key catalysts for the stock"""

        catalysts = []

        # Earnings catalysts
        if earnings.days_to_earnings <= 30:
            catalysts.append(f"Upcoming earnings in {earnings.days_to_earnings} days")

        if earnings.beat_rate > 0.7:
            catalysts.append(f"Strong earnings beat history ({earnings.beat_rate:.0%} beat rate)")

        # Sector catalysts
        if sector.sector_momentum == "ACCELERATING":
            catalysts.append(f"Accelerating {sector.sector} sector momentum")

        if sector.relative_strength_vs_sector > 1.15:
            catalysts.append("Strong relative performance vs peers")

        # General catalysts
        catalysts.extend([
            "Continued market leadership in key segments",
            "Potential for multiple expansion",
            "Strong free cash flow generation"
        ])

        return catalysts[:5]  # Return top 5 catalysts

    def _identify_key_risks(self, symbol: str, fundamentals: FundamentalMetrics,
                          technicals: TechnicalAnalysis, market_factors: MarketFactors = None) -> List[str]:
        """Identify key risks for the stock"""

        risks = []

        # Valuation risks
        if fundamentals.pe_ratio > 30:
            risks.append(f"High valuation (P/E: {fundamentals.pe_ratio:.1f})")

        if fundamentals.debt_to_equity > 2.0:
            risks.append(f"High debt levels (D/E: {fundamentals.debt_to_equity:.1f})")

        # Technical risks
        if technicals.current_price < technicals.sma_200:
            risks.append("Trading below 200-day moving average")

        # Market risks
        if market_factors and market_factors.vix_level > 25:
            risks.append("Elevated market volatility")

        # General risks
        risks.extend([
            "Sector rotation risk",
            "Interest rate sensitivity",
            "Competitive pressure"
        ])

        return risks[:5]  # Return top 5 risks

    def _calculate_strategy_fit(self, symbol: str, technicals: TechnicalAnalysis,
                              earnings: EarningsAnalysis) -> float:
        """Calculate how well current strategies fit the stock"""

        fit_score = 0.5  # Base score

        # Technical fit
        if technicals.trend_direction in [TechnicalTrend.STRONG_BULLISH, TechnicalTrend.BULLISH]:
            fit_score += 0.2

        # Volatility fit (good for premium strategies)
        if technicals.rsi > 70 or technicals.rsi < 30:
            fit_score += 0.1

        # Earnings timing fit
        if earnings.days_to_earnings > 30:
            fit_score += 0.2  # Good for most strategies

        return min(1.0, fit_score)

    def _recommend_strategies(self, symbol: str, fundamentals: FundamentalMetrics,
                            technicals: TechnicalAnalysis, market_factors: MarketFactors = None) -> List[str]:
        """Recommend optimal strategies based on analysis"""

        strategies = []

        # Based on trend
        if technicals.trend_direction in [TechnicalTrend.STRONG_BULLISH, TechnicalTrend.BULLISH]:
            strategies.append("LEAPS (Long-term bullish exposure)")
            strategies.append("Covered Calls (Income generation)")

        # Based on volatility
        if technicals.rsi > 60:  # High IV environment
            strategies.append("Credit Spreads (Premium collection)")
            strategies.append("Premium Selling (Systematic income)")

        # Based on fundamentals
        if fundamentals.pe_ratio < 25 and fundamentals.earnings_growth_yoy > 0.10:
            strategies.append("Long Stock (Fundamental value)")

        return strategies[:3]  # Return top 3 strategies

    def _calculate_strategy_timing(self, symbol: str, technicals: TechnicalAnalysis,
                                 earnings: EarningsAnalysis) -> float:
        """Calculate strategy timing score"""

        timing_score = 0.5  # Base score

        # Technical timing
        if technicals.trend_strength > 0.7:
            timing_score += 0.2

        # Earnings timing
        if earnings.days_to_earnings > 45:
            timing_score += 0.2  # Good timing for most strategies
        elif earnings.days_to_earnings < 14:
            timing_score -= 0.1  # Poor timing near earnings

        # Volume confirmation
        if technicals.volume_confirmation:
            timing_score += 0.1

        return min(1.0, max(0.0, timing_score))

    def _create_fallback_analysis(self, symbol: str) -> ComprehensiveAnalysis:
        """Create fallback analysis when main analysis fails"""

        # Create minimal analysis with default values
        current_price = 150.0  # Default price

        fallback_fundamentals = FundamentalMetrics(
            symbol=symbol, pe_ratio=25.0, forward_pe=22.0, peg_ratio=1.5,
            price_to_book=5.0, price_to_sales=6.0, debt_to_equity=1.0,
            current_ratio=1.5, roe=0.15, roa=0.10, gross_margin=0.30,
            operating_margin=0.20, net_margin=0.15, revenue_growth_yoy=0.08,
            earnings_growth_yoy=0.12, free_cash_flow=10e9, dividend_yield=0.02,
            payout_ratio=0.30
        )

        fallback_technical = TechnicalAnalysis(
            symbol=symbol, current_price=current_price, trend_direction=TechnicalTrend.NEUTRAL,
            trend_strength=0.5, sma_20=current_price*0.98, sma_50=current_price*0.95,
            sma_200=current_price*0.90, ema_12=current_price*0.99, ema_26=current_price*0.97,
            rsi=50.0, macd=0.0, macd_signal=0.0, bollinger_upper=current_price*1.05,
            bollinger_lower=current_price*0.95, bollinger_position=0.5,
            support_levels=[140.0, 135.0], resistance_levels=[160.0, 165.0],
            key_support=140.0, key_resistance=160.0, volume_trend="Normal",
            volume_confirmation=False, relative_volume=1.0
        )

        fallback_earnings = EarningsAnalysis(
            symbol=symbol, next_earnings_date=datetime.now() + timedelta(days=30),
            days_to_earnings=30, earnings_surprise_history=[0.0, 0.0, 0.0, 0.0],
            revenue_surprise_history=[0.0, 0.0, 0.0, 0.0], beat_rate=0.5,
            eps_estimate=5.0, eps_estimate_range=(4.8, 5.2), revenue_estimate=100e9,
            revenue_estimate_range=(98e9, 102e9), management_guidance="Neutral outlook",
            analyst_revisions_trend="STABLE", guidance_confidence=0.5,
            earnings_volatility_expected=0.05, post_earnings_move_estimate=0.04,
            options_activity_unusual=False
        )

        fallback_sector = SectorAnalysis(
            symbol=symbol, sector="Technology", industry="Software",
            sector_performance_1m=0.02, sector_performance_3m=0.05,
            sector_performance_ytd=0.10, relative_strength_vs_sector=1.0,
            relative_strength_vs_market=1.0, sector_rotation_score=0.5,
            sector_momentum="STABLE", peer_comparison_rank=10, total_peers=20,
            institutional_ownership=0.60, insider_ownership=0.05,
            recent_institutional_changes="STABLE"
        )

        fallback_prediction = PricePrediction(
            symbol=symbol, current_price=current_price, timeframe_days=45,
            target_price=current_price*1.05, bull_case_target=current_price*1.20,
            bear_case_target=current_price*0.85, confidence_level=0.68,
            price_range_low=current_price*0.90, price_range_high=current_price*1.10,
            probability_up_10pct=0.30, probability_up_20pct=0.15,
            probability_down_10pct=0.25, probability_down_20pct=0.10,
            volatility_estimate=0.25, max_drawdown_estimate=0.15, sharpe_ratio_estimate=1.0
        )

        return ComprehensiveAnalysis(
            symbol=symbol,
            analysis_date=datetime.now(),
            fundamental_metrics=fallback_fundamentals,
            technical_analysis=fallback_technical,
            earnings_analysis=fallback_earnings,
            sector_analysis=fallback_sector,
            price_prediction=fallback_prediction,
            ai_sentiment_analysis=None,
            ai_fundamental_review=f"Analysis unavailable for {symbol}",
            ai_technical_summary=f"Technical analysis unavailable for {symbol}",
            ai_risk_assessment=f"Standard market risks apply to {symbol}",
            analyst_rating=AnalystRating.HOLD,
            confidence_score=0.3,
            investment_thesis=f"{symbol} requires further analysis for comprehensive assessment",
            key_catalysts=["Earnings announcement", "Sector momentum"],
            key_risks=["Market volatility", "Sector rotation"],
            strategy_fit_score=0.5,
            recommended_strategies=["Hold", "Monitor"],
            strategy_timing_score=0.5
        )


def create_comprehensive_analyzer(api_key: str = None) -> ComprehensiveFinancialAnalyzer:
    """Factory function to create comprehensive financial analyzer"""
    return ComprehensiveFinancialAnalyzer(api_key)
