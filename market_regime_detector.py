"""
Advanced Market Regime Detection System
Real-time bull/bear/sideways market detection with volatility regime analysis

This module provides sophisticated market regime detection that influences strategy selection
by analyzing multiple market indicators to classify current market conditions.

Features:
- Bull/Bear/Sideways market classification
- Volatility regime detection (Low/Normal/High/Extreme)
- Trend strength measurement
- Market breadth analysis
- Sector rotation detection
- Confidence scoring for regime classification
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import math
import statistics

from market_analysis_engine import MarketRegime, VolatilityRegime

@dataclass
class RegimeIndicators:
    """Market regime indicators and signals"""
    
    # Price-based indicators
    spy_vs_ma20: float  # SPY vs 20-day MA
    spy_vs_ma50: float  # SPY vs 50-day MA
    spy_vs_ma200: float  # SPY vs 200-day MA
    
    # Momentum indicators
    rsi_14: float  # 14-day RSI
    macd_signal: str  # MACD signal (bullish/bearish/neutral)
    momentum_score: float  # Overall momentum (0-1)
    
    # Breadth indicators
    advance_decline_ratio: float  # Advancing vs declining stocks
    new_highs_lows_ratio: float  # New highs vs new lows
    up_volume_percentage: float  # Percentage of volume in advancing stocks
    
    # Volatility indicators
    vix_level: float
    vix_ma20: float  # VIX 20-day moving average
    vix_percentile: float  # VIX percentile vs 1-year range
    
    # Sector analysis
    sector_leadership: List[str]  # Leading sectors
    sector_rotation_signal: str  # growth/value/defensive
    
    # Sentiment indicators
    put_call_ratio: float
    fear_greed_index: float
    insider_buying_ratio: float

@dataclass
class RegimeClassification:
    """Complete market regime classification"""
    
    # Primary classification
    market_regime: MarketRegime
    volatility_regime: VolatilityRegime
    
    # Confidence and strength
    regime_confidence: float  # 0-1 confidence in classification
    trend_strength: float  # -1 to 1 (bearish to bullish)
    
    # Supporting analysis
    key_supporting_factors: List[str]
    key_contradicting_factors: List[str]
    
    # Regime stability
    regime_duration_days: int  # How long current regime has persisted
    regime_stability: str  # stable/transitioning/volatile
    
    # Strategy implications
    favored_strategies: List[str]
    strategies_to_avoid: List[str]
    
    # Timing signals
    regime_change_probability: float  # 0-1 probability of regime change soon
    next_regime_likely: Optional[MarketRegime]

class MarketRegimeDetector:
    """Advanced market regime detection system"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.regime_history = []
        self.indicator_weights = self._initialize_weights()
        
    def detect_market_regime(self, lookback_days: int = 60) -> RegimeClassification:
        """
        Detect current market regime with comprehensive analysis
        
        Args:
            lookback_days: Number of days to analyze for regime detection
            
        Returns:
            Complete regime classification with confidence scoring
        """
        
        # Gather regime indicators
        indicators = self._gather_regime_indicators(lookback_days)
        
        # Classify market regime
        market_regime, regime_confidence = self._classify_market_regime(indicators)
        
        # Classify volatility regime
        volatility_regime = self._classify_volatility_regime(indicators)
        
        # Calculate trend strength
        trend_strength = self._calculate_trend_strength(indicators)
        
        # Analyze regime stability
        regime_duration, stability = self._analyze_regime_stability(market_regime)
        
        # Identify supporting and contradicting factors
        supporting_factors = self._identify_supporting_factors(market_regime, indicators)
        contradicting_factors = self._identify_contradicting_factors(market_regime, indicators)
        
        # Determine strategy implications
        favored_strategies, avoid_strategies = self._determine_strategy_implications(
            market_regime, volatility_regime, trend_strength
        )
        
        # Assess regime change probability
        change_probability, next_regime = self._assess_regime_change_probability(
            market_regime, indicators, trend_strength
        )
        
        classification = RegimeClassification(
            market_regime=market_regime,
            volatility_regime=volatility_regime,
            regime_confidence=regime_confidence,
            trend_strength=trend_strength,
            key_supporting_factors=supporting_factors,
            key_contradicting_factors=contradicting_factors,
            regime_duration_days=regime_duration,
            regime_stability=stability,
            favored_strategies=favored_strategies,
            strategies_to_avoid=avoid_strategies,
            regime_change_probability=change_probability,
            next_regime_likely=next_regime
        )
        
        # Update regime history
        self._update_regime_history(classification)
        
        return classification
    
    def _gather_regime_indicators(self, lookback_days: int) -> RegimeIndicators:
        """Gather all indicators needed for regime detection"""
        
        # In production, this would fetch real market data
        # For now, return representative indicators
        
        return RegimeIndicators(
            spy_vs_ma20=1.02,  # SPY 2% above 20-day MA
            spy_vs_ma50=1.05,  # SPY 5% above 50-day MA
            spy_vs_ma200=1.12,  # SPY 12% above 200-day MA
            rsi_14=58.5,
            macd_signal="bullish",
            momentum_score=0.65,
            advance_decline_ratio=1.4,  # More advancing than declining
            new_highs_lows_ratio=2.1,  # More new highs than lows
            up_volume_percentage=0.62,  # 62% of volume in advancing stocks
            vix_level=18.5,
            vix_ma20=20.2,
            vix_percentile=35.0,
            sector_leadership=["Technology", "Communication Services"],
            sector_rotation_signal="growth_rotation",
            put_call_ratio=0.85,  # More calls than puts
            fear_greed_index=65.0,  # Moderate greed
            insider_buying_ratio=1.2  # More buying than selling
        )
    
    def _classify_market_regime(self, indicators: RegimeIndicators) -> Tuple[MarketRegime, float]:
        """Classify market regime with confidence scoring"""
        
        # Calculate regime scores
        bull_score = 0.0
        bear_score = 0.0
        sideways_score = 0.0
        
        # Price vs moving averages (30% weight)
        ma_weight = 0.30
        if indicators.spy_vs_ma20 > 1.02 and indicators.spy_vs_ma50 > 1.02:
            bull_score += ma_weight * 0.8
        elif indicators.spy_vs_ma20 < 0.98 and indicators.spy_vs_ma50 < 0.98:
            bear_score += ma_weight * 0.8
        else:
            sideways_score += ma_weight * 0.6
        
        # Momentum indicators (25% weight)
        momentum_weight = 0.25
        if indicators.momentum_score > 0.6 and indicators.rsi_14 > 50:
            bull_score += momentum_weight * indicators.momentum_score
        elif indicators.momentum_score < 0.4 and indicators.rsi_14 < 50:
            bear_score += momentum_weight * (1 - indicators.momentum_score)
        else:
            sideways_score += momentum_weight * 0.5
        
        # Market breadth (20% weight)
        breadth_weight = 0.20
        if (indicators.advance_decline_ratio > 1.2 and 
            indicators.new_highs_lows_ratio > 1.5 and
            indicators.up_volume_percentage > 0.55):
            bull_score += breadth_weight * 0.9
        elif (indicators.advance_decline_ratio < 0.8 and
              indicators.new_highs_lows_ratio < 0.7 and
              indicators.up_volume_percentage < 0.45):
            bear_score += breadth_weight * 0.9
        else:
            sideways_score += breadth_weight * 0.6
        
        # Sentiment indicators (15% weight)
        sentiment_weight = 0.15
        if (indicators.put_call_ratio < 1.0 and 
            indicators.fear_greed_index > 60 and
            indicators.insider_buying_ratio > 1.0):
            bull_score += sentiment_weight * 0.7
        elif (indicators.put_call_ratio > 1.3 and
              indicators.fear_greed_index < 40):
            bear_score += sentiment_weight * 0.7
        else:
            sideways_score += sentiment_weight * 0.5
        
        # Sector rotation (10% weight)
        sector_weight = 0.10
        if indicators.sector_rotation_signal == "growth_rotation":
            bull_score += sector_weight * 0.8
        elif indicators.sector_rotation_signal == "defensive_rotation":
            bear_score += sector_weight * 0.8
        else:
            sideways_score += sector_weight * 0.5
        
        # Determine regime and confidence
        scores = {
            MarketRegime.BULL_MARKET: bull_score,
            MarketRegime.BEAR_MARKET: bear_score,
            MarketRegime.SIDEWAYS_MARKET: sideways_score
        }
        
        regime = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[regime]
        
        # Check for transition state
        sorted_scores = sorted(scores.values(), reverse=True)
        if sorted_scores[0] - sorted_scores[1] < 0.15:  # Close scores indicate transition
            regime = MarketRegime.TRANSITION
            confidence = 0.5
        
        return regime, min(confidence, 1.0)
    
    def _classify_volatility_regime(self, indicators: RegimeIndicators) -> VolatilityRegime:
        """Classify volatility regime"""
        
        vix_level = indicators.vix_level
        vix_percentile = indicators.vix_percentile
        
        # VIX-based classification with percentile confirmation
        if vix_level > 30 or vix_percentile > 90:
            return VolatilityRegime.EXTREME_VOL
        elif vix_level > 25 or vix_percentile > 75:
            return VolatilityRegime.HIGH_VOL
        elif vix_level < 15 or vix_percentile < 25:
            return VolatilityRegime.LOW_VOL
        else:
            return VolatilityRegime.NORMAL_VOL
    
    def _calculate_trend_strength(self, indicators: RegimeIndicators) -> float:
        """Calculate trend strength from -1 (strong bear) to +1 (strong bull)"""
        
        strength_factors = []
        
        # Price vs MAs
        ma_strength = (indicators.spy_vs_ma20 - 1) + (indicators.spy_vs_ma50 - 1) + (indicators.spy_vs_ma200 - 1)
        strength_factors.append(ma_strength * 2)  # Scale to reasonable range
        
        # Momentum
        momentum_strength = (indicators.momentum_score - 0.5) * 2
        strength_factors.append(momentum_strength)
        
        # RSI
        rsi_strength = (indicators.rsi_14 - 50) / 50
        strength_factors.append(rsi_strength)
        
        # Market breadth
        breadth_strength = (indicators.advance_decline_ratio - 1) + (indicators.up_volume_percentage - 0.5) * 2
        strength_factors.append(breadth_strength)
        
        # Average and bound
        avg_strength = statistics.mean(strength_factors)
        return max(-1.0, min(1.0, avg_strength))
    
    def _analyze_regime_stability(self, current_regime: MarketRegime) -> Tuple[int, str]:
        """Analyze how long current regime has persisted and its stability"""
        
        # Count consecutive days in current regime
        duration = 1
        if self.regime_history:
            for past_regime in reversed(self.regime_history[-30:]):  # Look back 30 days
                if past_regime.market_regime == current_regime:
                    duration += 1
                else:
                    break
        
        # Classify stability
        if duration >= 20:
            stability = "stable"
        elif duration >= 10:
            stability = "established"
        elif duration >= 5:
            stability = "developing"
        else:
            stability = "transitioning"
        
        return duration, stability
    
    def _identify_supporting_factors(self, regime: MarketRegime, indicators: RegimeIndicators) -> List[str]:
        """Identify factors supporting the current regime classification"""
        
        factors = []
        
        if regime == MarketRegime.BULL_MARKET:
            if indicators.spy_vs_ma20 > 1.02:
                factors.append("Price above 20-day moving average")
            if indicators.advance_decline_ratio > 1.2:
                factors.append("Strong market breadth (more advancing stocks)")
            if indicators.momentum_score > 0.6:
                factors.append("Positive momentum indicators")
            if indicators.put_call_ratio < 1.0:
                factors.append("Bullish options sentiment (more calls than puts)")
            if indicators.fear_greed_index > 60:
                factors.append("Greedy market sentiment")
        
        elif regime == MarketRegime.BEAR_MARKET:
            if indicators.spy_vs_ma20 < 0.98:
                factors.append("Price below 20-day moving average")
            if indicators.advance_decline_ratio < 0.8:
                factors.append("Weak market breadth (more declining stocks)")
            if indicators.momentum_score < 0.4:
                factors.append("Negative momentum indicators")
            if indicators.put_call_ratio > 1.3:
                factors.append("Bearish options sentiment (more puts than calls)")
            if indicators.fear_greed_index < 40:
                factors.append("Fearful market sentiment")
        
        elif regime == MarketRegime.SIDEWAYS_MARKET:
            if 0.98 <= indicators.spy_vs_ma20 <= 1.02:
                factors.append("Price near 20-day moving average")
            if 0.9 <= indicators.advance_decline_ratio <= 1.1:
                factors.append("Balanced market breadth")
            if indicators.vix_level < 20:
                factors.append("Low volatility environment")
        
        return factors[:5]  # Return top 5 factors
    
    def _identify_contradicting_factors(self, regime: MarketRegime, indicators: RegimeIndicators) -> List[str]:
        """Identify factors that contradict the current regime classification"""
        
        factors = []
        
        if regime == MarketRegime.BULL_MARKET:
            if indicators.vix_level > 25:
                factors.append("Elevated volatility (VIX > 25)")
            if indicators.put_call_ratio > 1.2:
                factors.append("Bearish options sentiment")
            if indicators.fear_greed_index < 30:
                factors.append("Extreme fear in sentiment")
        
        elif regime == MarketRegime.BEAR_MARKET:
            if indicators.new_highs_lows_ratio > 1.5:
                factors.append("More new highs than lows")
            if indicators.insider_buying_ratio > 1.3:
                factors.append("Strong insider buying")
            if indicators.fear_greed_index > 70:
                factors.append("Extreme greed in sentiment")
        
        elif regime == MarketRegime.SIDEWAYS_MARKET:
            if abs(indicators.trend_strength) > 0.7:
                factors.append("Strong directional momentum")
            if indicators.vix_level > 30:
                factors.append("Extreme volatility suggests trending market")
        
        return factors[:3]  # Return top 3 contradicting factors
    
    def _determine_strategy_implications(self, market_regime: MarketRegime, 
                                       volatility_regime: VolatilityRegime,
                                       trend_strength: float) -> Tuple[List[str], List[str]]:
        """Determine which strategies are favored/avoided in current regime"""
        
        favored = []
        avoid = []
        
        # Market regime implications
        if market_regime == MarketRegime.BULL_MARKET:
            favored.extend(["LEAPS", "Credit Spreads", "Covered Calls"])
            if trend_strength > 0.7:
                avoid.append("Premium Selling")
        
        elif market_regime == MarketRegime.BEAR_MARKET:
            favored.extend(["Premium Selling", "Covered Calls"])
            avoid.extend(["LEAPS", "Credit Spreads"])
        
        elif market_regime == MarketRegime.SIDEWAYS_MARKET:
            favored.extend(["Covered Calls", "Premium Selling", "Iron Condors"])
            avoid.append("LEAPS")
        
        # Volatility regime implications
        if volatility_regime in [VolatilityRegime.HIGH_VOL, VolatilityRegime.EXTREME_VOL]:
            favored.extend(["Premium Selling", "Covered Calls"])
            if "LEAPS" not in avoid:
                avoid.append("LEAPS")
        
        elif volatility_regime == VolatilityRegime.LOW_VOL:
            favored.append("LEAPS")
            if "Premium Selling" in favored:
                favored.remove("Premium Selling")
        
        return list(set(favored)), list(set(avoid))
    
    def _assess_regime_change_probability(self, current_regime: MarketRegime,
                                        indicators: RegimeIndicators,
                                        trend_strength: float) -> Tuple[float, Optional[MarketRegime]]:
        """Assess probability of regime change and likely next regime"""
        
        change_probability = 0.1  # Base probability
        next_regime = None
        
        # Factors that increase regime change probability
        if indicators.vix_level > 30:
            change_probability += 0.2  # High volatility suggests instability
        
        if abs(trend_strength) < 0.3:
            change_probability += 0.15  # Weak trend suggests transition
        
        # Check for regime exhaustion signals
        if current_regime == MarketRegime.BULL_MARKET:
            if indicators.fear_greed_index > 80:  # Extreme greed
                change_probability += 0.25
                next_regime = MarketRegime.SIDEWAYS_MARKET
            elif indicators.vix_level < 12:  # Complacency
                change_probability += 0.15
        
        elif current_regime == MarketRegime.BEAR_MARKET:
            if indicators.fear_greed_index < 20:  # Extreme fear
                change_probability += 0.25
                next_regime = MarketRegime.SIDEWAYS_MARKET
            elif indicators.vix_level > 35:  # Panic
                change_probability += 0.15
        
        # Duration-based probability
        duration, _ = self._analyze_regime_stability(current_regime)
        if duration > 30:  # Long-lasting regime
            change_probability += 0.1
        
        return min(change_probability, 0.8), next_regime
    
    def _update_regime_history(self, classification: RegimeClassification):
        """Update regime history for trend analysis"""
        
        self.regime_history.append(classification)
        
        # Keep only last 90 days
        if len(self.regime_history) > 90:
            self.regime_history = self.regime_history[-90:]
    
    def _initialize_weights(self) -> Dict[str, float]:
        """Initialize indicator weights for regime classification"""
        
        return {
            'price_vs_ma': 0.30,
            'momentum': 0.25,
            'breadth': 0.20,
            'sentiment': 0.15,
            'sector_rotation': 0.10
        }
    
    def get_regime_summary(self, classification: RegimeClassification) -> str:
        """Get human-readable regime summary"""
        
        regime_names = {
            MarketRegime.BULL_MARKET: "Bull Market",
            MarketRegime.BEAR_MARKET: "Bear Market", 
            MarketRegime.SIDEWAYS_MARKET: "Sideways Market",
            MarketRegime.TRANSITION: "Market Transition"
        }
        
        vol_names = {
            VolatilityRegime.LOW_VOL: "Low Volatility",
            VolatilityRegime.NORMAL_VOL: "Normal Volatility",
            VolatilityRegime.HIGH_VOL: "High Volatility",
            VolatilityRegime.EXTREME_VOL: "Extreme Volatility"
        }
        
        summary = f"{regime_names[classification.market_regime]} with {vol_names[classification.volatility_regime]}"
        summary += f" (Confidence: {classification.regime_confidence:.0%})"
        
        if classification.trend_strength > 0.5:
            summary += f" - Strong Bullish Trend"
        elif classification.trend_strength < -0.5:
            summary += f" - Strong Bearish Trend"
        else:
            summary += f" - Weak/Mixed Trend"
        
        return summary
