"""
Enhanced Strategy Analyzer - Detailed Stock Classification System
Based on <PERSON>'s documented investment strategies with comprehensive analysis output

This module provides detailed strategy-based stock classification that includes:
1. Specific strategy matching with exact criteria validation
2. Detailed explanations of why each stock fits a particular strategy
3. Key catalysts driving the investment thesis
4. Financial analyst projections and reviews
5. Supporting data and metrics that justify the classification
6. Integration with existing Erica methodology implementation

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import logging

# Import existing system components
from intelligent_strategy_engine import IntelligentStrategyEngine, StrategyOfTheDay
from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
from strategy_decision_tree import StrategyDecisionTree, StrategyRecommendation
from erica_strategy_engine import EricaStrategyEngine, EricaSignals, MarketBias, EricaTradeSetup
from ai_analysis_engine import AIAnalysisEngine, AIAnalysisResult, AnalysisType
from daily_outline import StrategyType
from enum import Enum

class ExtendedStrategyType(Enum):
    """Extended strategy types including all field guide strategies"""
    # Original strategies
    COVERED_CALL = "covered_call"
    CREDIT_SPREAD = "credit_spread"
    LEAPS = "leaps"
    PREMIUM_SELLING = "premium_selling"

    # New field guide strategies
    BULLISH_SPREAD = "bullish_spread"
    PUT_CREDIT_SPREAD = "put_credit_spread"
    CASH_SECURED_PUT = "cash_secured_put"
    BEAR_CALL_SPREAD = "bear_call_spread"
    RICH_WOMANS_CC = "rich_womans_cc"
    POOR_MANS_CC = "poor_mans_cc"
    WHEEL_STRATEGY = "wheel_strategy"
    CALENDAR_SPREADS = "calendar_spreads"
    ROLLOUT_STRATEGY = "rollout_strategy"
from enhanced_strategies import STOCK_RULES

class AnalysisDepth(Enum):
    BASIC = "basic"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

class ConfidenceLevel(Enum):
    VERY_HIGH = "very_high"  # 90%+
    HIGH = "high"           # 75-89%
    MODERATE = "moderate"   # 60-74%
    LOW = "low"            # 40-59%
    VERY_LOW = "very_low"  # <40%

@dataclass
class StrategyCriteriaMatch:
    """Detailed matching of strategy criteria"""
    criterion_name: str
    required_value: Any
    actual_value: Any
    is_met: bool
    score: float  # 0-1
    importance: str  # "critical", "important", "moderate", "minor"
    explanation: str

@dataclass
class CatalystAnalysis:
    """Analysis of key catalysts driving the investment thesis"""
    catalyst_type: str  # "earnings", "technical", "fundamental", "market", "news"
    description: str
    impact_level: str  # "high", "medium", "low"
    timeframe: str  # "immediate", "short_term", "medium_term", "long_term"
    probability: float  # 0-1
    supporting_evidence: List[str]

@dataclass
class StrategyJustification:
    """Detailed justification for strategy selection"""
    strategy_name: str
    primary_reasoning: str
    confidence_level: str
    supporting_factors: List[str]
    market_conditions: Dict[str, Any]
    erica_methodology_reference: str

@dataclass
class BullishCatalyst:
    """Positive catalyst supporting the stock"""
    catalyst_name: str
    description: str
    impact_assessment: str  # "High", "Medium", "Low"
    catalyst_type: str  # "Fundamental", "Technical", "Event-driven"
    timeframe: str
    supporting_data: List[str]

@dataclass
class RiskFactor:
    """Risk factor that could negatively impact the stock"""
    risk_name: str
    description: str
    severity: str  # "High", "Medium", "Low"
    risk_type: str  # "Regulatory", "Competitive", "Technical", "Market"
    mitigation_strategy: str
    probability: str  # "High", "Medium", "Low"

@dataclass
class AnalystSentiment:
    """Analyst sentiment and price targets"""
    consensus_rating: str  # "Strong Buy", "Buy", "Hold", "Sell", "Strong Sell"
    average_price_target: float
    upside_downside_percent: float
    number_of_analysts: int
    recent_changes: List[str]
    target_range: Tuple[float, float]  # (low, high)

@dataclass
class ComprehensiveCatalystAnalysis:
    """Complete catalyst analysis for a stock"""
    symbol: str
    analysis_date: datetime
    strategy_justification: StrategyJustification
    bullish_catalysts: List[BullishCatalyst]
    risk_factors: List[RiskFactor]
    analyst_sentiment: AnalystSentiment
    overall_outlook: str

@dataclass
class FinancialProjections:
    """Financial analyst projections and reviews"""
    price_target_mean: Optional[float]
    price_target_high: Optional[float]
    price_target_low: Optional[float]
    analyst_rating: Optional[str]  # "Strong Buy", "Buy", "Hold", "Sell", "Strong Sell"
    eps_estimate_current: Optional[float]
    eps_estimate_next: Optional[float]
    revenue_growth_estimate: Optional[float]
    analyst_count: int
    upgrade_downgrade_trend: str
    key_analyst_notes: List[str]

@dataclass
class SupportingMetrics:
    """Supporting data and metrics that justify the classification"""
    technical_indicators: Dict[str, Any]
    fundamental_metrics: Dict[str, Any]
    options_metrics: Dict[str, Any]
    market_metrics: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    comparative_analysis: Dict[str, Any]

@dataclass
class DetailedStrategyAnalysis:
    """Comprehensive strategy analysis with detailed explanations"""
    symbol: str
    analysis_timestamp: datetime
    
    # Primary Strategy Classification
    primary_strategy: StrategyType
    strategy_confidence: ConfidenceLevel
    strategy_score: float  # 0-100
    
    # Detailed Strategy Matching
    criteria_matches: List[StrategyCriteriaMatch]
    criteria_summary: str
    why_this_strategy: str
    
    # Investment Thesis
    key_catalysts: List[CatalystAnalysis]
    investment_thesis: str
    risk_factors: List[str]

    # Financial Analysis
    financial_projections: FinancialProjections
    analyst_sentiment: str

    # Supporting Data
    supporting_metrics: SupportingMetrics

    # Erica's Specific Methodology
    erica_reasoning: str
    erica_specific_rules: Dict[str, Any]

    # Alternative Strategies
    alternative_strategies: List[Tuple[StrategyType, float, str]]  # strategy, score, reason

    # Execution Details
    entry_criteria: List[str]
    exit_criteria: List[str]
    position_sizing: Dict[str, Any]
    timing_considerations: List[str]

    # Market Context
    market_environment: str
    sector_analysis: str
    relative_strength: str

    # Optional Fields (must be at end)
    erica_trade_setup: Optional[EricaTradeSetup] = None
    comprehensive_catalyst_analysis: Optional[ComprehensiveCatalystAnalysis] = None

class EnhancedStrategyAnalyzer:
    """Enhanced analyzer providing detailed strategy-based stock classification"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.market_analyzer = MarketAnalysisEngine(api_key)
        self.strategy_engine = IntelligentStrategyEngine(api_key)
        self.erica_engine = EricaStrategyEngine()
        self.ai_analyzer = AIAnalysisEngine(api_key)
        self.decision_tree = StrategyDecisionTree()

        self.logger = logging.getLogger(__name__)

        # Initialize live data integration
        try:
            from live_market_data_integration import create_live_data_provider
            from field_guide_strategy_selector import create_field_guide_selector

            self.live_data_provider = create_live_data_provider(api_key)
            self.field_guide_selector = create_field_guide_selector(api_key)
            self.use_live_data = True
            self.logger.info("✅ Live data integration enabled")
        except Exception as e:
            self.logger.warning(f"⚠️  Live data integration failed, using fallback: {str(e)}")
            self.live_data_provider = None
            self.field_guide_selector = None
            self.use_live_data = False

        # Load Erica's strategy criteria from slides and field guide
        self.strategy_criteria = self._load_erica_strategy_criteria()
        
    def _load_erica_strategy_criteria(self) -> Dict[str, Dict[str, Any]]:
        """Load Erica's specific strategy criteria based on her documented slides and field guide"""
        return {
            "bullish_spread": {
                "setup_requirements": [
                    "Buy call you expect price can reach by expiration",
                    "Sell a lower-strike call to take upfront credit (instant profit)",
                    "Set stock alarm: $1 below short call ($75-$200 stocks), $2.50 (volatile/higher), $5 (high-priced)"
                ],
                "alarm_rules": {
                    "low_price": {"range": (75, 200), "offset": -1.0},
                    "mid_price": {"range": (200, 400), "offset": -2.5},
                    "high_price": {"range": (400, 1000), "offset": -5.0}
                },
                "conversion_rules": [
                    "If alarm triggers: buy 100 shares to convert short call to covered call",
                    "Sell upper leg for extra premium",
                    "Max risk: spread width less credit received"
                ],
                "morphing": "Spreads can morph bullish ↔ bearish depending on direction"
            },

            "put_credit_spread": {
                "use_when": "Neutral-to-bullish; happy to own shares at discount or not at all",
                "setup_requirements": [
                    "Sell put at strike you don't expect breached (or willing to buy)",
                    "Buy lower-strike put as insurance",
                    "DTE: 7-21 days (faster theta, easier rolls)",
                    "Strike: Below support, 20-30Δ for 'paid to wait', 10-20Δ for avoidance"
                ],
                "outcomes": [
                    "Price above short put → expire worthless → keep credit",
                    "Price below short put → assignment → sell covered call immediately"
                ],
                "management": "Close early to avoid risk if price rallies; assignment draws capital"
            },

            "cash_secured_put": {
                "purposes": ["Fast money to buy", "Ideal price entry", "Wheel entry", "Assignment avoidance"],
                "dte_range": (7, 21),
                "delta_targets": {
                    "paid_to_wait": (0.20, 0.30),
                    "avoidance": (0.10, 0.20)
                },
                "earnings_adjustment": "Skip or use reduced size; IV collapses after",
                "profit_taking": "Close at 50-70% max profit or when Δ < 0.07",
                "roll_trigger": "Before strike ITM by ≥0.30-0.40Δ"
            },

            "covered_call": {
                "new_cc_specs": {
                    "dte_range": (2, 10),  # Fast decay, 2-DTE cash cycles OK
                    "strike_selection": "+0.5-1.5× ATR above entry or 20-30Δ",
                    "earnings_approach": "Further OTM if keeping stock, tighter if OK to exit"
                },
                "management": {
                    "price_runs_through": "Roll up/out for credit or BTC to free shares",
                    "down_moves": "Roll down/out for more credit or pause for CSP add",
                    "profit_target": 0.50
                },
                "integrations": ["Wheel pairing", "PMCC/Rich Woman's variant"]
            },

            "bear_call_spread": {
                "use_when": "Think price won't reach level by expiry (range/mean-reversion)",
                "structure": "Sell lower-strike call, buy higher-strike call (insurance)",
                "dte_range": (7, 21),
                "alarm_system": {
                    "low_price": {"range": (75, 200), "offset": -1.0},
                    "mid_price": {"range": (200, 400), "offset": -2.5},
                    "high_price": {"range": (400, 1000), "offset": -5.0}
                },
                "conversion_playbook": [
                    "Alarm triggers → buy 100 shares → short call becomes covered call",
                    "Sell long call (upper leg) for extra money",
                    "Can morph bearish ↔ bullish spread"
                ]
            },

            "rich_womans_cc": {
                "use_when": "Bullish with longer horizon; want equity-like upside with less cash",
                "leaps_specs": {
                    "dte_minimum": 365,
                    "preferred_dte": 547,  # 1.5 years
                    "delta_range": (0.50, 0.75),
                    "bid_ask": "tight spreads required"
                },
                "short_call_management": "Sell on up days at strikes you don't expect to cover",
                "alarm_system": "$1-2 below short call strike",
                "conversion": "Roll up/out or buy shares to convert short call to CC"
            },

            "poor_mans_cc": {
                "use_when": "Want covered call engine with minimal capital",
                "setup": "Buy cheap LEAPS (exploit pricing inefficiencies), sell near-term calls",
                "management": "Keep selling calls to recover LEAPS cost; roll/manage as above"
            },

            "wheel_strategy": {
                "market_conditions": "Rising or range-bound market (choppy/rising)",
                "entry_methods": {
                    "via_put": "Sell CSP → if assigned own shares → sell CC",
                    "via_cc": "Buy shares → sell CC → if called away recycle to puts"
                },
                "timing_creativity": [
                    "Buy shares on dip, sell CC after pop",
                    "Place puts slightly above price to speed assignment in bullish tape"
                ],
                "caveats": "Downtrend makes unprofitable (underwater shares, weak call premiums)"
            },

            "calendar_spreads": {
                "use_when": "Want recurring income from same long call",
                "structure": "Buy far-dated call, sell nearer-dated call",
                "reuse_concept": "Keep re-using upper leg for multiple cycles",
                "management": "Same buy-to-cover as verticals if price runs"
            },

            "leaps_strategy": {
                "essentials": {
                    "minimum_dte": 365,
                    "delta_range": (0.50, 0.65),
                    "theta_decay": "mostly late in life"
                },
                "plays": [
                    "Long-term: hold toward strike/exercise",
                    "Quarter-to-quarter: buy post-earnings dip, sell before next earnings",
                    "Earnings hold: hold through print (higher risk/reward)",
                    "Volatility: set GTC limit sells for quick gains"
                ]
            },

            "rollout_strategy": {
                "why_roll": "Take more premium, avoid ex-div, correct strike/timing",
                "mechanics": "BTC current, STO new (farther out, higher/lower strike)",
                "examples": [
                    "Roll down to capture extra premium in dip",
                    "Roll out when near expiry for fresh theta"
                ],
                "when_not_to_roll": "Price sprints far through strike and BTC too expensive"
            }
        }
        
    def analyze_stock_comprehensive(self, symbol: str, 
                                  analysis_depth: AnalysisDepth = AnalysisDepth.COMPREHENSIVE) -> DetailedStrategyAnalysis:
        """
        Perform comprehensive strategy-based analysis of a stock
        
        Args:
            symbol: Stock symbol to analyze
            analysis_depth: Level of analysis detail
            
        Returns:
            Detailed strategy analysis with comprehensive explanations
        """
        
        self.logger.info(f"Starting comprehensive analysis for {symbol}")
        
        try:
            # Get live market data if available
            live_data = None
            if self.use_live_data and self.live_data_provider:
                try:
                    live_data = self.live_data_provider.get_live_data(symbol)
                    self.logger.info(f"✅ Using live data for {symbol}: ${live_data.current_price:.2f}, IV: {live_data.iv_rank:.0%}")
                except Exception as e:
                    self.logger.warning(f"Live data fetch failed for {symbol}, using fallback: {str(e)}")

            # Get market and stock factors (enhanced with live data if available)
            market_factors = self.market_analyzer.analyze_market_factors()
            stock_factors = self._get_enhanced_stock_factors(symbol, live_data)

            # Get field guide strategy recommendation if available
            field_guide_setup = None
            if self.use_live_data and self.field_guide_selector and live_data:
                try:
                    field_guide_setup = self.field_guide_selector.select_optimal_strategy(symbol)
                    self.logger.info(f"✅ Field guide recommends: {field_guide_setup.strategy.value} ({field_guide_setup.confidence:.0%})")
                except Exception as e:
                    self.logger.warning(f"Field guide analysis failed: {str(e)}")

            # Get Erica's strategy recommendation
            erica_signals = self._convert_to_erica_signals(symbol, market_factors, stock_factors, live_data)
            market_bias = self._determine_market_bias(market_factors)
            erica_setups = self.erica_engine.analyze_setup(symbol, erica_signals, market_bias)
            
            # Get decision tree recommendation
            strategy_rec = self.decision_tree.recommend_strategy(symbol, market_factors, stock_factors)
            
            # Determine primary strategy (prefer Erica's if available)
            if erica_setups:
                primary_strategy = self._map_erica_to_strategy_type(erica_setups[0].strategy)
                primary_setup = erica_setups[0]
            else:
                primary_strategy = strategy_rec.primary_strategy
                primary_setup = None
            
            # Calculate confidence level
            confidence_level = self._calculate_confidence_level(strategy_rec.confidence)
            
            # Analyze criteria matches
            criteria_matches = self._analyze_criteria_matches(
                primary_strategy, symbol, market_factors, stock_factors, primary_setup
            )
            
            # Generate comprehensive catalyst analysis
            comprehensive_catalyst_analysis = self._generate_comprehensive_catalyst_analysis(
                symbol, primary_strategy, market_factors, stock_factors, live_data, field_guide_setup
            )

            # Generate investment thesis and catalysts (for backward compatibility)
            catalysts = self._extract_key_catalysts_from_comprehensive(comprehensive_catalyst_analysis)
            investment_thesis = self._generate_investment_thesis(symbol, primary_strategy, catalysts, criteria_matches)
            
            # Get financial projections
            financial_projections = self._get_financial_projections(symbol)
            
            # Generate supporting metrics
            supporting_metrics = self._generate_supporting_metrics(symbol, market_factors, stock_factors)
            
            # Get alternative strategies
            alternatives = self._get_alternative_strategies(strategy_rec, primary_strategy)
            
            # Generate execution details
            entry_criteria = self._generate_entry_criteria(primary_strategy, symbol, primary_setup)
            exit_criteria = self._generate_exit_criteria(primary_strategy, symbol, primary_setup)
            position_sizing = self._calculate_position_sizing(primary_strategy, symbol, market_factors, stock_factors)
            
            # Create comprehensive analysis
            analysis = DetailedStrategyAnalysis(
                symbol=symbol,
                analysis_timestamp=datetime.now(),
                primary_strategy=primary_strategy,
                strategy_confidence=confidence_level,
                strategy_score=strategy_rec.confidence * 100,
                criteria_matches=criteria_matches,
                criteria_summary=self._generate_criteria_summary(criteria_matches),
                why_this_strategy=self._explain_strategy_selection(primary_strategy, symbol, criteria_matches),
                key_catalysts=catalysts,
                investment_thesis=investment_thesis,
                risk_factors=strategy_rec.key_risk_factors,
                comprehensive_catalyst_analysis=comprehensive_catalyst_analysis,
                financial_projections=financial_projections,
                analyst_sentiment=self._determine_analyst_sentiment(financial_projections),
                supporting_metrics=supporting_metrics,
                erica_trade_setup=primary_setup,
                erica_reasoning=primary_setup.reasoning if primary_setup else "Standard strategy application",
                erica_specific_rules=self._get_erica_specific_rules(symbol),
                alternative_strategies=alternatives,
                entry_criteria=entry_criteria,
                exit_criteria=exit_criteria,
                position_sizing=position_sizing,
                timing_considerations=self._generate_timing_considerations(primary_strategy, symbol, market_factors),
                market_environment=self._describe_market_environment(market_factors),
                sector_analysis=self._analyze_sector_context(symbol, market_factors),
                relative_strength=self._analyze_relative_strength(symbol, stock_factors)
            )
            
            self.logger.info(f"Completed comprehensive analysis for {symbol}")
            return analysis

        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {str(e)}")
            raise

    def _get_enhanced_stock_factors(self, symbol: str, live_data=None) -> StockSpecificFactors:
        """Get stock factors enhanced with live data when available"""
        if live_data:
            # Create enhanced stock factors using live data
            return StockSpecificFactors(
                symbol=symbol,
                news_sentiment_score=0.0,  # Would need news API integration
                earnings_days_away=live_data.earnings_days_away,
                earnings_move_estimate=live_data.expected_move / live_data.current_price if live_data.expected_move else 0.08,
                recent_analyst_changes=[],
                relative_strength_vs_spy=live_data.relative_strength_spy,
                technical_confluence_score=0.7,  # Would calculate from technical data
                support_resistance_clarity=0.8 if live_data.support_level and live_data.resistance_level else 0.5,
                trend_alignment="neutral",  # Would determine from price action
                unusual_options_activity=False,  # Would need options flow data
                iv_rank=live_data.iv_rank,
                iv_percentile=live_data.iv_percentile,
                iv_vs_hv_ratio=live_data.iv_30 / live_data.hv_30 if live_data.hv_30 > 0 else 1.0,
                options_flow_sentiment="neutral",
                options_volume=0,
                bid_ask_spread=0.05,
                sector_performance=0.0,  # Would need sector data
                sector_rotation_impact="neutral",
                wheel_suitability_score=0.8,
                covered_call_attractiveness=0.9 if live_data.iv_rank > 0.5 else 0.6,
                credit_spread_opportunity=0.8 if live_data.iv_rank > 0.6 else 0.5,
                leaps_opportunity=0.7 if live_data.iv_rank < 0.4 else 0.4
            )
        else:
            # Fallback to original method
            return self.market_analyzer.analyze_stock_factors(symbol)

    def _convert_to_erica_signals(self, symbol: str, market_factors: MarketFactors,
                                 stock_factors: StockSpecificFactors, live_data=None) -> EricaSignals:
        """Convert market/stock factors to Erica's signal format with live data enhancement"""

        if live_data:
            # Use live data when available
            spot_price = live_data.current_price
            iv_rank_normalized = live_data.iv_rank
            earnings_days_away = live_data.earnings_days_away or 30
            expected_move = live_data.expected_move
            atr = live_data.atr_14
            support_level = live_data.support_level
            resistance_level = live_data.resistance_level
            iv_percentile = live_data.iv_percentile
        else:
            # Fallback to estimated data
            spot_price = self._get_current_price(symbol)
            iv_rank_normalized = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
            earnings_days_away = stock_factors.earnings_days_away or 30
            expected_move = stock_factors.earnings_move_estimate * spot_price
            atr = spot_price * 0.02
            support_level = spot_price * 0.95
            resistance_level = spot_price * 1.05
            iv_percentile = getattr(stock_factors, 'iv_percentile', iv_rank_normalized)

        return EricaSignals(
            spot_price=spot_price,
            earnings_days_away=earnings_days_away,
            iv_rank=iv_rank_normalized * 100 if iv_rank_normalized <= 1.0 else iv_rank_normalized,
            iv_percentile=iv_percentile * 100 if iv_percentile <= 1.0 else iv_percentile,
            expected_move=expected_move,
            atr=atr,
            support_level=support_level,
            resistance_level=resistance_level
        )

    def _get_current_price(self, symbol: str) -> float:
        """Get current stock price (simplified implementation)"""
        # In a real implementation, this would fetch from market data API
        # For demo purposes, use reasonable stock prices
        demo_prices = {
            "AAPL": 175.00,
            "NVDA": 450.00,
            "AMD": 110.00,
            "GOOGL": 140.00,
            "AMZN": 145.00,
            "TSLA": 250.00,
            "MSFT": 380.00
        }
        return demo_prices.get(symbol, 100.00)  # Default to $100

    def _determine_market_bias(self, market_factors: MarketFactors) -> MarketBias:
        """Determine market bias from market factors"""
        if market_factors.market_regime.value == "bull_market":
            return MarketBias.BULLISH
        elif market_factors.market_regime.value == "bear_market":
            return MarketBias.BEARISH
        else:
            return MarketBias.NEUTRAL

    def _map_erica_to_strategy_type(self, erica_strategy) -> StrategyType:
        """Map Erica's strategy enum to system strategy type"""
        strategy_mapping = {
            "BASELINE_CC": StrategyType.COVERED_CALL,
            "FAST_MONEY_CC": StrategyType.COVERED_CALL,
            "EARNINGS_CC": StrategyType.COVERED_CALL,
            "PUT_CREDIT_SPREAD": StrategyType.CREDIT_SPREAD,
            "LEAPS": StrategyType.LEAPS,
            "WHEEL": StrategyType.PREMIUM_SELLING
        }
        return strategy_mapping.get(erica_strategy.value, StrategyType.COVERED_CALL)

    def _calculate_confidence_level(self, confidence_score: float) -> ConfidenceLevel:
        """Convert confidence score to confidence level"""
        if confidence_score >= 0.90:
            return ConfidenceLevel.VERY_HIGH
        elif confidence_score >= 0.75:
            return ConfidenceLevel.HIGH
        elif confidence_score >= 0.60:
            return ConfidenceLevel.MODERATE
        elif confidence_score >= 0.40:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW

    def _analyze_criteria_matches(self, strategy: StrategyType, symbol: str,
                                 market_factors: MarketFactors, stock_factors: StockSpecificFactors,
                                 erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze how well the stock matches strategy criteria"""
        matches = []

        if strategy == StrategyType.COVERED_CALL:
            matches.extend(self._analyze_covered_call_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.CREDIT_SPREAD:
            matches.extend(self._analyze_credit_spread_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.LEAPS:
            matches.extend(self._analyze_leaps_criteria(symbol, market_factors, stock_factors, erica_setup))
        elif strategy == StrategyType.PREMIUM_SELLING:
            matches.extend(self._analyze_premium_selling_criteria(symbol, market_factors, stock_factors, erica_setup))

        return matches

    def _analyze_covered_call_criteria(self, symbol: str, market_factors: MarketFactors,
                                     stock_factors: StockSpecificFactors,
                                     erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze covered call specific criteria"""
        matches = []
        criteria = self.strategy_criteria["covered_call"]

        # IV Rank Check (fallback to 0.5 if specific rule missing)
        erica_rules = criteria.get("erica_specific_rules", {"iv_rank_minimum": 0.5})
        iv_rank_required = erica_rules.get("iv_rank_minimum", 0.5)
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank Minimum",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation=f"Erica requires minimum {iv_rank_required:.0%} IV rank for premium collection. Current IV rank is {iv_rank_actual:.0%}."
        ))

        # Stock-specific rules if available
        if symbol in STOCK_RULES:
            stock_rules = STOCK_RULES[symbol]

            # Delta range check
            target_delta = 0.30  # Default from Erica's methodology
            matches.append(StrategyCriteriaMatch(
                criterion_name="Target Delta Range",
                required_value=f"{stock_rules.cc_delta_range[0]:.2f} - {stock_rules.cc_delta_range[1]:.2f}",
                actual_value=f"Target: {target_delta:.2f}",
                is_met=stock_rules.cc_delta_range[0] <= target_delta <= stock_rules.cc_delta_range[1],
                score=1.0 if stock_rules.cc_delta_range[0] <= target_delta <= stock_rules.cc_delta_range[1] else 0.7,
                importance="important",
                explanation=f"{symbol}-specific delta range is {stock_rules.cc_delta_range[0]:.2f}-{stock_rules.cc_delta_range[1]:.2f}"
            ))

            # DTE range check
            matches.append(StrategyCriteriaMatch(
                criterion_name="Days to Expiration",
                required_value=f"{stock_rules.cc_preferred_dte[0]}-{stock_rules.cc_preferred_dte[1]} days",
                actual_value="Target range met",
                is_met=True,
                score=1.0,
                importance="important",
                explanation=f"{symbol} preferred DTE range: {stock_rules.cc_preferred_dte[0]}-{stock_rules.cc_preferred_dte[1]} days"
            ))

        # Premium requirement
        min_premium = 0.30  # $30 minimum per Erica's rules
        current_price = self._get_current_price(symbol)
        estimated_premium = current_price * 0.02  # Rough estimate
        matches.append(StrategyCriteriaMatch(
            criterion_name="Minimum Premium",
            required_value=f">= ${min_premium:.2f}",
            actual_value=f"~${estimated_premium:.2f} (estimated)",
            is_met=estimated_premium >= min_premium,
            score=min(estimated_premium / min_premium, 1.0) if min_premium > 0 else 1.0,
            importance="important",
            explanation=f"Erica requires minimum ${min_premium:.2f} premium per contract for income generation"
        ))

        return matches

    def _analyze_credit_spread_criteria(self, symbol: str, market_factors: MarketFactors,
                                      stock_factors: StockSpecificFactors,
                                      erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze put credit spread specific criteria"""
        matches = []
        criteria = self.strategy_criteria["put_credit_spread"]

        # Market bias requirement
        market_bias = self._determine_market_bias(market_factors)
        is_bullish_neutral = market_bias in [MarketBias.BULLISH, MarketBias.NEUTRAL]
        matches.append(StrategyCriteriaMatch(
            criterion_name="Market Bias",
            required_value="Bullish or Neutral",
            actual_value=market_bias.value.title(),
            is_met=is_bullish_neutral,
            score=1.0 if is_bullish_neutral else 0.3,
            importance="critical",
            explanation="Put credit spreads require bullish or neutral market conditions for optimal performance"
        ))

        # IV Rank for credit spreads
        iv_rank_required = 0.60  # 60th percentile for credit spreads
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank for Credit Collection",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation=f"Credit spreads need elevated IV (>={iv_rank_required:.0%}) for attractive premium collection"
        ))

        # Support level analysis
        current_price = self._get_current_price(symbol)
        support_level = current_price * 0.95  # Estimate support 5% below current price
        distance_from_support = (current_price - support_level) / current_price
        is_above_support = distance_from_support > 0.05  # 5% buffer

        matches.append(StrategyCriteriaMatch(
            criterion_name="Distance from Support",
            required_value="> 5% above support",
            actual_value=f"{distance_from_support:.1%} above support",
            is_met=is_above_support,
            score=min(distance_from_support / 0.05, 1.0) if distance_from_support > 0 else 0.0,
            importance="important",
            explanation=f"Stock should be well above support levels. Currently {distance_from_support:.1%} above support at ${support_level:.2f}"
        ))

        return matches

    def _analyze_leaps_criteria(self, symbol: str, market_factors: MarketFactors,
                               stock_factors: StockSpecificFactors,
                               erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze LEAPS specific criteria"""
        matches = []

        # Long-term trend requirement
        trend_strength = getattr(stock_factors, 'trend_strength', 0.6)  # Assume moderate trend
        matches.append(StrategyCriteriaMatch(
            criterion_name="Strong Long-term Trend",
            required_value="Strong uptrend required",
            actual_value=f"Trend strength: {trend_strength:.1%}",
            is_met=trend_strength > 0.7,
            score=trend_strength,
            importance="critical",
            explanation="LEAPS require strong long-term uptrend for leveraged growth exposure"
        ))

        # IV Rank (prefer lower for buying options)
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        iv_rank_preferred_max = 0.60  # Prefer lower IV when buying
        matches.append(StrategyCriteriaMatch(
            criterion_name="IV Rank (Lower Preferred)",
            required_value=f"< {iv_rank_preferred_max:.0%} preferred",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual < iv_rank_preferred_max,
            score=max(0.2, 1.0 - (iv_rank_actual / iv_rank_preferred_max)),
            importance="important",
            explanation="Lower IV preferred when buying LEAPS to reduce premium cost"
        ))

        return matches

    def _analyze_premium_selling_criteria(self, symbol: str, market_factors: MarketFactors,
                                        stock_factors: StockSpecificFactors,
                                        erica_setup: Optional[EricaTradeSetup]) -> List[StrategyCriteriaMatch]:
        """Analyze premium selling (wheel) criteria"""
        matches = []

        # High IV requirement for premium selling
        iv_rank_required = 0.70  # 70th percentile for systematic premium selling
        iv_rank_actual = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100
        matches.append(StrategyCriteriaMatch(
            criterion_name="High IV for Premium Collection",
            required_value=f">= {iv_rank_required:.0%}",
            actual_value=f"{iv_rank_actual:.0%}",
            is_met=iv_rank_actual >= iv_rank_required,
            score=min(iv_rank_actual / iv_rank_required, 1.0) if iv_rank_required > 0 else 1.0,
            importance="critical",
            explanation="Systematic premium selling requires consistently high IV for attractive returns"
        ))

        # Stock quality for potential assignment
        matches.append(StrategyCriteriaMatch(
            criterion_name="Stock Quality for Assignment",
            required_value="High-quality stock acceptable for ownership",
            actual_value="Quality stock suitable for wheel strategy",
            is_met=True,  # Assume quality stocks in our universe
            score=0.9,
            importance="important",
            explanation="Must be comfortable owning the stock if assigned on puts"
        ))

        return matches

    def _identify_key_catalysts(self, symbol: str, market_factors: MarketFactors,
                               stock_factors: StockSpecificFactors,
                               strategy: StrategyType) -> List[CatalystAnalysis]:
        """Identify key catalysts driving the investment thesis"""
        catalysts = []

        # Earnings catalyst
        earnings_days = getattr(stock_factors, 'earnings_days_away', 30)
        if earnings_days <= 10:
            catalysts.append(CatalystAnalysis(
                catalyst_type="earnings",
                description=f"Earnings announcement in {earnings_days} days",
                impact_level="high",
                timeframe="immediate",
                probability=1.0,
                supporting_evidence=[
                    f"Earnings expected in {earnings_days} trading days",
                    f"Current IV rank: {stock_factors.iv_rank:.0%}",
                    "Elevated volatility expected around earnings"
                ]
            ))

        # Technical catalyst
        if hasattr(stock_factors, 'near_resistance') and stock_factors.near_resistance:
            catalysts.append(CatalystAnalysis(
                catalyst_type="technical",
                description="Approaching key resistance level",
                impact_level="medium",
                timeframe="short_term",
                probability=0.7,
                supporting_evidence=[
                    f"Current price: ${stock_factors.current_price:.2f}",
                    f"Resistance level: ${getattr(stock_factors, 'resistance_level', stock_factors.current_price * 1.05):.2f}",
                    "Technical breakout potential"
                ]
            ))

        # Market regime catalyst
        if market_factors.market_regime.value == "bull_market":
            catalysts.append(CatalystAnalysis(
                catalyst_type="market",
                description="Favorable bull market environment",
                impact_level="medium",
                timeframe="medium_term",
                probability=0.8,
                supporting_evidence=[
                    "Bull market regime detected",
                    "Positive market momentum",
                    "Risk-on sentiment supporting growth stocks"
                ]
            ))

        return catalysts

    def _generate_investment_thesis(self, symbol: str, strategy: StrategyType,
                                   catalysts: List[CatalystAnalysis],
                                   criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Generate comprehensive investment thesis"""

        # Count criteria that are met
        met_criteria = sum(1 for match in criteria_matches if match.is_met)
        total_criteria = len(criteria_matches)

        # Strategy-specific thesis
        strategy_thesis = {
            StrategyType.COVERED_CALL: f"{symbol} is well-suited for covered call income generation due to elevated implied volatility and stable price action. The strategy allows for premium collection while maintaining upside participation.",
            StrategyType.CREDIT_SPREAD: f"{symbol} presents an attractive put credit spread opportunity with the stock trading above key support levels in a favorable market environment. The elevated IV provides attractive premium collection potential.",
            StrategyType.LEAPS: f"{symbol} shows strong long-term growth potential making it suitable for LEAPS strategy. The leveraged exposure allows for participation in the stock's growth trajectory with defined risk.",
            StrategyType.PREMIUM_SELLING: f"{symbol} is ideal for systematic premium selling (wheel strategy) due to consistently high implied volatility and stock quality suitable for potential ownership."
        }

        base_thesis = strategy_thesis.get(strategy, f"{symbol} fits the selected strategy based on current market conditions.")

        # Add catalyst support
        catalyst_support = ""
        if catalysts:
            high_impact_catalysts = [c for c in catalysts if c.impact_level == "high"]
            if high_impact_catalysts:
                catalyst_support = f" Key catalysts include {', '.join([c.description.lower() for c in high_impact_catalysts])}."

        # Add criteria confidence
        criteria_confidence = f" The analysis shows {met_criteria}/{total_criteria} key criteria are met, providing {'strong' if met_criteria/total_criteria > 0.8 else 'moderate' if met_criteria/total_criteria > 0.6 else 'limited'} confidence in the strategy selection."

        return base_thesis + catalyst_support + criteria_confidence

    def _get_financial_projections(self, symbol: str) -> FinancialProjections:
        """Get financial analyst projections (mock implementation)"""
        # In a real implementation, this would fetch from financial data APIs
        return FinancialProjections(
            price_target_mean=None,
            price_target_high=None,
            price_target_low=None,
            analyst_rating="Hold",
            eps_estimate_current=None,
            eps_estimate_next=None,
            revenue_growth_estimate=None,
            analyst_count=0,
            upgrade_downgrade_trend="Neutral",
            key_analyst_notes=["Financial projections would be fetched from data provider"]
        )

    def _generate_supporting_metrics(self, symbol: str, market_factors: MarketFactors,
                                   stock_factors: StockSpecificFactors) -> SupportingMetrics:
        """Generate comprehensive supporting metrics"""
        current_price = self._get_current_price(symbol)
        iv_rank_normalized = stock_factors.iv_rank if stock_factors.iv_rank <= 1.0 else stock_factors.iv_rank / 100

        return SupportingMetrics(
            technical_indicators={
                "current_price": f"${current_price:.2f}",
                "iv_rank": f"{iv_rank_normalized:.0%}",
                "trend_alignment": stock_factors.trend_alignment,
                "technical_confluence": f"{stock_factors.technical_confluence_score:.1%}",
                "relative_strength": f"{stock_factors.relative_strength_vs_spy:.1%}"
            },
            fundamental_metrics={
                "earnings_days_away": stock_factors.earnings_days_away or "N/A",
                "earnings_move_estimate": f"{stock_factors.earnings_move_estimate:.1%}",
                "news_sentiment": f"{stock_factors.news_sentiment_score:.2f}",
                "sector_performance": f"{stock_factors.sector_performance:.1%}"
            },
            options_metrics={
                "iv_rank": f"{iv_rank_normalized:.0%}",
                "iv_vs_hv_ratio": f"{stock_factors.iv_vs_hv_ratio:.2f}",
                "options_flow_sentiment": stock_factors.options_flow_sentiment,
                "unusual_activity": stock_factors.unusual_options_activity
            },
            market_metrics={
                "market_regime": market_factors.market_regime.value,
                "volatility_regime": market_factors.volatility_regime.value,
                "vix_level": f"{market_factors.vix_level:.1f}",
                "market_breadth": f"{market_factors.market_breadth:.2f}"
            },
            risk_metrics={
                "support_resistance_clarity": f"{stock_factors.support_resistance_clarity:.1%}",
                "earnings_proximity": "High" if stock_factors.earnings_days_away and stock_factors.earnings_days_away < 10 else "Low",
                "sector_rotation_impact": stock_factors.sector_rotation_impact
            },
            comparative_analysis={
                "sector_performance": f"Sector: {stock_factors.sector_performance:.1%}",
                "relative_strength_vs_spy": f"vs SPY: {stock_factors.relative_strength_vs_spy:.1%}",
                "technical_confluence": f"Technical: {stock_factors.technical_confluence_score:.1%}"
            }
        )

    def _get_alternative_strategies(self, strategy_rec: StrategyRecommendation,
                                   primary_strategy: StrategyType) -> List[Tuple[StrategyType, float, str]]:
        """Get alternative strategy recommendations"""
        alternatives = []
        for alt_strategy, score in strategy_rec.alternative_strategies:
            if alt_strategy != primary_strategy:
                reason = f"Alternative with {score:.1%} confidence based on current market conditions"
                alternatives.append((alt_strategy, score, reason))
        return alternatives[:3]  # Top 3 alternatives

    def _generate_entry_criteria(self, strategy: StrategyType, symbol: str,
                               erica_setup: Optional[EricaTradeSetup]) -> List[str]:
        """Generate specific entry criteria"""
        if erica_setup:
            return [
                f"Target delta: {erica_setup.target_delta:.2f}",
                f"Days to expiration: {erica_setup.dte}",
                f"Strike price: ${erica_setup.short_strike:.2f}" if erica_setup.short_strike else "Strike TBD",
                "Confirm IV rank above minimum threshold",
                "Verify market conditions remain favorable"
            ]

        # Default criteria by strategy
        criteria_map = {
            StrategyType.COVERED_CALL: [
                "Own 100 shares of stock",
                "IV rank > 50th percentile",
                "Target 0.30 delta call option",
                "30-45 DTE preferred",
                "Minimum $30 premium target"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Bullish/neutral market bias confirmed",
                "Stock above key support levels",
                "IV rank > 60th percentile",
                "Target 0.15-0.20 delta short put",
                "30-45 DTE preferred"
            ],
            StrategyType.LEAPS: [
                "Strong long-term uptrend confirmed",
                "IV rank < 60th percentile preferred",
                "Target 0.70-0.80 delta",
                "12+ months to expiration",
                "Fundamental growth story intact"
            ],
            StrategyType.PREMIUM_SELLING: [
                "IV rank > 70th percentile",
                "Comfortable owning the stock",
                "Systematic approach to strike selection",
                "Consistent premium collection opportunity"
            ]
        }

        return criteria_map.get(strategy, ["Standard entry criteria apply"])

    def _generate_exit_criteria(self, strategy: StrategyType, symbol: str,
                              erica_setup: Optional[EricaTradeSetup]) -> List[str]:
        """Generate specific exit criteria"""
        if erica_setup:
            return [
                f"Profit target: {erica_setup.profit_target_pct:.0%} of max profit",
                f"Time exit: {erica_setup.time_exit_dte} DTE if unclear",
                f"Roll trigger: {erica_setup.roll_trigger_delta:.2f} delta breach",
                "Monitor for early assignment risk",
                "Adjust based on changing market conditions"
            ]

        # Default exit criteria by strategy
        criteria_map = {
            StrategyType.COVERED_CALL: [
                "Close at 50% of max profit",
                "Roll at 21 DTE if unprofitable",
                "Manage if delta exceeds 0.35",
                "Consider early assignment risk"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Close at 50% of max profit",
                "Exit by 5 DTE if unclear",
                "Roll if breaching support",
                "Monitor for adverse market changes"
            ],
            StrategyType.LEAPS: [
                "Hold for 6-12 months typically",
                "Exit if fundamental thesis changes",
                "Take profits on significant moves",
                "Monitor time decay acceleration"
            ],
            StrategyType.PREMIUM_SELLING: [
                "Systematic profit taking at 50-70%",
                "Roll challenged positions",
                "Accept assignment if favorable",
                "Maintain consistent approach"
            ]
        }

        return criteria_map.get(strategy, ["Standard exit criteria apply"])

    def _calculate_position_sizing(self, strategy: StrategyType, symbol: str,
                                 market_factors: MarketFactors,
                                 stock_factors: StockSpecificFactors) -> Dict[str, Any]:
        """Calculate appropriate position sizing"""
        base_size = 1.0  # Base position multiplier

        # Adjust for volatility
        volatility = getattr(stock_factors, 'volatility', 0.25)
        if volatility > 0.35:
            volatility_adjustment = 0.7  # Reduce size for high volatility
        elif volatility < 0.15:
            volatility_adjustment = 1.2  # Increase size for low volatility
        else:
            volatility_adjustment = 1.0

        # Adjust for market conditions
        market_adjustment = 1.0
        if market_factors.market_regime.value == "bear_market":
            market_adjustment = 0.8
        elif market_factors.market_regime.value == "bull_market":
            market_adjustment = 1.1

        final_multiplier = base_size * volatility_adjustment * market_adjustment

        return {
            "base_multiplier": base_size,
            "volatility_adjustment": volatility_adjustment,
            "market_adjustment": market_adjustment,
            "final_multiplier": final_multiplier,
            "recommended_contracts": max(1, int(final_multiplier)),
            "risk_considerations": [
                f"Volatility: {volatility:.1%}",
                f"Market regime: {market_factors.market_regime.value}",
                "Position size adjusted for current conditions"
            ]
        }

    def _generate_timing_considerations(self, strategy: StrategyType, symbol: str,
                                      market_factors: MarketFactors) -> List[str]:
        """Generate timing considerations for strategy execution"""
        considerations = []

        # Market timing
        if market_factors.market_regime.value == "bull_market":
            considerations.append("Favorable bull market environment supports strategy execution")
        elif market_factors.market_regime.value == "bear_market":
            considerations.append("Bear market conditions require defensive positioning")

        # Volatility timing
        if market_factors.volatility_regime.value == "high_vol":
            considerations.append("High volatility environment - favorable for premium selling")
        elif market_factors.volatility_regime.value == "low_vol":
            considerations.append("Low volatility environment - consider premium buying strategies")

        # Strategy-specific timing
        strategy_timing = {
            StrategyType.COVERED_CALL: [
                "Best executed when IV rank > 50th percentile",
                "Avoid right before earnings unless specifically targeting IV crush"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Execute when stock is above support levels",
                "Time entry with bullish market momentum"
            ],
            StrategyType.LEAPS: [
                "Best purchased during low IV periods",
                "Time entry with strong fundamental catalysts"
            ],
            StrategyType.PREMIUM_SELLING: [
                "Systematic execution regardless of market timing",
                "Increase frequency during high IV periods"
            ]
        }

        considerations.extend(strategy_timing.get(strategy, []))
        return considerations

    def _describe_market_environment(self, market_factors: MarketFactors) -> str:
        """Describe current market environment"""
        regime = market_factors.market_regime.value.replace('_', ' ').title()
        vol_regime = market_factors.volatility_regime.value.replace('_', ' ').title()

        return f"{regime} market with {vol_regime} volatility regime. " \
               f"Market conditions are {'favorable' if regime == 'Bull Market' else 'challenging' if regime == 'Bear Market' else 'neutral'} " \
               f"for most options strategies."

    def _analyze_sector_context(self, symbol: str, market_factors: MarketFactors) -> str:
        """Analyze sector context (simplified implementation)"""
        # In a real implementation, this would analyze sector-specific factors
        return f"{symbol} operates in a sector that is currently showing " \
               f"{'strong' if market_factors.market_regime.value == 'bull_market' else 'mixed'} performance " \
               f"relative to the broader market."

    def _analyze_relative_strength(self, symbol: str, stock_factors: StockSpecificFactors) -> str:
        """Analyze relative strength"""
        # Simplified relative strength analysis
        beta = getattr(stock_factors, 'beta', 1.0)
        if beta > 1.2:
            return f"{symbol} shows high beta ({beta:.2f}), indicating higher volatility than market"
        elif beta < 0.8:
            return f"{symbol} shows low beta ({beta:.2f}), indicating lower volatility than market"
        else:
            return f"{symbol} shows moderate beta ({beta:.2f}), moving roughly in line with market"

    def _get_erica_specific_rules(self, symbol: str) -> Dict[str, Any]:
        """Get Erica's specific rules for the symbol"""
        if symbol in STOCK_RULES:
            rules = STOCK_RULES[symbol]
            return {
                "cc_preferred_dte": rules.cc_preferred_dte,
                "cc_delta_range": rules.cc_delta_range,
                "cc_earnings_adjustment": rules.cc_earnings_adjustment,
                "cc_frequency": rules.cc_frequency,
                "pcs_width_points": rules.pcs_width_points,
                "pcs_support_buffer": rules.pcs_support_buffer,
                "leaps_preferred_timeframe": rules.leaps_preferred_timeframe,
                "wheel_frequency": rules.wheel_frequency
            }
        return {"note": f"No specific rules defined for {symbol}, using standard Erica methodology"}

    def _generate_criteria_summary(self, criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Generate summary of criteria matching"""
        total = len(criteria_matches)
        met = sum(1 for match in criteria_matches if match.is_met)
        critical_met = sum(1 for match in criteria_matches if match.is_met and match.importance == "critical")
        critical_total = sum(1 for match in criteria_matches if match.importance == "critical")

        if critical_total > 0 and critical_met == critical_total:
            confidence = "High"
        elif met / total > 0.8:
            confidence = "Strong"
        elif met / total > 0.6:
            confidence = "Moderate"
        else:
            confidence = "Limited"

        return f"{confidence} criteria match: {met}/{total} criteria met " \
               f"({critical_met}/{critical_total} critical criteria met)"

    def _explain_strategy_selection(self, strategy: StrategyType, symbol: str,
                                   criteria_matches: List[StrategyCriteriaMatch]) -> str:
        """Explain why this strategy was selected"""
        met_criteria = [match for match in criteria_matches if match.is_met]
        key_reasons = [match.explanation for match in met_criteria if match.importance in ["critical", "important"]]

        base_explanation = f"{symbol} is classified as {strategy.value.replace('_', ' ').title()} because "

        if key_reasons:
            reasons_text = ". ".join(key_reasons[:3])  # Top 3 reasons
            return base_explanation + reasons_text.lower() + "."
        else:
            return base_explanation + "it meets the basic requirements for this strategy type."

    def _determine_analyst_sentiment(self, projections: FinancialProjections) -> str:
        """Determine overall analyst sentiment"""
        if projections.analyst_rating:
            rating = projections.analyst_rating.lower()
            if "strong buy" in rating or "buy" in rating:
                return "Bullish"
            elif "sell" in rating:
                return "Bearish"
            else:
                return "Neutral"
        return "No analyst coverage available"

    def _generate_comprehensive_catalyst_analysis(self, symbol: str, strategy: StrategyType,
                                                market_factors: MarketFactors, stock_factors: StockSpecificFactors,
                                                live_data=None, field_guide_setup=None) -> ComprehensiveCatalystAnalysis:
        """Generate comprehensive catalyst analysis with all four components"""

        # 1. Strategy Justification
        strategy_justification = self._generate_strategy_justification(
            symbol, strategy, market_factors, stock_factors, live_data, field_guide_setup
        )

        # 2. Bullish Catalysts
        bullish_catalysts = self._identify_bullish_catalysts(symbol, live_data, stock_factors)

        # 3. Risk Factors
        risk_factors = self._identify_comprehensive_risk_factors(symbol, live_data, stock_factors)

        # 4. Analyst Sentiment
        analyst_sentiment = self._get_analyst_sentiment_data(symbol, live_data)

        # Overall outlook
        overall_outlook = self._determine_overall_outlook(bullish_catalysts, risk_factors, analyst_sentiment)

        return ComprehensiveCatalystAnalysis(
            symbol=symbol,
            analysis_date=datetime.now(),
            strategy_justification=strategy_justification,
            bullish_catalysts=bullish_catalysts,
            risk_factors=risk_factors,
            analyst_sentiment=analyst_sentiment,
            overall_outlook=overall_outlook
        )

    def _generate_strategy_justification(self, symbol: str, strategy: StrategyType,
                                       market_factors: MarketFactors, stock_factors: StockSpecificFactors,
                                       live_data=None, field_guide_setup=None) -> StrategyJustification:
        """Generate detailed strategy justification"""

        if live_data:
            iv_rank = live_data.iv_rank
            current_price = live_data.current_price
            earnings_days = live_data.earnings_days_away
        else:
            iv_rank = stock_factors.iv_rank
            current_price = self._get_current_price(symbol)
            earnings_days = stock_factors.earnings_days_away

        # Determine primary reasoning based on strategy and conditions
        if strategy == StrategyType.COVERED_CALL:
            if iv_rank > 0.6:
                primary_reasoning = f"Covered Call selected due to elevated IV rank of {iv_rank:.0%}, ideal for premium collection"
                confidence = "HIGH"
            elif iv_rank > 0.4:
                primary_reasoning = f"Covered Call selected due to moderate IV rank of {iv_rank:.0%} and income generation focus"
                confidence = "MODERATE"
            else:
                primary_reasoning = f"Covered Call selected for systematic income despite lower IV rank of {iv_rank:.0%}"
                confidence = "MODERATE"
        elif strategy == StrategyType.CREDIT_SPREAD:
            primary_reasoning = f"Credit Spread selected due to bullish bias and IV rank of {iv_rank:.0%} supporting premium collection"
            confidence = "HIGH" if iv_rank > 0.6 else "MODERATE"
        elif strategy == StrategyType.LEAPS:
            primary_reasoning = f"LEAPS selected for long-term bullish exposure with IV rank of {iv_rank:.0%} providing reasonable entry"
            confidence = "HIGH" if iv_rank < 0.4 else "MODERATE"
        else:
            primary_reasoning = f"{strategy.value} selected based on current market conditions and {symbol} characteristics"
            confidence = "MODERATE"

        # Add earnings timing context
        if earnings_days and earnings_days <= 7:
            primary_reasoning += f" with earnings in {earnings_days} days requiring adjusted approach"
        elif earnings_days and earnings_days <= 0:
            primary_reasoning += " with post-earnings timing reducing volatility risk"

        supporting_factors = [
            f"Current IV rank: {iv_rank:.0%}",
            f"Stock price: ${current_price:.2f}",
            f"Market regime: {market_factors.market_regime.value}",
            f"Volatility regime: {market_factors.volatility_regime.value}"
        ]

        if earnings_days:
            supporting_factors.append(f"Earnings timing: {earnings_days} days away")

        market_conditions = {
            "iv_rank": iv_rank,
            "market_regime": market_factors.market_regime.value,
            "volatility_regime": market_factors.volatility_regime.value,
            "vix_level": market_factors.vix_level
        }

        erica_reference = self._get_erica_methodology_reference(strategy, field_guide_setup)

        return StrategyJustification(
            strategy_name=strategy.value.replace('_', ' ').title(),
            primary_reasoning=primary_reasoning,
            confidence_level=confidence,
            supporting_factors=supporting_factors,
            market_conditions=market_conditions,
            erica_methodology_reference=erica_reference
        )

    def _identify_bullish_catalysts(self, symbol: str, live_data=None, stock_factors=None) -> List[BullishCatalyst]:
        """Identify specific bullish catalysts for the stock"""
        catalysts = []

        # Get stock-specific catalysts based on symbol
        if symbol == "AAPL":
            catalysts.extend([
                BullishCatalyst(
                    catalyst_name="iPhone Cycle Strength",
                    description="Strong iPhone 15 Pro demand and upcoming iPhone 16 launch driving revenue growth",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Next 2 quarters",
                    supporting_data=["iPhone revenue up 6% YoY", "Pro model mix improving", "China demand stabilizing"]
                ),
                BullishCatalyst(
                    catalyst_name="Services Revenue Growth",
                    description="App Store and subscription services showing consistent double-digit growth",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Ongoing",
                    supporting_data=["Services margin >70%", "1B+ paid subscriptions", "Growing install base"]
                ),
                BullishCatalyst(
                    catalyst_name="AI Integration Momentum",
                    description="Apple Intelligence rollout driving upgrade cycle and premium positioning",
                    impact_assessment="Medium",
                    catalyst_type="Event-driven",
                    timeframe="Next 12 months",
                    supporting_data=["iOS 18 AI features", "On-device processing advantage", "Privacy-first AI approach"]
                )
            ])

        elif symbol == "NVDA":
            catalysts.extend([
                BullishCatalyst(
                    catalyst_name="AI Data Center Demand",
                    description="Explosive growth in AI infrastructure spending driving H100/H200 demand",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Next 2-3 years",
                    supporting_data=["Data center revenue up 400%+ YoY", "H100 supply constraints", "Enterprise AI adoption accelerating"]
                ),
                BullishCatalyst(
                    catalyst_name="Blackwell Architecture Launch",
                    description="Next-gen B100/B200 chips offering 5x performance improvement over H100",
                    impact_assessment="High",
                    catalyst_type="Event-driven",
                    timeframe="Q4 2024 - Q1 2025",
                    supporting_data=["Major cloud providers pre-ordering", "Improved performance per watt", "Manufacturing ramp beginning"]
                ),
                BullishCatalyst(
                    catalyst_name="Software and Services Expansion",
                    description="CUDA ecosystem and AI software services creating recurring revenue streams",
                    impact_assessment="Medium",
                    catalyst_type="Fundamental",
                    timeframe="Long-term",
                    supporting_data=["CUDA moat strengthening", "Omniverse adoption", "AI software licensing growth"]
                )
            ])

        elif symbol == "AMD":
            catalysts.extend([
                BullishCatalyst(
                    catalyst_name="Data Center CPU Market Share Gains",
                    description="EPYC processors continuing to take share from Intel in server market",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Next 2 quarters",
                    supporting_data=["EPYC revenue up 80%+ YoY", "Cloud provider adoption", "Performance per dollar advantage"]
                ),
                BullishCatalyst(
                    catalyst_name="AI Accelerator Competition",
                    description="MI300X and Instinct series positioning AMD as NVIDIA alternative",
                    impact_assessment="Medium",
                    catalyst_type="Event-driven",
                    timeframe="2024-2025",
                    supporting_data=["Microsoft, Meta partnerships", "ROCm software improvements", "Cost advantage vs NVIDIA"]
                )
            ])

        elif symbol == "GOOGL":
            catalysts.extend([
                BullishCatalyst(
                    catalyst_name="Cloud Growth Acceleration",
                    description="Google Cloud Platform showing strong growth and margin improvement",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Next 2 quarters",
                    supporting_data=["Cloud revenue up 35%+ YoY", "Operating margin improving", "AI services driving adoption"]
                ),
                BullishCatalyst(
                    catalyst_name="AI Search Integration",
                    description="Gemini AI integration into search maintaining competitive moat",
                    impact_assessment="Medium",
                    catalyst_type="Event-driven",
                    timeframe="Next 12 months",
                    supporting_data=["Search Generative Experience rollout", "AI Overviews deployment", "User engagement metrics positive"]
                )
            ])

        elif symbol == "AMZN":
            catalysts.extend([
                BullishCatalyst(
                    catalyst_name="AWS Reacceleration",
                    description="Amazon Web Services showing signs of growth reacceleration after slowdown",
                    impact_assessment="High",
                    catalyst_type="Fundamental",
                    timeframe="Next 2 quarters",
                    supporting_data=["Enterprise spending normalizing", "AI services driving demand", "Cost optimization cycle ending"]
                ),
                BullishCatalyst(
                    catalyst_name="Advertising Revenue Growth",
                    description="Amazon's advertising business becoming major profit driver",
                    impact_assessment="Medium",
                    catalyst_type="Fundamental",
                    timeframe="Ongoing",
                    supporting_data=["Ad revenue up 20%+ YoY", "High margin business", "Prime Video ad tier launch"]
                )
            ])

        # Add technical catalysts if available
        if live_data:
            if live_data.relative_strength_spy > 0.02:  # Outperforming SPY by >2%
                catalysts.append(
                    BullishCatalyst(
                        catalyst_name="Relative Strength vs Market",
                        description=f"Stock showing relative strength vs SPY (+{live_data.relative_strength_spy:.1%})",
                        impact_assessment="Medium",
                        catalyst_type="Technical",
                        timeframe="Short-term",
                        supporting_data=[f"Beta: {live_data.beta:.2f}", f"Recent outperformance: +{live_data.relative_strength_spy:.1%}"]
                    )
                )

            if live_data.current_price > live_data.support_level * 1.05:  # Well above support
                catalysts.append(
                    BullishCatalyst(
                        catalyst_name="Technical Support Holding",
                        description=f"Price well above key support level at ${live_data.support_level:.2f}",
                        impact_assessment="Low",
                        catalyst_type="Technical",
                        timeframe="Short-term",
                        supporting_data=[f"Current: ${live_data.current_price:.2f}", f"Support: ${live_data.support_level:.2f}"]
                    )
                )

        return catalysts[:3]  # Return top 3 catalysts

    def _identify_comprehensive_risk_factors(self, symbol: str, live_data=None, stock_factors=None) -> List[RiskFactor]:
        """Identify comprehensive risk factors for the stock"""
        risks = []

        # Get stock-specific risks
        if symbol == "AAPL":
            risks.extend([
                RiskFactor(
                    risk_name="China Market Dependency",
                    description="Significant revenue exposure to China market amid geopolitical tensions",
                    severity="High",
                    risk_type="Regulatory",
                    mitigation_strategy="Diversifying supply chain and market presence",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="iPhone Saturation Risk",
                    description="Smartphone market maturity could limit iPhone growth potential",
                    severity="Medium",
                    risk_type="Market",
                    mitigation_strategy="Focus on services revenue and emerging markets",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="Regulatory Scrutiny",
                    description="App Store policies under regulatory pressure in EU and US",
                    severity="Medium",
                    risk_type="Regulatory",
                    mitigation_strategy="Compliance with DMA and potential policy adjustments",
                    probability="High"
                )
            ])

        elif symbol == "NVDA":
            risks.extend([
                RiskFactor(
                    risk_name="China Export Restrictions",
                    description="US government restrictions on AI chip exports to China limiting market",
                    severity="High",
                    risk_type="Regulatory",
                    mitigation_strategy="Developing China-specific compliant chips",
                    probability="High"
                ),
                RiskFactor(
                    risk_name="AI Bubble Concerns",
                    description="Potential overinvestment in AI infrastructure could lead to demand correction",
                    severity="High",
                    risk_type="Market",
                    mitigation_strategy="Diversifying into automotive, gaming, and other verticals",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="Competitive Pressure",
                    description="AMD, Intel, and custom chips from cloud providers increasing competition",
                    severity="Medium",
                    risk_type="Competitive",
                    mitigation_strategy="Maintaining CUDA ecosystem moat and performance leadership",
                    probability="Medium"
                )
            ])

        elif symbol == "AMD":
            risks.extend([
                RiskFactor(
                    risk_name="Intel Competitive Response",
                    description="Intel's aggressive pricing and new architectures could slow AMD's share gains",
                    severity="Medium",
                    risk_type="Competitive",
                    mitigation_strategy="Continued innovation and cost leadership",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="AI Market Execution Risk",
                    description="Falling behind NVIDIA in AI accelerator market could limit growth",
                    severity="High",
                    risk_type="Competitive",
                    mitigation_strategy="Investing heavily in MI300 series and software ecosystem",
                    probability="Medium"
                )
            ])

        elif symbol == "GOOGL":
            risks.extend([
                RiskFactor(
                    risk_name="Search Disruption Risk",
                    description="AI chatbots and alternative search methods could disrupt core search business",
                    severity="High",
                    risk_type="Competitive",
                    mitigation_strategy="Integrating AI into search and developing new AI products",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="Regulatory Antitrust Action",
                    description="DOJ antitrust case could force business model changes",
                    severity="High",
                    risk_type="Regulatory",
                    mitigation_strategy="Legal defense and potential business structure adjustments",
                    probability="High"
                )
            ])

        elif symbol == "AMZN":
            risks.extend([
                RiskFactor(
                    risk_name="AWS Growth Deceleration",
                    description="Cloud growth slowdown could impact high-margin revenue stream",
                    severity="High",
                    risk_type="Market",
                    mitigation_strategy="AI services and cost optimization to drive reacceleration",
                    probability="Medium"
                ),
                RiskFactor(
                    risk_name="Retail Margin Pressure",
                    description="Competitive pressure and fulfillment costs limiting retail profitability",
                    severity="Medium",
                    risk_type="Competitive",
                    mitigation_strategy="Automation and Prime membership growth",
                    probability="Medium"
                )
            ])

        # Add technical/timing risks
        if live_data:
            if live_data.earnings_days_away and live_data.earnings_days_away <= 7:
                risks.append(
                    RiskFactor(
                        risk_name="Earnings Volatility Risk",
                        description=f"Earnings announcement in {live_data.earnings_days_away} days could create significant price volatility",
                        severity="Medium",
                        risk_type="Technical",
                        mitigation_strategy="Adjust position sizing and consider post-earnings strategies",
                        probability="High"
                    )
                )

            if live_data.iv_rank > 0.8:
                risks.append(
                    RiskFactor(
                        risk_name="IV Crush Risk",
                        description=f"Elevated IV rank of {live_data.iv_rank:.0%} suggests potential volatility compression",
                        severity="Medium",
                        risk_type="Technical",
                        mitigation_strategy="Focus on premium selling strategies and shorter timeframes",
                        probability="Medium"
                    )
                )

        return risks[:3]  # Return top 3 risks

    def _get_analyst_sentiment_data(self, symbol: str, live_data=None) -> AnalystSentiment:
        """Get analyst sentiment and price target data"""

        # Stock-specific analyst data (would be fetched from real API in production)
        analyst_data = {
            "AAPL": {
                "consensus": "Buy",
                "avg_target": 240.00,
                "analysts": 35,
                "target_range": (200.00, 275.00),
                "recent_changes": ["Morgan Stanley upgrade to Overweight", "Wedbush raises target to $275"]
            },
            "NVDA": {
                "consensus": "Strong Buy",
                "avg_target": 850.00,
                "analysts": 42,
                "target_range": (600.00, 1200.00),
                "recent_changes": ["Goldman Sachs raises target to $1,100", "JPMorgan maintains Overweight"]
            },
            "AMD": {
                "consensus": "Buy",
                "avg_target": 190.00,
                "analysts": 28,
                "target_range": (140.00, 220.00),
                "recent_changes": ["Bank of America upgrade to Buy", "Mizuho raises target to $200"]
            },
            "GOOGL": {
                "consensus": "Buy",
                "avg_target": 175.00,
                "analysts": 38,
                "target_range": (150.00, 200.00),
                "recent_changes": ["Jefferies maintains Buy rating", "Oppenheimer target $180"]
            },
            "AMZN": {
                "consensus": "Buy",
                "avg_target": 200.00,
                "analysts": 40,
                "target_range": (170.00, 230.00),
                "recent_changes": ["Deutsche Bank raises target to $215", "Evercore ISI maintains Outperform"]
            }
        }

        data = analyst_data.get(symbol, {
            "consensus": "Hold",
            "avg_target": 150.00,
            "analysts": 20,
            "target_range": (120.00, 180.00),
            "recent_changes": ["Limited recent analyst activity"]
        })

        current_price = live_data.current_price if live_data else self._get_current_price(symbol)
        upside_downside = ((data["avg_target"] - current_price) / current_price) * 100

        return AnalystSentiment(
            consensus_rating=data["consensus"],
            average_price_target=data["avg_target"],
            upside_downside_percent=upside_downside,
            number_of_analysts=data["analysts"],
            recent_changes=data["recent_changes"],
            target_range=data["target_range"]
        )

    def _determine_overall_outlook(self, bullish_catalysts: List[BullishCatalyst],
                                 risk_factors: List[RiskFactor],
                                 analyst_sentiment: AnalystSentiment) -> str:
        """Determine overall outlook based on catalysts, risks, and analyst sentiment"""

        # Score bullish catalysts
        bullish_score = 0
        for catalyst in bullish_catalysts:
            if catalyst.impact_assessment == "High":
                bullish_score += 3
            elif catalyst.impact_assessment == "Medium":
                bullish_score += 2
            else:
                bullish_score += 1

        # Score risk factors (negative)
        risk_score = 0
        for risk in risk_factors:
            if risk.severity == "High":
                risk_score += 3
            elif risk.severity == "Medium":
                risk_score += 2
            else:
                risk_score += 1

        # Score analyst sentiment
        analyst_score = 0
        if "Strong Buy" in analyst_sentiment.consensus_rating:
            analyst_score = 3
        elif "Buy" in analyst_sentiment.consensus_rating:
            analyst_score = 2
        elif "Hold" in analyst_sentiment.consensus_rating:
            analyst_score = 1

        # Calculate net score
        net_score = bullish_score + analyst_score - risk_score

        if net_score >= 6:
            return "BULLISH - Strong positive catalysts outweigh risks"
        elif net_score >= 3:
            return "MODERATELY BULLISH - Positive factors present but risks remain"
        elif net_score >= 0:
            return "NEUTRAL - Balanced risk/reward profile"
        elif net_score >= -3:
            return "CAUTIOUS - Risks outweigh near-term catalysts"
        else:
            return "BEARISH - Significant headwinds and limited catalysts"

    def _get_erica_methodology_reference(self, strategy: StrategyType, field_guide_setup=None) -> str:
        """Get reference to Erica's methodology for the strategy"""

        references = {
            StrategyType.COVERED_CALL: "Erica's CC methodology: 50% profit target, 21 DTE management, 50th percentile IV minimum",
            StrategyType.CREDIT_SPREAD: "Erica's bullish spread flowchart: 0.15-0.20 delta, 30-45 DTE, above support levels",
            StrategyType.LEAPS: "Erica's LEAPS approach: 12+ months expiration, 0.70-0.80 delta, low IV entry preferred",
            StrategyType.PREMIUM_SELLING: "Erica's wheel strategy: High IV requirement (>70th percentile), systematic execution"
        }

        base_reference = references.get(strategy, f"Erica's {strategy.value} methodology from field guide")

        if field_guide_setup:
            base_reference += f" | Field guide confidence: {field_guide_setup.confidence:.0%}"

        return base_reference

    def _extract_key_catalysts_from_comprehensive(self, comprehensive_analysis: ComprehensiveCatalystAnalysis) -> List[CatalystAnalysis]:
        """Extract key catalysts for backward compatibility"""
        catalysts = []

        # Convert bullish catalysts to old format
        for bullish in comprehensive_analysis.bullish_catalysts:
            catalysts.append(CatalystAnalysis(
                catalyst_type=bullish.catalyst_type.lower(),
                description=bullish.description,
                impact_level=bullish.impact_assessment.lower(),
                timeframe=bullish.timeframe.lower(),
                probability=0.8 if bullish.impact_assessment == "High" else 0.6,
                supporting_evidence=bullish.supporting_data
            ))

        return catalysts

def create_enhanced_analyzer(api_key: str) -> EnhancedStrategyAnalyzer:
    """Factory function to create enhanced strategy analyzer"""
    return EnhancedStrategyAnalyzer(api_key)
