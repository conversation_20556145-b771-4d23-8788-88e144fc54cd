"""
Enhanced Strategy Example - Stock-Specific Implementation
Demonstrates the new enhanced strategies based on <PERSON>'s detailed strategy grid

This example shows how each stock gets customized strategy recommendations
based on their unique characteristics and behavior patterns.
"""

from enhanced_strategies import EnhancedStrategyAnalyzer, STOCK_RULES
from daily_outline import MarketAnalysis, MarketCondition
from erica_strategies import OptionData, OptionType

def create_sample_market_data():
    """Create sample market data for demonstration"""
    return {
        "AMD": MarketAnalysis(
            symbol="AMD",
            current_price=160.0,
            trend="UPTREND",
            volatility=0.35,  # High volatility
            rsi=65.0,
            ma_20=158.0,
            ma_50=155.0,
            support_level=150.0,
            resistance_level=175.0,
            market_condition=MarketCondition.BULLISH
        ),
        "NVDA": MarketAnalysis(
            symbol="NVDA",
            current_price=920.0,
            trend="UPTREND",
            volatility=0.45,  # Very high volatility
            rsi=70.0,
            ma_20=910.0,
            ma_50=880.0,
            support_level=880.0,
            resistance_level=950.0,
            market_condition=MarketCondition.BULLISH
        ),
        "GOOGL": MarketAnalysis(
            symbol="GOOGL",
            current_price=170.0,
            trend="NEUTRAL",
            volatility=0.22,  # Lower volatility
            rsi=55.0,
            ma_20=169.0,
            ma_50=168.0,
            support_level=165.0,
            resistance_level=175.0,
            market_condition=MarketCondition.NEUTRAL
        ),
        "AMZN": MarketAnalysis(
            symbol="AMZN",
            current_price=185.0,
            trend="UPTREND",
            volatility=0.28,
            rsi=62.0,
            ma_20=183.0,
            ma_50=180.0,
            support_level=175.0,
            resistance_level=195.0,
            market_condition=MarketCondition.BULLISH
        ),
        "AAPL": MarketAnalysis(
            symbol="AAPL",
            current_price=190.0,
            trend="UPTREND",
            volatility=0.20,  # Lowest volatility
            rsi=58.0,
            ma_20=189.0,
            ma_50=187.0,
            support_level=185.0,
            resistance_level=195.0,
            market_condition=MarketCondition.BULLISH
        )
    }

def create_sample_options_chain(symbol: str, current_price: float):
    """Create sample options chain for demonstration"""
    options = []
    
    # Create calls at various strikes
    for i in range(-2, 6):  # Strikes from 2 below to 5 above current price
        strike_increment = {
            "AMD": 5,
            "NVDA": 25,
            "GOOGL": 5,
            "AMZN": 5,
            "AAPL": 2.5
        }.get(symbol, 5)
        
        strike = current_price + (i * strike_increment)
        
        # Calculate approximate delta (simplified)
        moneyness = strike / current_price
        if moneyness < 0.95:
            delta = 0.80
        elif moneyness < 1.00:
            delta = 0.60
        elif moneyness < 1.05:
            delta = 0.30
        elif moneyness < 1.10:
            delta = 0.15
        else:
            delta = 0.05
        
        # Create call option
        call_option = OptionData(
            symbol=symbol,
            strike=strike,
            expiration="2024-03-15",  # ~30 days out
            option_type=OptionType.CALL,
            bid=max(0.10, (current_price - strike + 5) * 0.1),
            ask=max(0.15, (current_price - strike + 6) * 0.1),
            volume=1000,
            open_interest=5000,
            implied_volatility=0.25,
            delta=delta if strike > current_price else None
        )
        options.append(call_option)
        
        # Create put option
        put_delta = -(1 - delta) if strike < current_price else -delta
        put_option = OptionData(
            symbol=symbol,
            strike=strike,
            expiration="2024-03-15",
            option_type=OptionType.PUT,
            bid=max(0.10, (strike - current_price + 3) * 0.1),
            ask=max(0.15, (strike - current_price + 4) * 0.1),
            volume=800,
            open_interest=3000,
            implied_volatility=0.22,
            delta=put_delta if strike < current_price else None
        )
        options.append(put_option)
    
    return options

def demonstrate_enhanced_strategies():
    """Demonstrate the enhanced strategy system"""
    
    print("🚀 ENHANCED STRATEGY SYSTEM DEMONSTRATION")
    print("=" * 60)
    print("Based on Erica's Millionaire Mentorship Strategy Grid")
    print("Stock-specific rules and parameters for each ticker")
    print("=" * 60)
    print()
    
    # Initialize the enhanced analyzer
    analyzer = EnhancedStrategyAnalyzer()
    
    # Get sample market data
    market_data = create_sample_market_data()
    
    # Analyze each stock with enhanced strategies
    for symbol in ["AMD", "NVDA", "GOOGL", "AMZN", "AAPL"]:
        print(f"📊 {symbol} - ENHANCED STRATEGY ANALYSIS")
        print("-" * 40)
        
        # Get market analysis for this stock
        analysis = market_data[symbol]
        
        # Create sample options chain
        options_chain = create_sample_options_chain(symbol, analysis.current_price)
        
        # Get stock-specific recommendations
        signals = analyzer.get_stock_specific_recommendation(
            symbol, analysis, options_chain, 100000
        )
        
        # Display stock characteristics
        rules = STOCK_RULES[symbol]
        print(f"Current Price: ${analysis.current_price:.2f}")
        print(f"Volatility: {analysis.volatility:.1%}")
        print(f"Trend: {analysis.trend}")
        print(f"Market Condition: {analysis.market_condition.value}")
        print()
        
        # Display stock-specific rules
        print(f"📋 {symbol}-Specific Rules:")
        print(f"  • CC Preferred DTE: {rules.cc_preferred_dte[0]}-{rules.cc_preferred_dte[1]} days")
        print(f"  • CC Delta Range: {rules.cc_delta_range[0]:.2f}-{rules.cc_delta_range[1]:.2f}")
        print(f"  • CC Frequency: {rules.cc_frequency}")
        print(f"  • PCS Width: {rules.pcs_width_points[0]}-{rules.pcs_width_points[1]} points")
        print(f"  • Earnings Move Est: {rules.earnings_move_estimate:.0%}")
        print(f"  • Wheel Frequency: {rules.wheel_frequency}")
        print()
        
        # Display recommendations
        print(f"🎯 Strategy Recommendations:")
        if signals:
            for i, signal in enumerate(signals, 1):
                print(f"  {i}. {signal.action} {signal.strategy.value}")
                print(f"     Confidence: {signal.confidence:.0%}")
                print(f"     Reasoning: {signal.reasoning}")
                if signal.entry_price:
                    print(f"     Entry Price: ${signal.entry_price:.2f}")
                if signal.target_price:
                    print(f"     Target Price: ${signal.target_price:.2f}")
                print()
        else:
            print("  No high-confidence signals at current levels")
            print("  Monitor for better setups based on stock characteristics")
            print()
        
        # Add stock-specific insights
        insights = {
            "AMD": [
                "🔥 High volatility semiconductor stock",
                "📅 Focus on short-DTE covered calls between earnings",
                "⚡ Earnings moves average 12% - plan accordingly",
                "🎯 Frequent wheel strategy due to volatility cycles"
            ],
            "NVDA": [
                "⚡ Extreme volatility with gap risk",
                "🛡️ Use conservative deltas (0.15-0.20) for covered calls",
                "📈 Earnings moves average 15% - largest of the group",
                "⚠️ Limited wheel use due to gap risk"
            ],
            "GOOGL": [
                "📊 Lower volatility, range-bound tendencies",
                "🎯 Good for credit spreads and wheel strategies",
                "📅 Earnings moves average 8% - most predictable",
                "💰 Focus on systematic premium collection"
            ],
            "AMZN": [
                "🛒 Strong seasonal patterns (Q4 strength)",
                "📈 Effective wheel strategy in uptrends",
                "📅 Earnings moves average 10%",
                "🎯 Use LEAPS on breakouts after consolidation"
            ],
            "AAPL": [
                "🍎 Most reliable for steady appreciation",
                "💰 Frequent covered calls for cash flow",
                "📱 Product cycle awareness important",
                "🎯 Excellent for LEAPS and wheel strategies"
            ]
        }
        
        print(f"💡 {symbol} Key Insights:")
        for insight in insights[symbol]:
            print(f"  {insight}")
        
        print("\n" + "=" * 60 + "\n")

def show_example_trades():
    """Show specific example trades from Erica's strategy grid"""
    
    print("📝 EXAMPLE TRADES FROM ERICA'S STRATEGY GRID")
    print("=" * 60)
    
    examples = [
        {
            "title": "1. AMD Earnings Covered Call",
            "setup": "Price: $160, EM ≈ $12 (7.5%)",
            "trade": "Sell 175C (Δ ~0.18) 9 DTE for $1.90",
            "management": "Post-earnings: close at 50-70% if IV crush",
            "reasoning": "Short-DTE CC between earnings for cash flow"
        },
        {
            "title": "2. NVDA Put Credit Spread",
            "setup": "Price: $920, strong support $880",
            "trade": "Sell 900P / Buy 880P, credit $3.20, 14 DTE",
            "management": "Target: close at 50% gain or before last week",
            "reasoning": "Conservative PCS with wide buffer for volatility"
        },
        {
            "title": "3. GOOGL Near-Earnings CC",
            "setup": "Price: $170, EM ≈ $8 (4.7%)",
            "trade": "Sell 190C (EM + cushion) 10 DTE for $0.60",
            "management": "Standard 50% profit target",
            "reasoning": "Extra cushion above earnings move estimate"
        },
        {
            "title": "4. AMZN Wheel Strategy",
            "setup": "Consolidation phase, support at $175",
            "trade": "Sell CSP: 175P for $2.50 → assigned basis $172.50",
            "management": "Sell CC: 190C for $1.60; if called, recycle CSP",
            "reasoning": "Effective wheel in uptrend ranges"
        },
        {
            "title": "5. AAPL Short-DTE CC",
            "setup": "Price: $190, Friday expiration",
            "trade": "Sell Friday 197.5C on Thursday for $0.55",
            "management": "Close at 60% max profit or EOD",
            "reasoning": "Frequent short-term CCs for immediate cash flow"
        }
    ]
    
    for example in examples:
        print(f"📋 {example['title']}")
        print(f"   Setup: {example['setup']}")
        print(f"   Trade: {example['trade']}")
        print(f"   Management: {example['management']}")
        print(f"   Reasoning: {example['reasoning']}")
        print()
    
    print("🎯 Key Takeaways:")
    print("• Each stock has unique characteristics requiring different approaches")
    print("• DTE preferences vary by stock volatility and behavior")
    print("• Delta targets adjusted for gap risk and volatility")
    print("• Earnings move estimates guide strike selection")
    print("• Management rules consistent but timing varies by stock")

def main():
    """Run the enhanced strategy demonstration"""
    demonstrate_enhanced_strategies()
    show_example_trades()
    
    print("\n🚀 NEXT STEPS:")
    print("1. Test the enhanced system: python daily_outline.py --analysis-mode full")
    print("2. Launch desktop app: python launch_app.py")
    print("3. Add your own strategy refinements from Erica's videos")
    print("4. Customize parameters in enhanced_strategies.py")
    print("5. Paper trade the recommendations to validate performance")

if __name__ == "__main__":
    main()
