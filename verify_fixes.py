#!/usr/bin/env python3
"""
Comprehensive verification script for the Erica trading system fixes
This script tests all the identified issues and verifies they are resolved
"""

import time
import tkinter as tk
from datetime import datetime

def test_data_structure_fixes():
    """Test that data structure issues are fixed"""
    print("🔍 Testing data structure fixes...")
    
    try:
        from daily_recommendations import DailyRecommendationGenerator
        from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL"]
        
        # Test market data fetching
        market_data = {}
        historical_data = {}
        
        quote = fmp_quote("AAPL", api_key)
        if quote:
            market_data["AAPL"] = {"quote": quote}
        
        hist = fmp_historical_daily("AAPL", api_key, limit=10)
        if hist:
            historical_data["AAPL"] = hist
        
        # Test report generation
        generator = DailyRecommendationGenerator(100000, symbols)
        daily_report = generator.generate_daily_report(market_data, historical_data)
        
        # Test correct attribute access
        if hasattr(daily_report, 'stock_recommendations'):
            print(f"   ✅ DailyReport.stock_recommendations exists: {len(daily_report.stock_recommendations)} items")
        else:
            print("   ❌ DailyReport.stock_recommendations missing")
            return False
            
        if hasattr(daily_report, 'action_items'):
            print(f"   ✅ DailyReport.action_items exists: {len(daily_report.action_items)} items")
        else:
            print("   ⚠️ DailyReport.action_items missing (may be empty)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data structure test failed: {e}")
        return False

def test_gui_improvements():
    """Test GUI improvements and new features"""
    print("\n🔍 Testing GUI improvements...")
    
    try:
        # Create test GUI
        root = tk.Tk()
        root.withdraw()
        
        from desktop_app import TradingSystemGUI
        app = TradingSystemGUI()
        
        # Test that key components exist
        components_to_check = [
            ('notebook', 'Main notebook'),
            ('dashboard_frame', 'Dashboard frame'),
            ('best_strategy_frame', 'Best strategy frame'),
            ('strategy_frame', 'Strategy analysis frame'),  # Fixed name
            ('recommendations_frame', 'Recommendations frame'),
            ('ai_assistant_frame', 'AI assistant frame'),
            ('settings_frame', 'Settings frame')
        ]
        
        all_exist = True
        for attr, name in components_to_check:
            if hasattr(app, attr):
                print(f"   ✅ {name} exists")
            else:
                print(f"   ❌ {name} missing")
                all_exist = False
        
        # Test menu improvements
        if hasattr(app.root, 'tk'):
            print("   ✅ Menu system accessible")
        
        # Test F5 binding
        bindings = app.root.bind_all('<F5>')
        if bindings:
            print("   ✅ F5 key binding for analysis exists")
        else:
            print("   ⚠️ F5 key binding not found")
        
        root.destroy()
        return all_exist
        
    except Exception as e:
        print(f"   ❌ GUI test failed: {e}")
        return False

def test_enhanced_analysis_pipeline():
    """Test the enhanced analysis pipeline"""
    print("\n🔍 Testing enhanced analysis pipeline...")
    
    try:
        from daily_outline import resolve_fmp_key
        from intelligent_strategy_engine import IntelligentStrategyEngine
        from market_analysis_engine import MarketAnalysisEngine
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL", "NVDA"]
        
        # Test engine creation
        intelligent_engine = IntelligentStrategyEngine(api_key)
        market_analyzer = MarketAnalysisEngine(api_key)
        
        print("   ✅ Analysis engines created successfully")
        
        # Test enhanced recommendations
        market_report, strategy_recommendations = intelligent_engine.generate_daily_recommendations(symbols)
        
        if strategy_recommendations:
            print(f"   ✅ Enhanced analysis generated {len(strategy_recommendations)} strategies")
            for rec in strategy_recommendations:
                print(f"      • {rec.symbol}: {rec.recommended_strategy.value} ({rec.confidence:.0%})")
        else:
            print("   ❌ No enhanced recommendations generated")
            return False
        
        # Test market report
        if market_report:
            print("   ✅ Market report generated")
        else:
            print("   ⚠️ Market report is None")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced analysis test failed: {e}")
        return False

def test_error_handling():
    """Test error handling improvements"""
    print("\n🔍 Testing error handling improvements...")
    
    try:
        from market_analysis_engine import MarketAnalysisEngine
        
        # Test with invalid API key
        engine = MarketAnalysisEngine("invalid_key")
        
        # This should not crash due to error handling
        result = engine.analyze_stock_factors("AAPL")
        
        if result and result.symbol == "AAPL":
            print("   ✅ Error handling works - fallback data provided")
            print(f"      Fallback data: IV rank = {result.iv_rank}, Wheel score = {result.wheel_suitability_score}")
        else:
            print("   ❌ Error handling failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
        return False

def test_full_integration():
    """Test full integration with real data"""
    print("\n🚀 Testing full integration...")
    
    try:
        from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
        from daily_recommendations import DailyRecommendationGenerator
        from intelligent_strategy_engine import IntelligentStrategyEngine
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL"]
        
        print("   Step 1: Fetching market data...")
        market_data = {}
        historical_data = {}
        
        quote = fmp_quote("AAPL", api_key)
        if quote:
            market_data["AAPL"] = {"quote": quote}
            print(f"      ✅ Quote: ${quote.get('price', 'N/A')}")
        
        hist = fmp_historical_daily("AAPL", api_key, limit=10)
        if hist:
            historical_data["AAPL"] = hist
            print(f"      ✅ Historical data: {len(hist)} days")
        
        print("   Step 2: Generating traditional recommendations...")
        generator = DailyRecommendationGenerator(100000, symbols)
        daily_report = generator.generate_daily_report(market_data, historical_data)
        formatted_report = generator.format_daily_report(daily_report)
        
        print(f"      ✅ Traditional analysis: {len(daily_report.stock_recommendations)} recommendations")
        
        print("   Step 3: Generating enhanced recommendations...")
        intelligent_engine = IntelligentStrategyEngine(api_key)
        market_report, strategy_recommendations = intelligent_engine.generate_daily_recommendations(symbols)
        
        print(f"      ✅ Enhanced analysis: {len(strategy_recommendations)} strategies")
        
        print("   Step 4: Testing data compatibility...")
        # Test that the data can be used by the GUI update methods
        if daily_report.stock_recommendations:
            print("      ✅ Stock recommendations available for GUI")
        
        if hasattr(daily_report, 'action_items'):
            print(f"      ✅ Action items available: {len(daily_report.action_items) if daily_report.action_items else 0}")
        
        if strategy_recommendations:
            print("      ✅ Enhanced strategies available for GUI")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Full integration test failed: {e}")
        return False

def generate_user_instructions():
    """Generate instructions for the user"""
    print("\n" + "="*60)
    print("📋 USER INSTRUCTIONS FOR TESTING THE FIXES")
    print("="*60)
    
    print("\n🎯 HOW TO TEST THE FIXED APPLICATION:")
    
    print("\n1. 🚀 TRIGGER ANALYSIS:")
    print("   • Look for the GUI window (should be open)")
    print("   • Click '🚀 ANALYZE' in the menu bar")
    print("   • OR press F5 key")
    print("   • OR use View menu → Run Full Analysis")
    
    print("\n2. 📊 CHECK EACH TAB:")
    print("   • Dashboard: Should show market data and stock cards")
    print("   • Best Strategies: Should show strategy recommendations")
    print("   • Strategy Analysis: Should show detailed criteria")
    print("   • Daily Recommendations: Should show action items")
    print("   • Risk Management: Should show risk parameters")
    print("   • AI Assistant: Configure OpenAI key first")
    print("   • Settings: Configure API keys and preferences")
    
    print("\n3. 🔍 WHAT TO LOOK FOR:")
    print("   • No more 'Loading...' that never completes")
    print("   • Actual data in tables and charts")
    print("   • No blank/empty tabs")
    print("   • Responsive interface (no freezing)")
    
    print("\n4. ⚠️ IF ISSUES PERSIST:")
    print("   • Check Settings tab for API key configuration")
    print("   • Try running the manual trigger: python trigger_analysis.py")
    print("   • Check console for any error messages")
    print("   • Restart the application if needed")
    
    print("\n5. ✅ SUCCESS INDICATORS:")
    print("   • All tabs show content after running analysis")
    print("   • Strategy recommendations appear with confidence scores")
    print("   • Market data updates in real-time")
    print("   • No error messages in console")

def main():
    """Main verification function"""
    print("🔍 COMPREHENSIVE VERIFICATION OF ERICA TRADING SYSTEM FIXES")
    print("=" * 70)
    
    tests = [
        ("Data Structure Fixes", test_data_structure_fixes),
        ("GUI Improvements", test_gui_improvements),
        ("Enhanced Analysis Pipeline", test_enhanced_analysis_pipeline),
        ("Error Handling", test_error_handling),
        ("Full Integration", test_full_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 70)
    print("📊 VERIFICATION RESULTS")
    print("=" * 70)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:30} {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("The Erica trading system should now be fully functional.")
    else:
        print("\n⚠️ SOME ISSUES REMAIN!")
        print("Please check the failed tests above.")
    
    # Always show user instructions
    generate_user_instructions()
    
    return all_passed

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 SYSTEM READY!")
        print("Your enhanced Erica trading system is now fully operational!")
    else:
        print("\n🔧 ADDITIONAL FIXES NEEDED!")
        print("Some issues require further attention.")
