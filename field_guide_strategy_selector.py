"""
Field Guide Strategy Selector
Implements all strategies from <PERSON>'s comprehensive field guide

This module provides:
1. Complete strategy selection logic from field guide
2. Ticker-specific guardrails for AMD/NVDA/GOOGL/AAPL/AMZN
3. Real-time market data integration
4. Concrete setup templates and management rules
5. Live data-driven decision making

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import logging

from live_market_data_integration import LiveMarketData, LiveMarketDataProvider
from market_analysis_engine import MarketFactors, StockSpecificFactors

class FieldGuideStrategy(Enum):
    """All strategies from the field guide"""
    BULLISH_SPREAD = "bullish_spread"
    PUT_CREDIT_SPREAD = "put_credit_spread"
    CASH_SECURED_PUT = "cash_secured_put"
    COVERED_CALL = "covered_call"
    BEAR_CALL_SPREAD = "bear_call_spread"
    RICH_WOMANS_CC = "rich_womans_cc"
    POOR_MANS_CC = "poor_mans_cc"
    WHEEL_STRATEGY = "wheel_strategy"
    CALENDAR_SPREADS = "calendar_spreads"
    LEAPS_STRATEGY = "leaps_strategy"

@dataclass
class StrategySetup:
    """Complete strategy setup with all parameters"""
    strategy: FieldGuideStrategy
    symbol: str
    confidence: float  # 0-1
    
    # Entry parameters
    primary_action: str  # "buy_call", "sell_put", etc.
    secondary_action: Optional[str]  # For spreads
    strike_1: Optional[float]
    strike_2: Optional[float]
    dte: int
    delta_target: float
    
    # Risk management
    max_risk: float
    max_profit: float
    breakeven: float
    alarm_price: Optional[float]
    
    # Management rules
    profit_target_pct: float
    time_exit_dte: int
    roll_trigger: str
    
    # Reasoning
    why_selected: str
    key_factors: List[str]
    risk_factors: List[str]
    management_notes: str

class FieldGuideStrategySelector:
    """Strategy selector implementing complete field guide logic"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.data_provider = LiveMarketDataProvider(api_key)
        self.logger = logging.getLogger(__name__)
        
        # Ticker-specific guardrails
        self.ticker_guardrails = {
            "AMD": {
                "csp_delta_range": (0.15, 0.25),
                "cc_dte_range": (2, 5),
                "alarm_offset": -1.0,  # $1 for AMD price range
                "earnings_buffer": 0.05,
                "preferred_strategies": ["cash_secured_put", "covered_call", "wheel_strategy"]
            },
            "NVDA": {
                "csp_delta_range": (0.10, 0.20),
                "cc_dte_range": (7, 21),
                "alarm_offset": -2.5,  # $2.50 for NVDA volatility
                "earnings_buffer": 0.08,
                "preferred_strategies": ["bear_call_spread", "rich_womans_cc", "calendar_spreads"]
            },
            "AAPL": {
                "csp_delta_range": (0.20, 0.30),
                "cc_dte_range": (2, 30),
                "alarm_offset": -1.0,
                "earnings_buffer": 0.02,
                "preferred_strategies": ["covered_call", "wheel_strategy", "poor_mans_cc"]
            },
            "GOOGL": {
                "csp_delta_range": (0.15, 0.30),
                "cc_dte_range": (7, 21),
                "alarm_offset": -2.5,
                "earnings_buffer": 0.10,
                "preferred_strategies": ["put_credit_spread", "bullish_spread", "leaps_strategy"]
            },
            "AMZN": {
                "csp_delta_range": (0.20, 0.30),
                "cc_dte_range": (7, 21),
                "alarm_offset": -2.5,
                "earnings_buffer": 0.05,
                "preferred_strategies": ["wheel_strategy", "rich_womans_cc", "calendar_spreads"]
            }
        }
    
    def select_optimal_strategy(self, symbol: str) -> StrategySetup:
        """Select optimal strategy based on live market conditions and field guide rules"""
        
        # Get live market data
        live_data = self.data_provider.get_live_data(symbol)
        
        # Get ticker-specific guardrails
        guardrails = self.ticker_guardrails.get(symbol, self._get_default_guardrails())
        
        # Analyze market conditions
        market_condition = self._analyze_market_condition(live_data)
        
        # Apply field guide decision tree
        strategy_scores = self._score_all_strategies(live_data, guardrails, market_condition)
        
        # Select highest scoring strategy
        best_strategy = max(strategy_scores.items(), key=lambda x: x[1]['score'])
        strategy_type = best_strategy[0]
        strategy_data = best_strategy[1]
        
        # Generate complete setup
        setup = self._generate_strategy_setup(
            strategy_type, symbol, live_data, guardrails, strategy_data
        )
        
        return setup
    
    def _analyze_market_condition(self, live_data: LiveMarketData) -> str:
        """Analyze current market condition for strategy selection"""
        
        # Momentum analysis
        if abs(live_data.change_percent) > 0.03:  # >3% move
            if live_data.change_percent > 0:
                return "strong_bullish"
            else:
                return "strong_bearish"
        
        # Volatility analysis
        if live_data.iv_rank > 0.70:
            return "high_volatility"
        elif live_data.iv_rank < 0.30:
            return "low_volatility"
        
        # Earnings proximity
        if live_data.earnings_days_away and live_data.earnings_days_away <= 7:
            return "pre_earnings"
        elif live_data.earnings_days_away and -3 <= live_data.earnings_days_away <= 3:
            return "post_earnings"
        
        # Default to range-bound
        return "range_bound"
    
    def _score_all_strategies(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                            market_condition: str) -> Dict[FieldGuideStrategy, Dict[str, Any]]:
        """Score all strategies based on current conditions"""
        
        scores = {}
        
        # Cash Secured Put scoring
        scores[FieldGuideStrategy.CASH_SECURED_PUT] = self._score_csp(live_data, guardrails, market_condition)
        
        # Covered Call scoring
        scores[FieldGuideStrategy.COVERED_CALL] = self._score_cc(live_data, guardrails, market_condition)
        
        # Put Credit Spread scoring
        scores[FieldGuideStrategy.PUT_CREDIT_SPREAD] = self._score_pcs(live_data, guardrails, market_condition)
        
        # Bullish Spread scoring
        scores[FieldGuideStrategy.BULLISH_SPREAD] = self._score_bullish_spread(live_data, guardrails, market_condition)
        
        # Bear Call Spread scoring
        scores[FieldGuideStrategy.BEAR_CALL_SPREAD] = self._score_bear_call_spread(live_data, guardrails, market_condition)
        
        # Wheel Strategy scoring
        scores[FieldGuideStrategy.WHEEL_STRATEGY] = self._score_wheel(live_data, guardrails, market_condition)
        
        # LEAPS scoring
        scores[FieldGuideStrategy.LEAPS_STRATEGY] = self._score_leaps(live_data, guardrails, market_condition)
        
        # Rich Woman's CC scoring
        scores[FieldGuideStrategy.RICH_WOMANS_CC] = self._score_rich_womans_cc(live_data, guardrails, market_condition)
        
        # Calendar Spreads scoring
        scores[FieldGuideStrategy.CALENDAR_SPREADS] = self._score_calendar(live_data, guardrails, market_condition)
        
        # Poor Man's Covered Call scoring
        scores[FieldGuideStrategy.POOR_MANS_CC] = self._score_poor_mans_cc(live_data, guardrails, market_condition)

        return scores
    
    def _score_csp(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                  market_condition: str) -> Dict[str, Any]:
        """Score Cash Secured Put strategy"""
        base_score = 0.5
        
        # Favor CSP in neutral-to-bullish conditions
        if market_condition in ["range_bound", "strong_bullish"]:
            base_score += 0.2
        
        # High IV favors premium selling
        if live_data.iv_rank > 0.60:
            base_score += 0.2
        
        # Near support levels
        if live_data.support_level and live_data.current_price > live_data.support_level * 1.05:
            base_score += 0.1
        
        # Earnings timing
        if live_data.earnings_days_away and live_data.earnings_days_away > 21:
            base_score += 0.1
        elif live_data.earnings_days_away and live_data.earnings_days_away < 10:
            base_score -= 0.2  # Avoid near earnings
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"CSP suitable for {market_condition} with IV rank {live_data.iv_rank:.0%}",
            'key_factors': ['IV rank', 'Market condition', 'Support levels']
        }
    
    def _score_cc(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                 market_condition: str) -> Dict[str, Any]:
        """Score Covered Call strategy"""
        base_score = 0.6  # Generally good strategy
        
        # Favor CC in range-bound or mildly bullish
        if market_condition in ["range_bound", "high_volatility"]:
            base_score += 0.2
        elif market_condition == "strong_bullish":
            base_score -= 0.1  # Don't want to cap upside
        
        # High IV favors premium collection
        if live_data.iv_rank > 0.50:
            base_score += 0.2
        
        # Near resistance
        if live_data.resistance_level and live_data.current_price < live_data.resistance_level * 0.95:
            base_score += 0.1
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"CC suitable for income generation with IV rank {live_data.iv_rank:.0%}",
            'key_factors': ['IV rank', 'Resistance levels', 'Premium collection']
        }
    
    def _score_pcs(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                  market_condition: str) -> Dict[str, Any]:
        """Score Put Credit Spread strategy"""
        base_score = 0.4
        
        # Favor PCS in bullish conditions
        if market_condition in ["strong_bullish", "range_bound"]:
            base_score += 0.3
        
        # High IV required
        if live_data.iv_rank > 0.60:
            base_score += 0.2
        
        # Above support
        if live_data.support_level and live_data.current_price > live_data.support_level * 1.05:
            base_score += 0.1
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"PCS suitable for bullish bias with high IV ({live_data.iv_rank:.0%})",
            'key_factors': ['Market bias', 'IV rank', 'Support levels']
        }
    
    def _score_bullish_spread(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                            market_condition: str) -> Dict[str, Any]:
        """Score Bullish Spread strategy"""
        base_score = 0.3
        
        # Favor in bullish conditions with defined risk preference
        if market_condition == "strong_bullish":
            base_score += 0.4
        
        # Moderate IV preferred
        if 0.40 <= live_data.iv_rank <= 0.70:
            base_score += 0.2
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"Bullish spread for defined risk bullish play",
            'key_factors': ['Bullish bias', 'Defined risk', 'Conversion option']
        }
    
    def _score_bear_call_spread(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                              market_condition: str) -> Dict[str, Any]:
        """Score Bear Call Spread strategy"""
        base_score = 0.2
        
        # Favor after strong moves up or near resistance
        if market_condition == "strong_bullish" and live_data.resistance_level:
            if live_data.current_price >= live_data.resistance_level * 0.98:
                base_score += 0.4
        
        # High IV favors credit spreads
        if live_data.iv_rank > 0.60:
            base_score += 0.2
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"Bear call spread for mean reversion near resistance",
            'key_factors': ['Resistance levels', 'Mean reversion', 'IV crush potential']
        }
    
    def _score_wheel(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                    market_condition: str) -> Dict[str, Any]:
        """Score Wheel Strategy"""
        base_score = 0.5
        
        # Favor in rising/range-bound markets
        if market_condition in ["range_bound", "strong_bullish"]:
            base_score += 0.2
        elif market_condition == "strong_bearish":
            base_score -= 0.3  # Avoid in downtrends
        
        # High IV helps both sides
        if live_data.iv_rank > 0.60:
            base_score += 0.2
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"Wheel strategy for systematic premium collection",
            'key_factors': ['Market regime', 'Systematic approach', 'Premium collection']
        }
    
    def _score_leaps(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                    market_condition: str) -> Dict[str, Any]:
        """Score LEAPS Strategy"""
        base_score = 0.3
        
        # Favor in strong bullish or low IV conditions
        if market_condition == "strong_bullish":
            base_score += 0.3
        
        # Low IV preferred for buying options
        if live_data.iv_rank < 0.40:
            base_score += 0.3
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"LEAPS for long-term bullish exposure with capital efficiency",
            'key_factors': ['Long-term bullish', 'Capital efficiency', 'Low IV entry']
        }
    
    def _score_rich_womans_cc(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                            market_condition: str) -> Dict[str, Any]:
        """Score Rich Woman's Covered Call"""
        base_score = 0.4
        
        # Good for bullish with longer horizon
        if market_condition in ["strong_bullish", "range_bound"]:
            base_score += 0.2
        
        # Moderate IV good for both buying LEAPS and selling calls
        if 0.30 <= live_data.iv_rank <= 0.60:
            base_score += 0.2
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"Rich Woman's CC for equity-like upside with less capital",
            'key_factors': ['Capital efficiency', 'Bullish horizon', 'LEAPS + short calls']
        }
    
    def _score_calendar(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                       market_condition: str) -> Dict[str, Any]:
        """Score Calendar Spreads"""
        base_score = 0.3
        
        # Good for range-bound or slow trending
        if market_condition == "range_bound":
            base_score += 0.3
        
        # Benefits from time decay differential
        if live_data.iv_rank > 0.40:
            base_score += 0.2
        
        return {
            'score': min(base_score, 1.0),
            'reasoning': f"Calendar spreads for recurring income from same long call",
            'key_factors': ['Time decay', 'Recurring income', 'Range-bound preference']
        }

    def _score_poor_mans_cc(self, live_data: LiveMarketData, guardrails: Dict[str, Any], 
                            market_condition: str) -> Dict[str, Any]:
        """Score Poor Man's Covered Call (long LEAPS + short calls)"""
        base_score = 0.3

        # Favor in range-bound to mildly bullish with moderate IV
        if market_condition in ["range_bound", "strong_bullish"]:
            base_score += 0.2

        # Moderate IV (0.30-0.60) preferred: buy LEAPS not too expensive, still decent short-call premium
        if 0.30 <= live_data.iv_rank <= 0.60:
            base_score += 0.3

        # Avoid very near earnings due to gap risk on long call
        if live_data.earnings_days_away is not None and live_data.earnings_days_away < 7:
            base_score -= 0.2

        return {
            'score': min(max(base_score, 0.0), 1.0),
            'reasoning': "PMCC suitable when IV is moderate and trend is stable-to-bullish",
            'key_factors': ['Moderate IV', 'Bullish/range-bound bias', 'Capital efficiency']
        }
    
    def _get_default_guardrails(self) -> Dict[str, Any]:
        """Default guardrails for unknown tickers"""
        return {
            "csp_delta_range": (0.15, 0.25),
            "cc_dte_range": (7, 21),
            "alarm_offset": -2.0,
            "earnings_buffer": 0.05,
            "preferred_strategies": ["covered_call", "cash_secured_put"]
        }
    
    def _generate_strategy_setup(self, strategy: FieldGuideStrategy, symbol: str, 
                               live_data: LiveMarketData, guardrails: Dict[str, Any], 
                               strategy_data: Dict[str, Any]) -> StrategySetup:
        """Generate complete strategy setup"""
        
        # This would contain detailed setup logic for each strategy
        # For now, return a basic setup structure
        
        return StrategySetup(
            strategy=strategy,
            symbol=symbol,
            confidence=strategy_data['score'],
            primary_action=f"Setup {strategy.value}",
            secondary_action=None,
            strike_1=live_data.current_price * 1.02,  # Example
            strike_2=None,
            dte=14,  # Example
            delta_target=0.25,  # Example
            max_risk=live_data.current_price * 0.05,
            max_profit=live_data.current_price * 0.02,
            breakeven=live_data.current_price,
            alarm_price=live_data.current_price + guardrails['alarm_offset'],
            profit_target_pct=0.50,
            time_exit_dte=5,
            roll_trigger="Delta > 0.35",
            why_selected=strategy_data['reasoning'],
            key_factors=strategy_data['key_factors'],
            risk_factors=["Market risk", "Time decay", "Volatility risk"],
            management_notes=f"Follow {strategy.value} management rules from field guide"
        )

def create_field_guide_selector(api_key: str) -> FieldGuideStrategySelector:
    """Factory function to create field guide strategy selector"""
    return FieldGuideStrategySelector(api_key)
