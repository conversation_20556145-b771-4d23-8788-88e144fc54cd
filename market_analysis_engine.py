"""
Multi-Factor Market Analysis Engine
Comprehensive market factor analysis for intelligent strategy selection

This module analyzes multiple market factors to provide intelligent strategy recommendations:
- Market sentiment indicators (VIX, put/call ratios, market breadth)
- News sentiment analysis and earnings calendar proximity
- Psychological indicators (fear/greed, social sentiment)
- Technical analysis confluence
- Sector rotation and relative strength
- Volatility regime detection

Based on <PERSON>'s decision-making framework from @AbundantlyErica
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import math
import requests
import json

class MarketRegime(Enum):
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    TRANSITION = "transition"

class VolatilityRegime(Enum):
    LOW_VOL = "low_volatility"
    NORMAL_VOL = "normal_volatility"
    HIGH_VOL = "high_volatility"
    EXTREME_VOL = "extreme_volatility"

class SentimentLevel(Enum):
    EXTREME_FEAR = "extreme_fear"
    FEAR = "fear"
    NEUTRAL = "neutral"
    GREED = "greed"
    EXTREME_GREED = "extreme_greed"

@dataclass
class MarketFactors:
    """Comprehensive market factor analysis results"""
    
    # Market Sentiment Indicators
    vix_level: float
    vix_percentile: float  # 0-100 percentile vs 1-year range
    put_call_ratio: float
    market_breadth: float  # Advance/decline ratio
    
    # Volatility Analysis
    volatility_regime: VolatilityRegime
    iv_rank_spy: float  # SPY IV rank
    iv_term_structure: str  # contango/backwardation
    
    # Market Regime
    market_regime: MarketRegime
    trend_strength: float  # -1 to 1 scale
    regime_confidence: float  # 0-1 confidence in regime classification
    
    # Psychological Indicators
    fear_greed_index: float  # 0-100 scale
    sentiment_level: SentimentLevel
    social_sentiment: float  # -1 to 1 scale
    
    # Technical Confluence
    spy_technical_score: float  # Overall technical health of market
    sector_rotation_signal: str  # growth/value/defensive rotation
    relative_strength_leaders: List[str]  # Leading sectors
    
    # News and Events
    earnings_season_intensity: float  # 0-1 scale
    fed_meeting_proximity: int  # Days until next Fed meeting
    major_events_this_week: List[str]
    
    # Factor Scores (for decision tree)
    bullish_factors: float  # 0-1 composite bullish score
    bearish_factors: float  # 0-1 composite bearish score
    volatility_factors: float  # 0-1 volatility opportunity score
    uncertainty_factors: float  # 0-1 uncertainty/risk score

@dataclass
class StockSpecificFactors:
    """Stock-specific factor analysis"""
    symbol: str
    
    # News and Events
    news_sentiment_score: float  # -1 to 1
    earnings_days_away: Optional[int]
    earnings_move_estimate: float
    recent_analyst_changes: List[Dict]  # upgrades/downgrades
    
    # Technical Factors
    relative_strength_vs_spy: float  # -1 to 1
    technical_confluence_score: float  # 0-1
    support_resistance_clarity: float  # 0-1
    trend_alignment: str  # strong_up/up/neutral/down/strong_down
    
    # Options Activity
    unusual_options_activity: bool
    iv_rank: float  # 0-1 IV rank for this stock
    iv_percentile: float  # 0-1 IV percentile for this stock
    iv_vs_hv_ratio: float  # IV vs Historical Volatility
    options_flow_sentiment: str  # bullish/bearish/neutral
    
    # Sector Analysis
    sector_performance: float  # Sector performance vs market
    sector_rotation_impact: str  # positive/negative/neutral
    
    # Erica's Key Factors
    wheel_suitability_score: float  # 0-1 based on Erica's criteria
    covered_call_attractiveness: float  # 0-1 based on premium/risk
    credit_spread_opportunity: float  # 0-1 based on market conditions
    leaps_opportunity: float  # 0-1 based on long-term outlook

class MarketAnalysisEngine:
    """Comprehensive market analysis engine"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.cache = {}
        self.cache_expiry = {}
        
    def analyze_market_factors(self) -> MarketFactors:
        """Perform comprehensive market factor analysis"""
        
        # Get market sentiment indicators
        vix_data = self._get_vix_data()
        put_call_data = self._get_put_call_ratio()
        breadth_data = self._get_market_breadth()
        
        # Analyze volatility regime
        vol_regime = self._analyze_volatility_regime(vix_data)
        
        # Detect market regime
        market_regime, trend_strength, regime_confidence = self._detect_market_regime()
        
        # Get psychological indicators
        fear_greed = self._get_fear_greed_index()
        social_sentiment = self._analyze_social_sentiment()
        
        # Technical analysis
        spy_technical = self._analyze_spy_technicals()
        sector_rotation = self._analyze_sector_rotation()
        
        # News and events
        earnings_intensity = self._calculate_earnings_intensity()
        fed_proximity = self._get_fed_meeting_proximity()
        major_events = self._get_major_events()
        
        # Calculate composite factor scores
        bullish_score = self._calculate_bullish_factors(
            vix_data, put_call_data, breadth_data, spy_technical, fear_greed
        )
        bearish_score = self._calculate_bearish_factors(
            vix_data, put_call_data, breadth_data, spy_technical, fear_greed
        )
        volatility_score = self._calculate_volatility_opportunity(vix_data, vol_regime)
        uncertainty_score = self._calculate_uncertainty_factors(
            fed_proximity, major_events, earnings_intensity
        )
        
        return MarketFactors(
            vix_level=vix_data.get('level', 20.0),
            vix_percentile=vix_data.get('percentile', 50.0),
            put_call_ratio=put_call_data.get('ratio', 1.0),
            market_breadth=breadth_data.get('ratio', 1.0),
            volatility_regime=vol_regime,
            iv_rank_spy=vix_data.get('iv_rank', 0.5),
            iv_term_structure=vix_data.get('term_structure', 'normal'),
            market_regime=market_regime,
            trend_strength=trend_strength,
            regime_confidence=regime_confidence,
            fear_greed_index=fear_greed,
            sentiment_level=self._classify_sentiment(fear_greed),
            social_sentiment=social_sentiment,
            spy_technical_score=spy_technical,
            sector_rotation_signal=sector_rotation.get('signal', 'neutral'),
            relative_strength_leaders=sector_rotation.get('leaders', []),
            earnings_season_intensity=earnings_intensity,
            fed_meeting_proximity=fed_proximity,
            major_events_this_week=major_events,
            bullish_factors=bullish_score,
            bearish_factors=bearish_score,
            volatility_factors=volatility_score,
            uncertainty_factors=uncertainty_score
        )
    
    def analyze_stock_factors(self, symbol: str) -> StockSpecificFactors:
        """Analyze stock-specific factors for strategy selection"""
        
        # News sentiment analysis
        news_sentiment = self._analyze_stock_news_sentiment(symbol)
        
        # Earnings analysis
        earnings_data = self._get_earnings_data(symbol)
        
        # Technical analysis
        technical_data = self._analyze_stock_technicals(symbol)
        
        # Options analysis
        options_data = self._analyze_options_activity(symbol)
        
        # Sector analysis
        sector_data = self._analyze_sector_impact(symbol)
        
        # Erica's specific factors
        erica_factors = self._calculate_erica_factors(symbol, technical_data, options_data)
        
        return StockSpecificFactors(
            symbol=symbol,
            news_sentiment_score=news_sentiment.get('score', 0.0),
            earnings_days_away=earnings_data.get('days_away'),
            earnings_move_estimate=earnings_data.get('move_estimate', 0.08),
            recent_analyst_changes=earnings_data.get('analyst_changes', []),
            relative_strength_vs_spy=technical_data.get('relative_strength', 0.0),
            technical_confluence_score=technical_data.get('confluence_score', 0.5),
            support_resistance_clarity=technical_data.get('sr_clarity', 0.5),
            trend_alignment=technical_data.get('trend_alignment', 'neutral'),
            unusual_options_activity=options_data.get('unusual_activity', False),
            iv_rank=options_data.get('iv_rank', 0.5),
            iv_percentile=options_data.get('iv_percentile', 0.5),
            iv_vs_hv_ratio=options_data.get('iv_hv_ratio', 1.0),
            options_flow_sentiment=options_data.get('flow_sentiment', 'neutral'),
            sector_performance=sector_data.get('performance', 0.0),
            sector_rotation_impact=sector_data.get('rotation_impact', 'neutral'),
            wheel_suitability_score=erica_factors.get('wheel_score', 0.5),
            covered_call_attractiveness=erica_factors.get('cc_score', 0.5),
            credit_spread_opportunity=erica_factors.get('cs_score', 0.5),
            leaps_opportunity=erica_factors.get('leaps_score', 0.5)
        )
    
    def _get_vix_data(self) -> Dict:
        """Get VIX data and calculate percentiles"""
        try:
            # In real implementation, would fetch from FMP or other source
            # For now, return mock data structure
            return {
                'level': 18.5,
                'percentile': 35.0,  # 35th percentile = relatively low
                'iv_rank': 0.35,
                'term_structure': 'contango'  # normal market conditions
            }
        except Exception:
            return {'level': 20.0, 'percentile': 50.0, 'iv_rank': 0.5, 'term_structure': 'normal'}
    
    def _get_put_call_ratio(self) -> Dict:
        """Get put/call ratio data"""
        try:
            # Mock implementation - would fetch real data
            return {
                'ratio': 0.85,  # Below 1.0 = more calls than puts (bullish)
                'percentile': 25.0  # Low percentile = bullish sentiment
            }
        except Exception:
            return {'ratio': 1.0, 'percentile': 50.0}
    
    def _get_market_breadth(self) -> Dict:
        """Get market breadth indicators"""
        try:
            # Mock implementation - advance/decline ratio
            return {
                'ratio': 1.8,  # More advancing than declining stocks
                'new_highs_lows': 2.5,  # More new highs than lows
                'up_volume_ratio': 0.65  # 65% of volume in advancing stocks
            }
        except Exception:
            return {'ratio': 1.0, 'new_highs_lows': 1.0, 'up_volume_ratio': 0.5}
    
    def _analyze_volatility_regime(self, vix_data: Dict) -> VolatilityRegime:
        """Determine current volatility regime"""
        vix_level = vix_data.get('level', 20.0)
        vix_percentile = vix_data.get('percentile', 50.0)
        
        if vix_level > 30 or vix_percentile > 90:
            return VolatilityRegime.EXTREME_VOL
        elif vix_level > 25 or vix_percentile > 75:
            return VolatilityRegime.HIGH_VOL
        elif vix_level < 15 or vix_percentile < 25:
            return VolatilityRegime.LOW_VOL
        else:
            return VolatilityRegime.NORMAL_VOL
    
    def _detect_market_regime(self) -> Tuple[MarketRegime, float, float]:
        """Detect current market regime with confidence"""
        try:
            # Mock implementation - would analyze SPY, QQQ, market internals
            # This would look at:
            # - Price vs moving averages
            # - Market breadth
            # - Sector performance
            # - Momentum indicators
            
            trend_strength = 0.6  # Moderate bullish trend
            regime_confidence = 0.75  # High confidence
            
            if trend_strength > 0.5:
                regime = MarketRegime.BULL_MARKET
            elif trend_strength < -0.5:
                regime = MarketRegime.BEAR_MARKET
            else:
                regime = MarketRegime.SIDEWAYS_MARKET
            
            return regime, trend_strength, regime_confidence
            
        except Exception:
            return MarketRegime.SIDEWAYS_MARKET, 0.0, 0.5
    
    def _get_fear_greed_index(self) -> float:
        """Get CNN Fear & Greed Index or calculate similar metric"""
        try:
            # Mock implementation - would fetch from CNN or calculate similar
            return 65.0  # Moderate greed
        except Exception:
            return 50.0  # Neutral
    
    def _classify_sentiment(self, fear_greed: float) -> SentimentLevel:
        """Classify sentiment level based on fear/greed index"""
        if fear_greed <= 20:
            return SentimentLevel.EXTREME_FEAR
        elif fear_greed <= 40:
            return SentimentLevel.FEAR
        elif fear_greed <= 60:
            return SentimentLevel.NEUTRAL
        elif fear_greed <= 80:
            return SentimentLevel.GREED
        else:
            return SentimentLevel.EXTREME_GREED
    
    def _analyze_social_sentiment(self) -> float:
        """Analyze social media sentiment"""
        try:
            # Mock implementation - would analyze Twitter, Reddit, etc.
            return 0.3  # Moderately bullish social sentiment
        except Exception:
            return 0.0  # Neutral
    
    def _analyze_spy_technicals(self) -> float:
        """Analyze SPY technical health"""
        try:
            # Mock implementation - would analyze:
            # - Price vs moving averages
            # - RSI, MACD, other indicators
            # - Support/resistance levels
            # - Volume patterns
            return 0.7  # Strong technical health
        except Exception:
            return 0.5  # Neutral
    
    def _analyze_sector_rotation(self) -> Dict:
        """Analyze sector rotation patterns"""
        try:
            # Mock implementation - would analyze sector performance
            return {
                'signal': 'growth_rotation',
                'leaders': ['Technology', 'Communication Services'],
                'laggards': ['Utilities', 'Real Estate']
            }
        except Exception:
            return {'signal': 'neutral', 'leaders': [], 'laggards': []}
    
    def _calculate_earnings_intensity(self) -> float:
        """Calculate earnings season intensity"""
        try:
            # Mock implementation - would count earnings this week
            return 0.6  # Moderate earnings intensity
        except Exception:
            return 0.3  # Low intensity
    
    def _get_fed_meeting_proximity(self) -> int:
        """Days until next Fed meeting"""
        try:
            # Mock implementation - would check Fed calendar
            return 15  # 15 days until next meeting
        except Exception:
            return 30
    
    def _get_major_events(self) -> List[str]:
        """Get major market events this week"""
        try:
            # Mock implementation
            return ["CPI Report", "FOMC Minutes"]
        except Exception:
            return []
    
    def _calculate_bullish_factors(self, vix_data: Dict, put_call_data: Dict, 
                                 breadth_data: Dict, spy_technical: float, 
                                 fear_greed: float) -> float:
        """Calculate composite bullish factor score"""
        factors = []
        
        # Low VIX is bullish
        if vix_data.get('percentile', 50) < 50:
            factors.append(0.8)
        
        # Low put/call ratio is bullish
        if put_call_data.get('ratio', 1.0) < 1.0:
            factors.append(0.7)
        
        # Good market breadth is bullish
        if breadth_data.get('ratio', 1.0) > 1.2:
            factors.append(0.8)
        
        # Strong technicals are bullish
        if spy_technical > 0.6:
            factors.append(spy_technical)
        
        # Moderate greed is bullish (not extreme)
        if 50 < fear_greed < 80:
            factors.append(0.7)
        
        return sum(factors) / len(factors) if factors else 0.5
    
    def _calculate_bearish_factors(self, vix_data: Dict, put_call_data: Dict,
                                 breadth_data: Dict, spy_technical: float,
                                 fear_greed: float) -> float:
        """Calculate composite bearish factor score"""
        factors = []
        
        # High VIX is bearish
        if vix_data.get('percentile', 50) > 75:
            factors.append(0.8)
        
        # High put/call ratio is bearish
        if put_call_data.get('ratio', 1.0) > 1.3:
            factors.append(0.7)
        
        # Poor market breadth is bearish
        if breadth_data.get('ratio', 1.0) < 0.8:
            factors.append(0.8)
        
        # Weak technicals are bearish
        if spy_technical < 0.4:
            factors.append(1.0 - spy_technical)
        
        # Extreme fear is bearish
        if fear_greed < 30:
            factors.append(0.8)
        
        return sum(factors) / len(factors) if factors else 0.5
    
    def _calculate_volatility_opportunity(self, vix_data: Dict, vol_regime: VolatilityRegime) -> float:
        """Calculate volatility opportunity score for premium selling"""
        base_score = 0.5
        
        # High VIX percentile increases opportunity
        vix_percentile = vix_data.get('percentile', 50)
        if vix_percentile > 70:
            base_score += 0.3
        elif vix_percentile > 50:
            base_score += 0.1
        
        # Volatility regime adjustment
        if vol_regime == VolatilityRegime.HIGH_VOL:
            base_score += 0.2
        elif vol_regime == VolatilityRegime.EXTREME_VOL:
            base_score += 0.3
        elif vol_regime == VolatilityRegime.LOW_VOL:
            base_score -= 0.2
        
        return min(base_score, 1.0)
    
    def _calculate_uncertainty_factors(self, fed_proximity: int, major_events: List[str],
                                     earnings_intensity: float) -> float:
        """Calculate uncertainty/risk factor score"""
        uncertainty = 0.0
        
        # Fed meeting proximity increases uncertainty
        if fed_proximity <= 7:
            uncertainty += 0.3
        elif fed_proximity <= 14:
            uncertainty += 0.1
        
        # Major events increase uncertainty
        uncertainty += len(major_events) * 0.1
        
        # High earnings intensity increases uncertainty
        uncertainty += earnings_intensity * 0.2
        
        return min(uncertainty, 1.0)
    
    # Stock-specific analysis methods
    def _analyze_stock_news_sentiment(self, symbol: str) -> Dict:
        """Analyze news sentiment for specific stock"""
        try:
            # Mock implementation - would use news API and sentiment analysis
            return {'score': 0.2, 'articles_count': 5, 'confidence': 0.7}
        except Exception:
            return {'score': 0.0, 'articles_count': 0, 'confidence': 0.5}
    
    def _get_earnings_data(self, symbol: str) -> Dict:
        """Get earnings-related data for stock"""
        try:
            # Mock implementation - would fetch from earnings calendar
            return {
                'days_away': 12,
                'move_estimate': 0.08,
                'analyst_changes': []
            }
        except Exception:
            return {'days_away': None, 'move_estimate': 0.08, 'analyst_changes': []}
    
    def _analyze_stock_technicals(self, symbol: str) -> Dict:
        """Analyze stock-specific technical factors"""
        try:
            # Mock implementation
            return {
                'relative_strength': 0.3,
                'confluence_score': 0.7,
                'sr_clarity': 0.8,
                'trend_alignment': 'up'
            }
        except Exception:
            return {
                'relative_strength': 0.0,
                'confluence_score': 0.5,
                'sr_clarity': 0.5,
                'trend_alignment': 'neutral'
            }
    
    def _analyze_options_activity(self, symbol: str) -> Dict:
        """Analyze options activity for stock"""
        try:
            # Mock implementation
            return {
                'unusual_activity': False,
                'iv_rank': 0.6,
                'iv_percentile': 0.65,
                'iv_hv_ratio': 1.2,
                'flow_sentiment': 'neutral'
            }
        except Exception:
            return {
                'unusual_activity': False,
                'iv_rank': 0.5,
                'iv_percentile': 0.5,
                'iv_hv_ratio': 1.0,
                'flow_sentiment': 'neutral'
            }
    
    def _analyze_sector_impact(self, symbol: str) -> Dict:
        """Analyze sector impact on stock"""
        try:
            # Mock implementation
            return {
                'performance': 0.15,
                'rotation_impact': 'positive'
            }
        except Exception:
            return {'performance': 0.0, 'rotation_impact': 'neutral'}
    
    def _calculate_erica_factors(self, symbol: str, technical_data: Dict, options_data: Dict) -> Dict:
        """Calculate Erica's specific strategy factors"""
        try:
            # Mock implementation based on Erica's criteria
            return {
                'wheel_score': 0.7,
                'cc_score': 0.8,
                'cs_score': 0.6,
                'leaps_score': 0.5
            }
        except Exception:
            return {
                'wheel_score': 0.5,
                'cc_score': 0.5,
                'cs_score': 0.5,
                'leaps_score': 0.5
            }
