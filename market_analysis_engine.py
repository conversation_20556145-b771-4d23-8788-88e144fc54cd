"""
Multi-Factor Market Analysis Engine
Comprehensive market factor analysis for intelligent strategy selection

This module analyzes multiple market factors to provide intelligent strategy recommendations:
- Market sentiment indicators (VIX, put/call ratios, market breadth)
- News sentiment analysis and earnings calendar proximity
- Psychological indicators (fear/greed, social sentiment)
- Technical analysis confluence
- Sector rotation and relative strength
- Volatility regime detection

Based on <PERSON>'s decision-making framework from @AbundantlyErica
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import math
import requests
import json
from enhanced_fmp_api import EnhancedFMPClient, MarketIndicators, OptionsData, SectorPerformance

class MarketRegime(Enum):
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    TRANSITION = "transition"

class VolatilityRegime(Enum):
    LOW_VOL = "low_volatility"
    NORMAL_VOL = "normal_volatility"
    HIGH_VOL = "high_volatility"
    EXTREME_VOL = "extreme_volatility"
    # Backward-compatible aliases used in some tests
    LOW_VOLATILITY = LOW_VOL
    NORMAL_VOLATILITY = NORMAL_VOL
    HIGH_VOLATILITY = HIGH_VOL
    EXTREME_VOLATILITY = EXTREME_VOL

class SentimentLevel(Enum):
    EXTREME_FEAR = "extreme_fear"
    FEAR = "fear"
    NEUTRAL = "neutral"
    GREED = "greed"
    EXTREME_GREED = "extreme_greed"
    # Alias for older tests that use BULLISH/BEARISH terms
    BULLISH = GREED

@dataclass
class MarketFactors:
    """Comprehensive market factor analysis results"""

    # Market Sentiment Indicators
    vix_level: float
    vix_percentile: float = 50.0  # 0-100 percentile vs 1-year range
    put_call_ratio: float = 1.0
    market_breadth: float = 1.0  # Advance/decline ratio

    # Volatility Analysis
    volatility_regime: VolatilityRegime = VolatilityRegime.NORMAL_VOL
    iv_rank_spy: float = 0.5  # SPY IV rank
    iv_term_structure: str = 'normal'  # contango/backwardation

    # Market Regime
    market_regime: MarketRegime = MarketRegime.SIDEWAYS_MARKET
    trend_strength: float = 0.0  # -1 to 1 scale
    regime_confidence: float = 0.5  # 0-1 confidence in regime classification

    # Psychological Indicators
    fear_greed_index: float = 50.0  # 0-100 scale
    sentiment_level: SentimentLevel = SentimentLevel.NEUTRAL
    social_sentiment: float = 0.0  # -1 to 1 scale

    # Technical Confluence
    spy_technical_score: float = 0.5  # Overall technical health of market
    sector_rotation_signal: str = 'neutral'  # growth/value/defensive rotation
    relative_strength_leaders: List[str] = None  # Leading sectors

    # News and Events
    earnings_season_intensity: float = 0.0  # 0-1 scale
    fed_meeting_proximity: int = 30  # Days until next Fed meeting
    major_events_this_week: List[str] = None

    # Factor Scores (for decision tree)
    bullish_factors: float = 0.5  # 0-1 composite bullish score
    bearish_factors: float = 0.5  # 0-1 composite bearish score
    volatility_factors: float = 0.5  # 0-1 volatility opportunity score
    uncertainty_factors: float = 0.5  # 0-1 uncertainty/risk score

    # Backward-compatible field names expected by some tests
    bullish_factor_score: float = 0.5
    bearish_factor_score: float = 0.5
    volatility_factor_score: float = 0.5
    uncertainty_factor_score: float = 0.5
    market_breadth_score: float = 0.5
    sector_rotation_score: float = 0.5
    earnings_calendar_intensity: float = 0.0
    fed_meeting_proximity_days: int = 30
    social_sentiment_score: float = 0.0
    major_events_impact: float = 0.0

    def __post_init__(self):
        # Initialize lists if None
        if self.relative_strength_leaders is None:
            self.relative_strength_leaders = []
        if self.major_events_this_week is None:
            self.major_events_this_week = []

@dataclass
class StockSpecificFactors:
    """Stock-specific factor analysis"""
    symbol: str

    # News and Events
    news_sentiment_score: float  # -1 to 1
    earnings_days_away: Optional[int]
    earnings_move_estimate: float = 0.08
    recent_analyst_changes: List[Dict] = None  # upgrades/downgrades

    # Technical Factors
    relative_strength_vs_spy: float = 0.0  # -1 to 1
    technical_confluence_score: float = 0.5  # 0-1
    support_resistance_clarity: float = 0.5  # 0-1
    trend_alignment: str = 'neutral'  # strong_up/up/neutral/down/strong_down

    # Options Activity
    unusual_options_activity: bool = False
    iv_rank: float = 0.5  # 0-1 IV rank for this stock
    iv_percentile: float = 0.5  # 0-1 IV percentile for this stock
    iv_vs_hv_ratio: float = 1.0  # IV vs Historical Volatility
    options_flow_sentiment: str = 'neutral'  # bullish/bearish/neutral
    options_volume: int = 1000  # Daily options volume
    bid_ask_spread: float = 0.05  # Options bid-ask spread

    # Sector Analysis
    sector_performance: float = 0.0  # Sector performance vs market
    sector_rotation_impact: str = 'neutral'  # positive/negative/neutral

    # Erica's Key Factors
    wheel_suitability_score: float = 0.5  # 0-1 based on Erica's criteria
    covered_call_attractiveness: float = 0.5  # 0-1 based on premium/risk
    credit_spread_opportunity: float = 0.5  # 0-1 based on market conditions
    leaps_opportunity: float = 0.5  # 0-1 based on long-term outlook

    # Backward-compatible/optional fields used in tests (place defaults at end)
    rsi: float = 50.0
    macd_signal: float = 0.0
    bollinger_position: float = 0.5
    volume_profile_score: float = 0.5
    support_resistance_score: float = 0.5
    earnings_surprise_history: float = 0.0
    analyst_sentiment_score: float = 0.0
    options_open_interest: int = 0
    gamma_exposure: float = 0.0
    delta_hedging_flow: float = 0.0

class MarketAnalysisEngine:
    """Comprehensive market analysis engine"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.cache = {}
        self.cache_expiry = {}
        self.enhanced_client = EnhancedFMPClient(api_key)
        
    def analyze_market_factors(self) -> MarketFactors:
        """Perform comprehensive market factor analysis"""
        
        # Get real-time market indicators from enhanced FMP client
        market_indicators = self.enhanced_client.get_market_indicators()

        # Get sector performance data
        sector_data = self.enhanced_client.get_sector_performance()

        # Get economic calendar
        economic_events = self.enhanced_client.get_economic_calendar()

        # Analyze volatility regime
        vol_regime = self._analyze_volatility_regime_from_vix(market_indicators.vix_level)

        # Detect market regime from sector rotation
        market_regime, trend_strength, regime_confidence = self._detect_market_regime_from_sectors(sector_data)

        # Calculate composite scores
        spy_technical = self._analyze_spy_technicals()
        earnings_intensity = self._calculate_earnings_intensity_from_calendar(economic_events)
        fed_proximity = self._get_fed_meeting_proximity_from_calendar(economic_events)
        
        # Calculate composite factor scores using real market data
        bullish_score = self._calculate_bullish_factors_from_indicators(
            market_indicators, spy_technical, sector_data
        )
        bearish_score = self._calculate_bearish_factors_from_indicators(
            market_indicators, spy_technical, sector_data
        )
        volatility_score = self._calculate_volatility_opportunity_from_vix(
            market_indicators.vix_level, vol_regime
        )
        uncertainty_score = self._calculate_uncertainty_factors_from_events(
            fed_proximity, economic_events, earnings_intensity
        )
        
        return MarketFactors(
            vix_level=market_indicators.vix_level,
            vix_percentile=market_indicators.vix_percentile,
            put_call_ratio=market_indicators.put_call_ratio,
            market_breadth=market_indicators.market_breadth,
            volatility_regime=vol_regime,
            iv_rank_spy=market_indicators.vix_percentile / 100.0,
            iv_term_structure='normal',
            market_regime=market_regime,
            trend_strength=trend_strength,
            regime_confidence=regime_confidence,
            fear_greed_index=market_indicators.fear_greed_index,
            sentiment_level=self._classify_sentiment(market_indicators.fear_greed_index),
            social_sentiment=0.5,
            spy_technical_score=spy_technical,
            sector_rotation_signal=self._get_dominant_sector_signal(sector_data),
            relative_strength_leaders=self._get_leading_sectors(sector_data),
            earnings_season_intensity=earnings_intensity,
            fed_meeting_proximity=fed_proximity,
            major_events_this_week=[event.event_name for event in economic_events[:3]],
            bullish_factors=bullish_score,
            bearish_factors=bearish_score,
            volatility_factors=volatility_score,
            uncertainty_factors=uncertainty_score
        )
    
    def analyze_stock_factors(self, symbol: str) -> StockSpecificFactors:
        """Analyze stock-specific factors for strategy selection"""

        try:
            # Get real-time news sentiment with error handling
            news_data = self.enhanced_client.get_news_sentiment([symbol])
            if news_data and isinstance(news_data, dict):
                news_sentiment = self._process_news_sentiment(news_data.get(symbol, []))
            else:
                news_sentiment = {'score': 0.0, 'articles_count': 0}

            # Get earnings data with error handling
            earnings_data = self.enhanced_client.get_earnings_calendar([symbol])
            if earnings_data and isinstance(earnings_data, dict):
                earnings_info = earnings_data.get(symbol, {})
            else:
                earnings_info = {}

            # Get options data with error handling
            options_data = self.enhanced_client.get_options_data(symbol)
            if not options_data or not isinstance(options_data, dict):
                options_data = self._get_fallback_options_data(symbol)

            # Get technical analysis (would use real price data)
            technical_data = self._analyze_stock_technicals_real(symbol)

            # Get sector performance with error handling
            sector_performance = self.enhanced_client.get_sector_performance()
            if sector_performance and isinstance(sector_performance, dict):
                sector_data = self._get_sector_for_symbol(symbol, sector_performance)
            else:
                sector_data = {'performance': 0.0, 'sector': 'Technology'}

            # Erica's specific factors
            erica_factors = self._calculate_erica_factors(symbol, technical_data, options_data)

        except Exception as e:
            self.logger.error(f"Error in analyze_stock_factors for {symbol}: {e}")
            # Return fallback data
            return self._get_fallback_stock_factors(symbol)

        return StockSpecificFactors(
            symbol=symbol,
            news_sentiment_score=news_sentiment.get('score', 0.0) if news_sentiment else 0.0,
            earnings_days_away=earnings_info.get('days_away') if earnings_info else None,
            earnings_move_estimate=earnings_info.get('move_estimate', 0.08) if earnings_info else 0.08,
            recent_analyst_changes=earnings_info.get('analyst_changes', []) if earnings_info else [],
            relative_strength_vs_spy=technical_data.get('relative_strength', 0.0),
            technical_confluence_score=technical_data.get('confluence_score', 0.5),
            support_resistance_clarity=technical_data.get('sr_clarity', 0.5),
            trend_alignment=technical_data.get('trend_alignment', 'neutral'),
            unusual_options_activity=options_data.get('unusual_activity', False),
            iv_rank=options_data.get('iv_rank', 0.5),
            iv_percentile=options_data.get('iv_percentile', 0.5),
            iv_vs_hv_ratio=options_data.get('iv_hv_ratio', 1.0),
            options_flow_sentiment=options_data.get('flow_sentiment', 'neutral'),
            options_volume=options_data.get('options_volume', 1000),
            bid_ask_spread=options_data.get('bid_ask_spread', 0.05),
            sector_performance=sector_data.get('performance', 0.0),
            sector_rotation_impact=sector_data.get('rotation_impact', 'neutral'),
            wheel_suitability_score=erica_factors.get('wheel_score', 0.5),
            covered_call_attractiveness=erica_factors.get('cc_score', 0.5),
            credit_spread_opportunity=erica_factors.get('cs_score', 0.5),
            leaps_opportunity=erica_factors.get('leaps_score', 0.5)
        )
    
    def _get_vix_data(self) -> Dict:
        """Get VIX data and calculate percentiles"""
        try:
            # In real implementation, would fetch from FMP or other source
            # For now, return mock data structure
            return {
                'level': 18.5,
                'percentile': 35.0,  # 35th percentile = relatively low
                'iv_rank': 0.35,
                'term_structure': 'contango'  # normal market conditions
            }
        except Exception:
            return {'level': 20.0, 'percentile': 50.0, 'iv_rank': 0.5, 'term_structure': 'normal'}
    
    def _get_put_call_ratio(self) -> Dict:
        """Get put/call ratio data"""
        try:
            # Mock implementation - would fetch real data
            return {
                'ratio': 0.85,  # Below 1.0 = more calls than puts (bullish)
                'percentile': 25.0  # Low percentile = bullish sentiment
            }
        except Exception:
            return {'ratio': 1.0, 'percentile': 50.0}
    
    def _get_market_breadth(self) -> Dict:
        """Get market breadth indicators"""
        try:
            # Mock implementation - advance/decline ratio
            return {
                'ratio': 1.8,  # More advancing than declining stocks
                'new_highs_lows': 2.5,  # More new highs than lows
                'up_volume_ratio': 0.65  # 65% of volume in advancing stocks
            }
        except Exception:
            return {'ratio': 1.0, 'new_highs_lows': 1.0, 'up_volume_ratio': 0.5}
    
    def _analyze_volatility_regime(self, vix_data: Dict) -> VolatilityRegime:
        """Determine current volatility regime"""
        vix_level = vix_data.get('level', 20.0)
        vix_percentile = vix_data.get('percentile', 50.0)
        
        if vix_level > 30 or vix_percentile > 90:
            return VolatilityRegime.EXTREME_VOL
        elif vix_level > 25 or vix_percentile > 75:
            return VolatilityRegime.HIGH_VOL
        elif vix_level < 15 or vix_percentile < 25:
            return VolatilityRegime.LOW_VOL
        else:
            return VolatilityRegime.NORMAL_VOL
    
    def _detect_market_regime(self) -> Tuple[MarketRegime, float, float]:
        """Detect current market regime with confidence"""
        try:
            # Mock implementation - would analyze SPY, QQQ, market internals
            # This would look at:
            # - Price vs moving averages
            # - Market breadth
            # - Sector performance
            # - Momentum indicators
            
            trend_strength = 0.6  # Moderate bullish trend
            regime_confidence = 0.75  # High confidence
            
            if trend_strength > 0.5:
                regime = MarketRegime.BULL_MARKET
            elif trend_strength < -0.5:
                regime = MarketRegime.BEAR_MARKET
            else:
                regime = MarketRegime.SIDEWAYS_MARKET
            
            return regime, trend_strength, regime_confidence
            
        except Exception:
            return MarketRegime.SIDEWAYS_MARKET, 0.0, 0.5

    def _analyze_volatility_regime_from_vix(self, vix_level: float) -> VolatilityRegime:
        """Determine volatility regime from VIX level"""
        if vix_level < 15:
            return VolatilityRegime.LOW_VOL
        elif vix_level > 25:
            return VolatilityRegime.HIGH_VOL
        else:
            return VolatilityRegime.NORMAL_VOL

    def _detect_market_regime_from_sectors(self, sector_data: List[SectorPerformance]) -> Tuple[MarketRegime, float, float]:
        """Detect market regime from sector performance"""
        if not sector_data:
            return MarketRegime.SIDEWAYS_MARKET, 0.0, 0.5

        # Calculate average sector performance
        avg_performance = sum(sector.performance_1d for sector in sector_data) / len(sector_data)

        # Count sectors with positive performance
        positive_sectors = sum(1 for sector in sector_data if sector.performance_1d > 0)
        sector_breadth = positive_sectors / len(sector_data)

        # Determine regime
        if avg_performance > 0.01 and sector_breadth > 0.6:
            regime = MarketRegime.BULL_MARKET
            trend_strength = min(avg_performance * 10, 1.0)
        elif avg_performance < -0.01 and sector_breadth < 0.4:
            regime = MarketRegime.BEAR_MARKET
            trend_strength = max(avg_performance * 10, -1.0)
        else:
            regime = MarketRegime.SIDEWAYS_MARKET
            trend_strength = avg_performance * 5

        confidence = abs(trend_strength) * sector_breadth

        return regime, trend_strength, confidence

    def _get_dominant_sector_signal(self, sector_data: List[SectorPerformance]) -> str:
        """Get dominant sector rotation signal"""
        if not sector_data:
            return "neutral"

        inflow_count = sum(1 for sector in sector_data if sector.rotation_signal == "inflow")
        outflow_count = sum(1 for sector in sector_data if sector.rotation_signal == "outflow")

        if inflow_count > outflow_count * 1.5:
            return "risk_on"
        elif outflow_count > inflow_count * 1.5:
            return "risk_off"
        else:
            return "neutral"

    def _get_leading_sectors(self, sector_data: List[SectorPerformance]) -> List[str]:
        """Get leading sectors by relative strength"""
        if not sector_data:
            return []

        # Sort by relative strength and return top 3
        sorted_sectors = sorted(sector_data, key=lambda x: x.relative_strength, reverse=True)
        return [sector.sector_name for sector in sorted_sectors[:3]]

    def _calculate_earnings_intensity_from_calendar(self, economic_events: List) -> float:
        """Calculate earnings season intensity from economic calendar"""
        # Count high-importance earnings-related events in next 7 days
        earnings_events = [event for event in economic_events
                          if 'earnings' in event.event_name.lower() and event.importance == 'high']

        # Normalize to 0-1 scale
        return min(len(earnings_events) / 10.0, 1.0)

    def _get_fed_meeting_proximity_from_calendar(self, economic_events: List) -> int:
        """Get days until next Fed meeting from economic calendar"""
        fed_events = [event for event in economic_events
                     if 'fed' in event.event_name.lower() or 'fomc' in event.event_name.lower()]

        if fed_events:
            # Return days until next Fed event
            next_fed = min(fed_events, key=lambda x: x.date)
            return (next_fed.date - datetime.now()).days

        return 30  # Default if no Fed events found

    def _calculate_bullish_factors_from_indicators(self, market_indicators: MarketIndicators,
                                                  spy_technical: float, sector_data: List[SectorPerformance]) -> float:
        """Calculate bullish factors from real market data"""
        factors = []

        # VIX factor (low VIX = bullish)
        vix_factor = max(0, (30 - market_indicators.vix_level) / 30)
        factors.append(vix_factor * 0.3)

        # Put/call ratio factor (low ratio = bullish)
        pc_factor = max(0, (1.2 - market_indicators.put_call_ratio) / 1.2)
        factors.append(pc_factor * 0.2)

        # Market breadth factor
        breadth_factor = min(1.0, market_indicators.market_breadth / 2.0)
        factors.append(breadth_factor * 0.2)

        # Fear/greed factor
        fg_factor = market_indicators.fear_greed_index / 100.0
        factors.append(fg_factor * 0.2)

        # Technical factor
        factors.append(spy_technical * 0.1)

        return sum(factors)

    def _calculate_bearish_factors_from_indicators(self, market_indicators: MarketIndicators,
                                                  spy_technical: float, sector_data: List[SectorPerformance]) -> float:
        """Calculate bearish factors from real market data"""
        return 1.0 - self._calculate_bullish_factors_from_indicators(market_indicators, spy_technical, sector_data)

    def _calculate_volatility_opportunity_from_vix(self, vix_level: float, vol_regime: VolatilityRegime) -> float:
        """Calculate volatility opportunity score from VIX"""
        if vol_regime == VolatilityRegime.HIGH_VOL:
            return min(1.0, vix_level / 30.0)
        elif vol_regime == VolatilityRegime.LOW_VOL:
            return max(0.0, (15 - vix_level) / 15.0)
        else:
            return 0.5

    def _calculate_uncertainty_factors_from_events(self, fed_proximity: int,
                                                  economic_events: List, earnings_intensity: float) -> float:
        """Calculate uncertainty factors from economic events"""
        uncertainty = 0.0

        # Fed meeting proximity
        if fed_proximity <= 7:
            uncertainty += 0.4
        elif fed_proximity <= 14:
            uncertainty += 0.2

        # High-importance economic events
        high_impact_events = [event for event in economic_events if event.importance == 'high']
        uncertainty += min(len(high_impact_events) / 5.0, 0.3)

        # Earnings intensity
        uncertainty += earnings_intensity * 0.3

        return min(uncertainty, 1.0)
    
    def _get_fear_greed_index(self) -> float:
        """Get CNN Fear & Greed Index or calculate similar metric"""
        try:
            # Mock implementation - would fetch from CNN or calculate similar
            return 65.0  # Moderate greed
        except Exception:
            return 50.0  # Neutral
    
    def _classify_sentiment(self, fear_greed: float) -> SentimentLevel:
        """Classify sentiment level based on fear/greed index"""
        if fear_greed <= 20:
            return SentimentLevel.EXTREME_FEAR
        elif fear_greed <= 40:
            return SentimentLevel.FEAR
        elif fear_greed <= 60:
            return SentimentLevel.NEUTRAL
        elif fear_greed <= 80:
            return SentimentLevel.GREED
        else:
            return SentimentLevel.EXTREME_GREED
    
    def _analyze_social_sentiment(self) -> float:
        """Analyze social media sentiment"""
        try:
            # Mock implementation - would analyze Twitter, Reddit, etc.
            return 0.3  # Moderately bullish social sentiment
        except Exception:
            return 0.0  # Neutral
    
    def _analyze_spy_technicals(self) -> float:
        """Analyze SPY technical health"""
        try:
            # Mock implementation - would analyze:
            # - Price vs moving averages
            # - RSI, MACD, other indicators
            # - Support/resistance levels
            # - Volume patterns
            return 0.7  # Strong technical health
        except Exception:
            return 0.5  # Neutral
    
    def _analyze_sector_rotation(self) -> Dict:
        """Analyze sector rotation patterns"""
        try:
            # Mock implementation - would analyze sector performance
            return {
                'signal': 'growth_rotation',
                'leaders': ['Technology', 'Communication Services'],
                'laggards': ['Utilities', 'Real Estate']
            }
        except Exception:
            return {'signal': 'neutral', 'leaders': [], 'laggards': []}
    
    def _calculate_earnings_intensity(self) -> float:
        """Calculate earnings season intensity"""
        try:
            # Mock implementation - would count earnings this week
            return 0.6  # Moderate earnings intensity
        except Exception:
            return 0.3  # Low intensity
    
    def _get_fed_meeting_proximity(self) -> int:
        """Days until next Fed meeting"""
        try:
            # Mock implementation - would check Fed calendar
            return 15  # 15 days until next meeting
        except Exception:
            return 30
    
    def _get_major_events(self) -> List[str]:
        """Get major market events this week"""
        try:
            # Mock implementation
            return ["CPI Report", "FOMC Minutes"]
        except Exception:
            return []
    
    def _calculate_bullish_factors(self, vix_data: Dict, put_call_data: Dict, 
                                 breadth_data: Dict, spy_technical: float, 
                                 fear_greed: float) -> float:
        """Calculate composite bullish factor score"""
        factors = []
        
        # Low VIX is bullish
        if vix_data.get('percentile', 50) < 50:
            factors.append(0.8)
        
        # Low put/call ratio is bullish
        if put_call_data.get('ratio', 1.0) < 1.0:
            factors.append(0.7)
        
        # Good market breadth is bullish
        if breadth_data.get('ratio', 1.0) > 1.2:
            factors.append(0.8)
        
        # Strong technicals are bullish
        if spy_technical > 0.6:
            factors.append(spy_technical)
        
        # Moderate greed is bullish (not extreme)
        if 50 < fear_greed < 80:
            factors.append(0.7)
        
        return sum(factors) / len(factors) if factors else 0.5
    
    def _calculate_bearish_factors(self, vix_data: Dict, put_call_data: Dict,
                                 breadth_data: Dict, spy_technical: float,
                                 fear_greed: float) -> float:
        """Calculate composite bearish factor score"""
        factors = []
        
        # High VIX is bearish
        if vix_data.get('percentile', 50) > 75:
            factors.append(0.8)
        
        # High put/call ratio is bearish
        if put_call_data.get('ratio', 1.0) > 1.3:
            factors.append(0.7)
        
        # Poor market breadth is bearish
        if breadth_data.get('ratio', 1.0) < 0.8:
            factors.append(0.8)
        
        # Weak technicals are bearish
        if spy_technical < 0.4:
            factors.append(1.0 - spy_technical)
        
        # Extreme fear is bearish
        if fear_greed < 30:
            factors.append(0.8)
        
        return sum(factors) / len(factors) if factors else 0.5
    
    def _calculate_volatility_opportunity(self, vix_data: Dict, vol_regime: VolatilityRegime) -> float:
        """Calculate volatility opportunity score for premium selling"""
        base_score = 0.5
        
        # High VIX percentile increases opportunity
        vix_percentile = vix_data.get('percentile', 50)
        if vix_percentile > 70:
            base_score += 0.3
        elif vix_percentile > 50:
            base_score += 0.1
        
        # Volatility regime adjustment
        if vol_regime == VolatilityRegime.HIGH_VOL:
            base_score += 0.2
        elif vol_regime == VolatilityRegime.EXTREME_VOL:
            base_score += 0.3
        elif vol_regime == VolatilityRegime.LOW_VOL:
            base_score -= 0.2
        
        return min(base_score, 1.0)
    
    def _calculate_uncertainty_factors(self, fed_proximity: int, major_events: List[str],
                                     earnings_intensity: float) -> float:
        """Calculate uncertainty/risk factor score"""
        uncertainty = 0.0
        
        # Fed meeting proximity increases uncertainty
        if fed_proximity <= 7:
            uncertainty += 0.3
        elif fed_proximity <= 14:
            uncertainty += 0.1
        
        # Major events increase uncertainty
        uncertainty += len(major_events) * 0.1
        
        # High earnings intensity increases uncertainty
        uncertainty += earnings_intensity * 0.2
        
        return min(uncertainty, 1.0)

    def _process_news_sentiment(self, news_articles: List[dict]) -> dict:
        """Process news sentiment from articles"""
        if not news_articles:
            return {'score': 0.0, 'articles_count': 0, 'confidence': 0.5}

        total_sentiment = 0.0
        article_count = len(news_articles)

        for article in news_articles:
            sentiment_score = article.get('sentiment_score', 0.0)
            total_sentiment += sentiment_score

        avg_sentiment = total_sentiment / article_count if article_count > 0 else 0.0

        return {
            'score': avg_sentiment,
            'articles_count': article_count,
            'confidence': min(1.0, article_count / 10.0)  # More articles = higher confidence
        }

    def _analyze_stock_technicals_real(self, symbol: str) -> dict:
        """Analyze stock technicals using real data"""
        try:
            # This would use real price data for technical analysis
            # For now, return mock data with proper structure
            return {
                'relative_strength': 0.0,
                'confluence_score': 0.5,
                'sr_clarity': 0.5,
                'trend_alignment': 'neutral'
            }
        except Exception as e:
            self.logger.error(f"Error analyzing technicals for {symbol}: {e}")
            return {
                'relative_strength': 0.0,
                'confluence_score': 0.5,
                'sr_clarity': 0.5,
                'trend_alignment': 'neutral'
            }

    def _get_sector_for_symbol(self, symbol: str, sector_data: List[SectorPerformance]) -> dict:
        """Get sector data for a specific symbol"""
        # Map symbols to sectors (simplified)
        symbol_sectors = {
            'AAPL': 'Technology',
            'NVDA': 'Technology',
            'AMD': 'Technology',
            'GOOGL': 'Communication Services',
            'AMZN': 'Consumer Discretionary'
        }

        sector_name = symbol_sectors.get(symbol, 'Technology')

        for sector in sector_data:
            if sector.sector_name == sector_name:
                return {
                    'performance': sector.performance_1d,
                    'rotation_impact': sector.rotation_signal
                }

        return {'performance': 0.0, 'rotation_impact': 'neutral'}

    def _get_fallback_options_data(self, symbol: str) -> dict:
        """Provide fallback options data when API fails"""
        return {
            'iv_rank': 50.0,  # Neutral IV rank
            'iv_percentile': 50.0,
            'iv_hv_ratio': 1.0,
            'unusual_activity': False,
            'flow_sentiment': 'neutral',
            'put_call_ratio': 1.0,
            'volume': 1000,
            'bid_ask_spread': 0.05,
            'options_volume': 1000
        }

    def _get_fallback_stock_factors(self, symbol: str) -> 'StockSpecificFactors':
        """Provide fallback stock factors when analysis fails"""
        return StockSpecificFactors(
            symbol=symbol,
            news_sentiment_score=0.0,
            earnings_days_away=None,
            earnings_move_estimate=0.08,
            recent_analyst_changes=[],
            relative_strength_vs_spy=0.0,
            technical_confluence_score=0.5,
            support_resistance_clarity=0.5,
            trend_alignment="neutral",
            unusual_options_activity=False,
            iv_rank=50.0,
            iv_percentile=50.0,
            iv_vs_hv_ratio=1.0,
            options_flow_sentiment="neutral",
            options_volume=1000,
            bid_ask_spread=0.05,
            sector_performance=0.0,
            sector_rotation_impact="neutral",
            wheel_suitability_score=0.5,
            covered_call_attractiveness=0.5,
            credit_spread_opportunity=0.5,
            leaps_opportunity=0.5
        )

    def _calculate_wheel_score(self, options_data: OptionsData, technical_data: dict) -> float:
        """Calculate wheel strategy suitability score"""
        if not options_data:
            return 0.5

        # High IV rank favors wheel strategy
        iv_score = options_data.iv_rank

        # Stable technical environment favors wheel
        tech_score = 0.5 if technical_data.get('trend_alignment') == 'neutral' else 0.3

        return (iv_score * 0.7 + tech_score * 0.3)

    def _calculate_cc_score(self, options_data: OptionsData, technical_data: dict) -> float:
        """Calculate covered call attractiveness score"""
        if not options_data:
            return 0.5

        # Moderate IV rank and stable trend favor covered calls
        iv_score = min(1.0, options_data.iv_rank * 1.5)
        trend_score = 0.7 if technical_data.get('trend_alignment') in ['neutral', 'bullish'] else 0.3

        return (iv_score * 0.6 + trend_score * 0.4)

    def _calculate_cs_score(self, options_data: OptionsData, technical_data: dict) -> float:
        """Calculate credit spread environment score"""
        if not options_data:
            return 0.5

        # High IV rank and bullish trend favor credit spreads
        iv_score = options_data.iv_rank
        trend_score = 0.8 if technical_data.get('trend_alignment') == 'bullish' else 0.4

        return (iv_score * 0.5 + trend_score * 0.5)

    def _calculate_leaps_score(self, options_data: OptionsData, technical_data: dict) -> float:
        """Calculate LEAPS opportunity score"""
        if not options_data:
            return 0.5

        # Low IV rank and strong trend favor LEAPS
        iv_score = 1.0 - options_data.iv_rank  # Inverted - low IV is better
        trend_score = 0.8 if technical_data.get('trend_alignment') == 'bullish' else 0.2

        return (iv_score * 0.4 + trend_score * 0.6)

    # Stock-specific analysis methods
    def _analyze_stock_news_sentiment(self, symbol: str) -> Dict:
        """Analyze news sentiment for specific stock"""
        try:
            # Mock implementation - would use news API and sentiment analysis
            return {'score': 0.2, 'articles_count': 5, 'confidence': 0.7}
        except Exception:
            return {'score': 0.0, 'articles_count': 0, 'confidence': 0.5}
    
    def _get_earnings_data(self, symbol: str) -> Dict:
        """Get earnings-related data for stock"""
        try:
            # Mock implementation - would fetch from earnings calendar
            return {
                'days_away': 12,
                'move_estimate': 0.08,
                'analyst_changes': []
            }
        except Exception:
            return {'days_away': None, 'move_estimate': 0.08, 'analyst_changes': []}
    
    def _analyze_stock_technicals(self, symbol: str) -> Dict:
        """Analyze stock-specific technical factors"""
        try:
            # Mock implementation
            return {
                'relative_strength': 0.3,
                'confluence_score': 0.7,
                'sr_clarity': 0.8,
                'trend_alignment': 'up'
            }
        except Exception:
            return {
                'relative_strength': 0.0,
                'confluence_score': 0.5,
                'sr_clarity': 0.5,
                'trend_alignment': 'neutral'
            }
    
    def _analyze_options_activity(self, symbol: str) -> Dict:
        """Analyze options activity for stock"""
        try:
            # Mock implementation
            return {
                'unusual_activity': False,
                'iv_rank': 0.6,
                'iv_percentile': 0.65,
                'iv_hv_ratio': 1.2,
                'flow_sentiment': 'neutral'
            }
        except Exception:
            return {
                'unusual_activity': False,
                'iv_rank': 0.5,
                'iv_percentile': 0.5,
                'iv_hv_ratio': 1.0,
                'flow_sentiment': 'neutral'
            }
    
    def _analyze_sector_impact(self, symbol: str) -> Dict:
        """Analyze sector impact on stock"""
        try:
            # Mock implementation
            return {
                'performance': 0.15,
                'rotation_impact': 'positive'
            }
        except Exception:
            return {'performance': 0.0, 'rotation_impact': 'neutral'}
    
    def _calculate_erica_factors(self, symbol: str, technical_data: Dict, options_data: Dict) -> Dict:
        """Calculate Erica's specific strategy factors"""
        try:
            # Mock implementation based on Erica's criteria
            return {
                'wheel_score': 0.7,
                'cc_score': 0.8,
                'cs_score': 0.6,
                'leaps_score': 0.5
            }
        except Exception:
            return {
                'wheel_score': 0.5,
                'cc_score': 0.5,
                'cs_score': 0.5,
                'leaps_score': 0.5
            }
