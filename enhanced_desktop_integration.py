"""
Enhanced Desktop Integration
Integrates the enhanced strategy analyzer with the existing desktop application

This module provides:
1. Enhanced strategy display widgets
2. Detailed criteria visualization
3. Strategy documentation viewer
4. Integration with existing desktop app
5. Export capabilities for detailed analysis

Date: August 18, 2025
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from typing import Dict, List, Optional
import json
import os
import webbrowser
from datetime import datetime

from enhanced_strategy_analyzer import <PERSON>hancedStrategyAnalyzer, DetailedStrategyAnalysis, AnalysisDepth
from daily_outline import StrategyType

class StrategyDocumentationViewer:
    """Widget to display <PERSON>'s strategy documentation and criteria"""

    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup the documentation viewer UI"""

        # Title
        title_label = ttk.Label(self.frame, text="Erica's Strategy Documentation",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        # Strategy selector
        strategy_frame = ttk.Frame(self.frame)
        strategy_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(strategy_frame, text="Strategy:").pack(side=tk.LEFT)

        self.strategy_var = tk.StringVar()
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.strategy_var,
                                     values=["Covered Calls", "Put Credit Spreads", "LEAPS", "Premium Selling"],
                                     state="readonly")
        strategy_combo.pack(side=tk.LEFT, padx=5)
        strategy_combo.bind('<<ComboboxSelected>>', self.on_strategy_selected)

        # Documentation display
        self.doc_text = scrolledtext.ScrolledText(self.frame, height=20, width=80)
        self.doc_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Buttons row
        btn_frame = ttk.Frame(self.frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=(0,10))
        ttk.Button(btn_frame, text="Open PDF", command=self.open_pdf_for_current).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="Refresh", command=lambda: self.load_strategy_documentation(self.strategy_var.get() or "Covered Calls")).pack(side=tk.LEFT, padx=5)

        # Load initial documentation
        self.load_strategy_documentation("Covered Calls")


    def open_pdf_for_current(self):
        """Open the relevant PDF slide deck for the selected strategy"""
        strategy = (self.strategy_var.get() or "Covered Calls").strip()
        base_dir = r"C:\\Users\\<USER>\\Documents\\EricaSlides"
        pdf_map = {
            "Covered Calls": [
                os.path.join(base_dir, "19. New Covered Call.pdf"),
                os.path.join(base_dir, "6. Rich Woman's Covered Call - Diagonal Spread - Trading Guide.pdf"),
                os.path.join(base_dir, "7. Poor Man's Covered Call - Diagonal Spread - Trading Guide.pdf"),
            ],
            "Put Credit Spreads": [
                os.path.join(base_dir, "5. Put Credit Spread - Trading Guide.pdf"),
                os.path.join(base_dir, "4. Erica's Bullish Spread - Trading Guide.pdf"),
                os.path.join(base_dir, "Bear Call Spreads What To do Next.pdf"),
                os.path.join(base_dir, "3. PDF of What Vertical Spreads Look Like with Stock Price.pdf"),
            ],
            "LEAPS": [
                os.path.join(base_dir, "17.  Leaps Slides.pdf"),
                os.path.join(base_dir, "17.  Leaps Slides (1).pdf"),
            ],
            "Premium Selling": [
                os.path.join(base_dir, "7. The Wheel.pdf"),
                os.path.join(base_dir, "17. Cash Secured Puts.pdf"),
                os.path.join(base_dir, "10. Calendar Spreads - Time for Profits.pdf"),
                os.path.join(base_dir, "12. Rollout (and in).pdf"),
                os.path.join(base_dir, "8. Wealth Building .pdf"),
            ],
        }
        candidates = pdf_map.get(strategy, [])
        for filename in candidates:
            if os.path.exists(filename):
                try:
                    webbrowser.open_new(filename)
                    return
                except Exception:
                    continue
        messagebox.showinfo("Open PDF", f"No local PDF found for {strategy}. Files checked: {', '.join(candidates) if candidates else 'None'}")


    def on_strategy_selected(self, event=None):
        """Handle strategy selection"""
        strategy = self.strategy_var.get()
        self.load_strategy_documentation(strategy)

    def load_strategy_documentation(self, strategy_name: str):
        """Load and display strategy documentation"""

        documentation = {
            "Covered Calls": """
ERICA'S COVERED CALL STRATEGY

📋 BASIC REQUIREMENTS:
• Own 100 shares of stock
• Sell call options against the shares
• Target 30-45 DTE (Days to Expiration)
• Target 0.30 delta
• Minimum $30 premium per contract

🎯 ERICA'S SPECIFIC RULES:
• Profit Target: 50% of maximum profit
• Management: Close at 21 DTE if not profitable
• IV Rank: Minimum 50th percentile
• Delta Management: Roll if exceeds 0.35

📊 EARNINGS ADJUSTMENTS:
• Near Earnings: Maximum 0.10 delta
• Use Expected Move: Place strikes beyond expected move
• Buffer: Add 10-15% buffer to expected move

💡 STOCK-SPECIFIC RULES:
• AMD: 2-5 DTE between earnings, 0.15-0.25 delta
• NVDA: Conservative delta near earnings
• AAPL: Fast money preferred, frequent execution
• GOOGL: Standard methodology
• AMZN: Wheel strategy preferred

⚠️ RISK MANAGEMENT:
• Monitor for early assignment
• Roll challenged positions
• Avoid earnings unless specifically targeting IV crush
• Consider gap risk for volatile stocks
            """,

            "Put Credit Spreads": """
ERICA'S PUT CREDIT SPREAD STRATEGY

📋 SETUP REQUIREMENTS:
• Bullish or neutral market bias required
• Sell put at 0.15-0.20 delta
• Buy protection 5-10 strikes lower
• 30-45 DTE preferred
• Close at 50% profit

🎯 MARKET CONDITIONS:
• Stock above key support levels
• Bullish market regime
• High IV rank (>60th percentile)
• 5% buffer from support levels

💡 ERICA'S BULLISH SPREAD PROCESS:
1. Set up Erica's Call Spread
2. Set Stock Alarm ($1 less than bottom leg)
3. Monitor for notifications:
   - No Alarm: Bull call spread (stock above strike)
   - Price Higher: Allow assignment
   - Price Lower: Let expire, sell new call

📊 MANAGEMENT RULES:
• Wait to construct to expire or close for profit
• Buy 100 shares to cover bottom leg if needed
• Sell upside leg for profit
• Roll if approaching support levels

⚠️ RISK FACTORS:
• Support level breakdown
• Market regime change
• Earnings proximity
• High correlation to market moves
            """,

            "LEAPS": """
ERICA'S LEAPS STRATEGY

📋 BASIC REQUIREMENTS:
• Buy calls with 12+ months expiration
• Target 0.70-0.80 delta
• Strong long-term growth stocks
• Holding period: 6-12 months typically

🎯 SELECTION CRITERIA:
• Strong fundamental growth story
• Consistent earnings growth
• Market leadership position
• Technical uptrend confirmed

📊 TIMING CONSIDERATIONS:
• Best purchased during low IV periods
• IV rank < 60th percentile preferred
• Time entry with fundamental catalysts
• Avoid during high volatility periods

💡 FUNDING STRATEGIES:
• Sell monthly calls against LEAPS
• Target 0.30 delta short calls
• Collect premium to reduce cost basis
• Manage short calls actively

⚠️ RISK MANAGEMENT:
• Monitor time decay acceleration
• Exit if fundamental thesis changes
• Take profits on significant moves
• Consider rolling up and out
            """,

            "Premium Selling": """
ERICA'S PREMIUM SELLING (WHEEL) STRATEGY

📋 SYSTEMATIC APPROACH:
• High IV rank required (>70th percentile)
• Comfortable owning the stock
• Consistent premium collection
• Accept assignment when favorable

🎯 EXECUTION PROCESS:
1. Sell cash-secured puts
2. If assigned, own stock
3. Sell covered calls against stock
4. If called away, repeat process

📊 SELECTION CRITERIA:
• Quality stocks suitable for ownership
• Consistent high implied volatility
• Strong support levels
• Liquid options market

💡 MANAGEMENT RULES:
• Systematic profit taking at 50-70%
• Roll challenged positions
• Maintain consistent approach
• Size positions appropriately

⚠️ CONSIDERATIONS:
• Capital intensive strategy
• Requires stock ownership comfort
• Market regime sensitivity
• Concentration risk management
            """
        }

        self.doc_text.delete(1.0, tk.END)
        self.doc_text.insert(1.0, documentation.get(strategy_name, "Documentation not available"))

class EnhancedAnalysisDisplay:
    """Enhanced display widget for detailed strategy analysis"""

    def __init__(self, parent, analyzer: EnhancedStrategyAnalyzer):
        self.parent = parent
        self.analyzer = analyzer
        self.frame = ttk.Frame(parent)
        self.current_analysis: Optional[DetailedStrategyAnalysis] = None
        self.setup_ui()

    def setup_ui(self):
        """Setup the enhanced analysis display UI"""

        # Control frame
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Symbol input
        ttk.Label(control_frame, text="Symbol:").pack(side=tk.LEFT)
        self.symbol_var = tk.StringVar()
        symbol_entry = ttk.Entry(control_frame, textvariable=self.symbol_var, width=10)
        symbol_entry.pack(side=tk.LEFT, padx=5)

        # Analyze button
        analyze_btn = ttk.Button(control_frame, text="Analyze", command=self.analyze_symbol)
        analyze_btn.pack(side=tk.LEFT, padx=5)

        # Export button
        export_btn = ttk.Button(control_frame, text="Export Analysis", command=self.export_analysis)
        export_btn.pack(side=tk.LEFT, padx=5)

        # Analysis depth selector
        ttk.Label(control_frame, text="Depth:").pack(side=tk.LEFT, padx=(20, 5))
        self.depth_var = tk.StringVar(value="comprehensive")
        depth_combo = ttk.Combobox(control_frame, textvariable=self.depth_var,
                                  values=["basic", "detailed", "comprehensive"],
                                  state="readonly", width=12)
        depth_combo.pack(side=tk.LEFT, padx=5)

        # Create notebook for different analysis sections
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Strategy Overview tab
        self.overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.overview_frame, text="Strategy Overview")
        self.setup_overview_tab()

        # Criteria Analysis tab
        self.criteria_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.criteria_frame, text="Criteria Analysis")
        self.setup_criteria_tab()

        # Execution Details tab
        self.execution_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.execution_frame, text="Execution Details")
        self.setup_execution_tab()

        # Supporting Data tab
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="Supporting Data")
        self.setup_data_tab()

    def setup_overview_tab(self):
        """Setup strategy overview tab"""
        self.overview_text = scrolledtext.ScrolledText(self.overview_frame, height=25)
        self.overview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def setup_criteria_tab(self):
        """Setup criteria analysis tab"""
        self.criteria_text = scrolledtext.ScrolledText(self.criteria_frame, height=25)
        self.criteria_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def setup_execution_tab(self):
        """Setup execution details tab"""
        self.execution_text = scrolledtext.ScrolledText(self.execution_frame, height=25)
        self.execution_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def setup_data_tab(self):
        """Setup supporting data tab"""
        self.data_text = scrolledtext.ScrolledText(self.data_frame, height=25)
        self.data_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def analyze_symbol(self):
        """Analyze the entered symbol"""
        symbol = self.symbol_var.get().upper().strip()
        if not symbol:
            messagebox.showwarning("Warning", "Please enter a symbol")
            return

        try:
            # Show progress
            self.clear_displays()
            self.overview_text.insert(tk.END, f"Analyzing {symbol}...\n")
            self.parent.update()

            # Perform analysis
            depth_map = {
                "basic": AnalysisDepth.BASIC,
                "detailed": AnalysisDepth.DETAILED,
                "comprehensive": AnalysisDepth.COMPREHENSIVE
            }
            depth = depth_map[self.depth_var.get()]

            analysis = self.analyzer.analyze_stock_comprehensive(symbol, depth)
            self.current_analysis = analysis

            # Display results
            self.display_analysis(analysis)

        except Exception as e:
            messagebox.showerror("Error", f"Analysis failed: {str(e)}")

    def clear_displays(self):
        """Clear all display areas"""
        self.overview_text.delete(1.0, tk.END)
        self.criteria_text.delete(1.0, tk.END)
        self.execution_text.delete(1.0, tk.END)
        self.data_text.delete(1.0, tk.END)

    def display_analysis(self, analysis: DetailedStrategyAnalysis):
        """Display the comprehensive analysis"""

        # Overview tab
        overview = f"""
STRATEGY ANALYSIS - {analysis.symbol}
Analysis Date: {analysis.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}

PRIMARY STRATEGY: {analysis.primary_strategy.value.upper().replace('_', ' ')}
Confidence: {analysis.strategy_confidence.value.upper().replace('_', ' ')} ({analysis.strategy_score:.1f}%)

WHY THIS STRATEGY:
{analysis.why_this_strategy}

INVESTMENT THESIS:
{analysis.investment_thesis}

ERICA'S REASONING:
{analysis.erica_reasoning}

MARKET ENVIRONMENT:
{analysis.market_environment}

ANALYST SENTIMENT: {analysis.analyst_sentiment}
        """
        self.overview_text.insert(1.0, overview.strip())

        # Criteria tab
        criteria_text = f"CRITERIA ANALYSIS - {analysis.symbol}\n"
        criteria_text += f"Summary: {analysis.criteria_summary}\n\n"

        for i, criteria in enumerate(analysis.criteria_matches, 1):
            status = "✅ MET" if criteria.is_met else "❌ NOT MET"
            criteria_text += f"{i}. {criteria.criterion_name} [{criteria.importance.upper()}] - {status}\n"
            criteria_text += f"   Required: {criteria.required_value}\n"
            criteria_text += f"   Actual: {criteria.actual_value}\n"
            criteria_text += f"   Score: {criteria.score:.1%}\n"
            criteria_text += f"   Explanation: {criteria.explanation}\n\n"

        self.criteria_text.insert(1.0, criteria_text)

        # Execution tab
        execution_text = f"EXECUTION DETAILS - {analysis.symbol}\n\n"

        execution_text += "ENTRY CRITERIA:\n"
        for i, criteria in enumerate(analysis.entry_criteria, 1):
            execution_text += f"{i}. {criteria}\n"

        execution_text += "\nEXIT CRITERIA:\n"
        for i, criteria in enumerate(analysis.exit_criteria, 1):
            execution_text += f"{i}. {criteria}\n"

        execution_text += f"\nPOSITION SIZING:\n"
        sizing = analysis.position_sizing
        execution_text += f"Recommended Contracts: {sizing['recommended_contracts']}\n"
        execution_text += f"Final Multiplier: {sizing['final_multiplier']:.2f}\n"
        execution_text += f"Risk Considerations: {', '.join(sizing['risk_considerations'])}\n"

        if analysis.timing_considerations:
            execution_text += "\nTIMING CONSIDERATIONS:\n"
            for i, timing in enumerate(analysis.timing_considerations, 1):
                execution_text += f"{i}. {timing}\n"

        self.execution_text.insert(1.0, execution_text)

        # Data tab
        data_text = f"SUPPORTING DATA - {analysis.symbol}\n\n"

        data_text += "TECHNICAL INDICATORS:\n"
        for key, value in analysis.supporting_metrics.technical_indicators.items():
            data_text += f"  {key}: {value}\n"

        data_text += "\nOPTIONS METRICS:\n"
        for key, value in analysis.supporting_metrics.options_metrics.items():
            data_text += f"  {key}: {value}\n"

        data_text += "\nMARKET METRICS:\n"
        for key, value in analysis.supporting_metrics.market_metrics.items():
            data_text += f"  {key}: {value}\n"

        if analysis.key_catalysts:
            data_text += "\nKEY CATALYSTS:\n"
            for i, catalyst in enumerate(analysis.key_catalysts, 1):
                data_text += f"{i}. {catalyst.catalyst_type.upper()}: {catalyst.description}\n"
                data_text += f"   Impact: {catalyst.impact_level.upper()} | Probability: {catalyst.probability:.0%}\n"

        if analysis.alternative_strategies:
            data_text += "\nALTERNATIVE STRATEGIES:\n"
            for i, (strategy, score, reason) in enumerate(analysis.alternative_strategies, 1):
                data_text += f"{i}. {strategy.value.replace('_', ' ').title()} ({score:.1%}) - {reason}\n"

        self.data_text.insert(1.0, data_text)

    def export_analysis(self):
        """Export the current analysis to a file"""
        if not self.current_analysis:
            messagebox.showwarning("Warning", "No analysis to export")
            return

        try:
            filename = f"{self.current_analysis.symbol}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

            with open(filename, 'w') as f:
                f.write(f"COMPREHENSIVE STRATEGY ANALYSIS - {self.current_analysis.symbol}\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")

                # Write all analysis details
                f.write(self.overview_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n")
                f.write(self.criteria_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n")
                f.write(self.execution_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n")
                f.write(self.data_text.get(1.0, tk.END))

            messagebox.showinfo("Success", f"Analysis exported to {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {str(e)}")

def create_enhanced_desktop_components(parent, api_key: str):
    """Create enhanced desktop components"""

    # Create analyzer
    analyzer = EnhancedStrategyAnalyzer(api_key)

    # Create main notebook
    main_notebook = ttk.Notebook(parent)
    main_notebook.pack(fill=tk.BOTH, expand=True)

    # Strategy Documentation tab
    doc_frame = ttk.Frame(main_notebook)
    main_notebook.add(doc_frame, text="Strategy Documentation")
    doc_viewer = StrategyDocumentationViewer(doc_frame)
    doc_viewer.frame.pack(fill=tk.BOTH, expand=True)

    # Enhanced Analysis tab
    analysis_frame = ttk.Frame(main_notebook)
    main_notebook.add(analysis_frame, text="Enhanced Analysis")
    analysis_display = EnhancedAnalysisDisplay(analysis_frame, analyzer)
    analysis_display.frame.pack(fill=tk.BOTH, expand=True)

    return {
        'notebook': main_notebook,
        'doc_viewer': doc_viewer,
        'analysis_display': analysis_display,
        'analyzer': analyzer
    }
