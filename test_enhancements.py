"""
Comprehensive Test Suite for AI Assistant and Live Data Integration
Tests both enhancements to ensure functionality, performance, and reliability

Test Coverage:
- AI Assistant functionality and OpenAI integration
- Real-time data feeds and FMP API integration
- Data refresh mechanisms and error handling
- GUI integration and user interactions
- Performance and reliability testing
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import tkinter as tk
from datetime import datetime, timedelta

# Import modules to test
from ai_assistant import AIAssistantGUI, AIConversationManager, AIAssistantConfig, DataAccessLayer
from enhanced_fmp_api import EnhancedFMPClient, MarketIndicators, OptionsData
from realtime_data_manager import RealTimeDataManager, RefreshInterval, DataUpdate
from desktop_app import TradingSystemGUI

class TestAIAssistant(unittest.TestCase):
    """Test AI Assistant functionality"""
    
    def setUp(self):
        """Setup test environment"""
        self.mock_openai_key = "test_openai_key"
        self.mock_fmp_key = "test_fmp_key"
        self.test_symbols = ["AAPL", "NVDA", "AMD"]
        
    def test_ai_config_creation(self):
        """Test AI Assistant configuration"""
        config = AIAssistantConfig(
            openai_api_key=self.mock_openai_key,
            model="gpt-4",
            max_tokens=1500,
            temperature=0.7
        )
        
        self.assertEqual(config.openai_api_key, self.mock_openai_key)
        self.assertEqual(config.model, "gpt-4")
        self.assertEqual(config.max_tokens, 1500)
        self.assertEqual(config.temperature, 0.7)
    
    def test_data_access_layer_initialization(self):
        """Test DataAccessLayer initialization"""
        data_access = DataAccessLayer(self.mock_fmp_key)
        
        self.assertEqual(data_access.fmp_api_key, self.mock_fmp_key)
        self.assertIsNotNone(data_access.market_analyzer)
        self.assertIsNotNone(data_access.strategy_engine)
        self.assertIsNotNone(data_access.erica_framework)
    
    @patch('ai_assistant.OpenAI')
    def test_conversation_manager_initialization(self, mock_openai):
        """Test AI conversation manager initialization"""
        config = AIAssistantConfig(openai_api_key=self.mock_openai_key)
        data_access = DataAccessLayer(self.mock_fmp_key)
        
        conversation_manager = AIConversationManager(config, data_access)
        
        self.assertEqual(conversation_manager.config, config)
        self.assertEqual(conversation_manager.data_access, data_access)
        self.assertIsNotNone(conversation_manager.system_prompt)
        self.assertTrue(len(conversation_manager.conversation_history) > 0)
    
    @patch('ai_assistant.OpenAI')
    def test_ai_response_generation(self, mock_openai):
        """Test AI response generation"""
        # Setup mocks
        mock_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test AI response"
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # Create conversation manager
        config = AIAssistantConfig(openai_api_key=self.mock_openai_key)
        data_access = DataAccessLayer(self.mock_fmp_key)
        conversation_manager = AIConversationManager(config, data_access)
        
        # Test response generation
        response = conversation_manager.generate_response("What's the best strategy for AAPL?")
        
        self.assertEqual(response, "Test AI response")
        mock_client.chat.completions.create.assert_called_once()
    
    def test_ai_gui_creation(self):
        """Test AI Assistant GUI creation"""
        root = tk.Tk()
        frame = tk.Frame(root)
        
        # Mock conversation manager
        mock_manager = Mock()
        
        try:
            ai_gui = AIAssistantGUI(frame, mock_manager)
            self.assertIsNotNone(ai_gui.chat_history)
            self.assertIsNotNone(ai_gui.question_entry)
            self.assertIsNotNone(ai_gui.send_button)
        finally:
            root.destroy()

class TestEnhancedFMPAPI(unittest.TestCase):
    """Test Enhanced FMP API integration"""
    
    def setUp(self):
        """Setup test environment"""
        self.mock_api_key = "test_fmp_key"
        self.test_symbols = ["AAPL", "NVDA", "AMD"]
    
    def test_enhanced_client_initialization(self):
        """Test Enhanced FMP Client initialization"""
        client = EnhancedFMPClient(self.mock_api_key)
        
        self.assertEqual(client.api_key, self.mock_api_key)
        self.assertIsNotNone(client.session)
        self.assertIsNotNone(client.cache)
        self.assertIsNotNone(client.cache_timestamps)
    
    @patch('enhanced_fmp_api.requests.Session.get')
    def test_real_time_quotes(self, mock_get):
        """Test real-time quotes fetching"""
        # Mock API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [{"symbol": "AAPL", "price": 150.0, "change": 2.5}]
        mock_get.return_value = mock_response
        
        client = EnhancedFMPClient(self.mock_api_key)
        quotes = client.get_real_time_quotes(["AAPL"])
        
        self.assertIn("AAPL", quotes)
        self.assertEqual(quotes["AAPL"]["price"], 150.0)
    
    @patch('enhanced_fmp_api.requests.Session.get')
    def test_market_indicators(self, mock_get):
        """Test market indicators fetching"""
        # Mock VIX response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [{"symbol": "^VIX", "price": 20.5}]
        mock_get.return_value = mock_response
        
        client = EnhancedFMPClient(self.mock_api_key)
        indicators = client.get_market_indicators()
        
        self.assertIsInstance(indicators, MarketIndicators)
        self.assertGreater(indicators.vix_level, 0)
        self.assertGreater(indicators.fear_greed_index, 0)
    
    @patch('enhanced_fmp_api.requests.Session.get')
    def test_options_data(self, mock_get):
        """Test options data fetching"""
        # Mock options response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {"type": "call", "volume": 1000, "bid": 5.0, "ask": 5.2},
            {"type": "put", "volume": 800, "bid": 3.0, "ask": 3.1}
        ]
        mock_get.return_value = mock_response
        
        client = EnhancedFMPClient(self.mock_api_key)
        options_data = client.get_options_data("AAPL")
        
        self.assertIsInstance(options_data, OptionsData)
        self.assertEqual(options_data.symbol, "AAPL")
        self.assertGreater(options_data.options_volume, 0)
    
    def test_error_handling(self):
        """Test API error handling"""
        client = EnhancedFMPClient(self.mock_api_key)
        
        # Test with invalid response
        with patch('enhanced_fmp_api.requests.Session.get') as mock_get:
            mock_get.side_effect = Exception("Network error")
            
            quotes = client.get_real_time_quotes(["AAPL"])
            self.assertEqual(quotes, {})

class TestRealTimeDataManager(unittest.TestCase):
    """Test Real-time Data Manager"""
    
    def setUp(self):
        """Setup test environment"""
        self.mock_api_key = "test_fmp_key"
        self.test_symbols = ["AAPL", "NVDA"]
    
    def test_data_manager_initialization(self):
        """Test data manager initialization"""
        manager = RealTimeDataManager(self.mock_api_key, self.test_symbols)
        
        self.assertEqual(manager.fmp_api_key, self.mock_api_key)
        self.assertEqual(manager.symbols, self.test_symbols)
        self.assertFalse(manager.running)
        self.assertGreater(len(manager.subscriptions), 0)
    
    def test_subscription_management(self):
        """Test subscription add/remove functionality"""
        manager = RealTimeDataManager(self.mock_api_key, self.test_symbols)
        
        # Test adding subscription
        def test_refresh():
            return {"test": "data"}
        
        manager.add_subscription("test_sub", test_refresh, RefreshInterval.FREQUENT)
        
        self.assertIn("test_sub", manager.subscriptions)
        self.assertEqual(manager.subscriptions["test_sub"].interval, RefreshInterval.FREQUENT)
    
    def test_callback_system(self):
        """Test callback system"""
        manager = RealTimeDataManager(self.mock_api_key, self.test_symbols)
        
        callback_called = False
        test_data = None
        
        def test_callback(update):
            nonlocal callback_called, test_data
            callback_called = True
            test_data = update
        
        manager.add_callback("stock_quotes", test_callback)
        
        # Simulate update
        update = DataUpdate(
            subscription_name="stock_quotes",
            data={"AAPL": {"price": 150.0}},
            timestamp=datetime.now(),
            success=True
        )
        
        # Process update manually
        for callback in manager.callbacks["stock_quotes"]:
            callback(update)
        
        self.assertTrue(callback_called)
        self.assertEqual(test_data.data["AAPL"]["price"], 150.0)
    
    def test_force_refresh(self):
        """Test force refresh functionality"""
        manager = RealTimeDataManager(self.mock_api_key, self.test_symbols)
        
        # Mock the refresh function
        def mock_refresh():
            return {"test": "refreshed_data"}
        
        manager.add_subscription("test_sub", mock_refresh, RefreshInterval.FREQUENT)
        
        # Force refresh
        success = manager.force_refresh("test_sub")
        
        self.assertTrue(success)
        data = manager.get_current_data("test_sub")
        self.assertEqual(data["test"], "refreshed_data")
    
    def test_subscription_status(self):
        """Test subscription status reporting"""
        manager = RealTimeDataManager(self.mock_api_key, self.test_symbols)
        
        status = manager.get_subscription_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn("stock_quotes", status)
        self.assertIn("enabled", status["stock_quotes"])
        self.assertIn("interval_seconds", status["stock_quotes"])

class TestDesktopAppIntegration(unittest.TestCase):
    """Test desktop application integration"""
    
    def setUp(self):
        """Setup test environment"""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide window during testing
    
    def tearDown(self):
        """Cleanup test environment"""
        self.root.destroy()
    
    def test_app_initialization(self):
        """Test desktop app initialization with enhancements"""
        app = TradingSystemGUI()
        
        # Check AI assistant components
        self.assertIsNone(app.ai_assistant)  # Not initialized until API key provided
        self.assertIsNone(app.ai_conversation_manager)
        self.assertIsNone(app.data_manager)
        
        # Check GUI components
        self.assertIsNotNone(app.notebook)
        self.assertIsNotNone(app.ai_assistant_frame)
    
    def test_ai_assistant_initialization(self):
        """Test AI assistant initialization in desktop app"""
        app = TradingSystemGUI()
        
        # Mock API keys
        app.api_key = "test_fmp_key"
        app.openai_key_var = tk.StringVar(value="test_openai_key")
        
        # Test initialization (would normally require real API keys)
        try:
            app.initialize_ai_assistant()
        except Exception as e:
            # Expected to fail without real API keys
            self.assertIn("API", str(e).upper())
    
    def test_data_manager_initialization(self):
        """Test data manager initialization in desktop app"""
        app = TradingSystemGUI()
        app.api_key = "test_fmp_key"
        
        # Test initialization
        app.initialize_data_manager()
        
        # Should create data manager even with mock key
        self.assertIsNotNone(app.data_manager)

class TestPerformanceAndReliability(unittest.TestCase):
    """Test performance and reliability aspects"""
    
    def test_data_caching(self):
        """Test data caching functionality"""
        client = EnhancedFMPClient("test_key")
        
        # Test cache storage
        test_data = {"test": "data"}
        client.cache["test_key"] = test_data
        client.cache_timestamps["test_key"] = time.time()
        
        # Verify cache retrieval
        self.assertEqual(client.cache["test_key"], test_data)
    
    def test_error_recovery(self):
        """Test error recovery mechanisms"""
        manager = RealTimeDataManager("test_key", ["AAPL"])
        
        # Test subscription error handling
        subscription = manager.subscriptions["stock_quotes"]
        subscription.error_count = 2
        subscription.max_errors = 3
        
        # Simulate error
        subscription.error_count += 1
        
        # Should disable subscription after max errors
        if subscription.error_count >= subscription.max_errors:
            subscription.enabled = False
        
        self.assertFalse(subscription.enabled)
    
    def test_thread_safety(self):
        """Test thread safety of data operations"""
        manager = RealTimeDataManager("test_key", ["AAPL"])
        
        # Test concurrent data access
        def update_data():
            with manager.data_lock:
                manager.current_data["test"] = "thread_data"
        
        def read_data():
            with manager.data_lock:
                return manager.current_data.get("test")
        
        # Run concurrent operations
        update_thread = threading.Thread(target=update_data)
        update_thread.start()
        update_thread.join()
        
        result = read_data()
        self.assertEqual(result, "thread_data")
    
    def test_memory_usage(self):
        """Test memory usage and cleanup"""
        manager = RealTimeDataManager("test_key", ["AAPL"])
        
        # Add test data
        for i in range(100):
            manager.current_data[f"test_{i}"] = {"data": f"value_{i}"}
        
        # Verify data storage
        self.assertEqual(len(manager.current_data), 100)
        
        # Test cleanup
        manager.current_data.clear()
        self.assertEqual(len(manager.current_data), 0)

def run_comprehensive_tests():
    """Run all enhancement tests"""
    print("🧪 Running Comprehensive Enhancement Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestAIAssistant,
        TestEnhancedFMPAPI,
        TestRealTimeDataManager,
        TestDesktopAppIntegration,
        TestPerformanceAndReliability
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ Enhancement tests PASSED!")
    else:
        print("❌ Enhancement tests need attention")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_comprehensive_tests()
