"""
Enhanced Catalyst Analysis Demo
Demonstrates comprehensive catalyst analysis with all four components

This demo shows:
1. Strategy justification with live data
2. Bullish catalysts (fundamental, technical, event-driven)
3. Risk factors (regulatory, competitive, technical)
4. Analyst sentiment with price targets
5. Complete catalyst analysis formatting

Usage: python enhanced_catalyst_demo.py
"""

import os
import sys
from datetime import datetime

from enhanced_strategy_analyzer import create_enhanced_analyzer, AnalysisDepth
from catalyst_analysis_display import display_comprehensive_catalyst_analysis, print_catalyst_analysis
from daily_outline import resolve_fmp_key

def demonstrate_catalyst_analysis():
    """Demonstrate comprehensive catalyst analysis for multiple stocks"""
    
    print("🚀 ENHANCED CATALYST ANALYSIS DEMO")
    print("Comprehensive Strategy Justification + Catalysts + Risks + Analyst Sentiment")
    print("=" * 100)
    
    # Get API key
    api_key = resolve_fmp_key(None)
    if not api_key:
        print("❌ Error: FMP API key not found. Please set FMP_API_KEY environment variable.")
        return
    
    # Create enhanced analyzer
    try:
        analyzer = create_enhanced_analyzer(api_key)
        print("✅ Enhanced strategy analyzer with catalyst analysis initialized")
    except Exception as e:
        print(f"❌ Error initializing analyzer: {str(e)}")
        return
    
    # Target symbols for catalyst analysis
    symbols = ["AAPL", "NVDA", "AMD"]
    
    print(f"\n📈 Performing comprehensive catalyst analysis for {len(symbols)} symbols...")
    
    for i, symbol in enumerate(symbols, 1):
        try:
            print(f"\n{'='*120}")
            print(f"🔍 COMPREHENSIVE CATALYST ANALYSIS {i}/{len(symbols)}: {symbol}")
            print(f"{'='*120}")
            
            # Perform comprehensive analysis
            print(f"\n📡 Analyzing {symbol} with live market data and catalyst identification...")
            analysis = analyzer.analyze_stock_comprehensive(
                symbol=symbol,
                analysis_depth=AnalysisDepth.COMPREHENSIVE
            )
            
            # Display basic strategy info first
            print(f"\n🎯 PRIMARY STRATEGY: {analysis.primary_strategy.value.upper().replace('_', ' ')}")
            print(f"Confidence Level: {analysis.strategy_confidence.value} ({analysis.strategy_score:.1f}/100)")
            print(f"Strategy Score: {analysis.strategy_score:.1f}%")
            
            # Display comprehensive catalyst analysis
            if analysis.comprehensive_catalyst_analysis:
                print(f"\n" + display_comprehensive_catalyst_analysis(analysis))
            else:
                print(f"\n❌ Comprehensive catalyst analysis not available for {symbol}")
                
                # Show basic catalyst info as fallback
                if analysis.key_catalysts:
                    print(f"\n📋 BASIC CATALYSTS:")
                    for j, catalyst in enumerate(analysis.key_catalysts[:3], 1):
                        print(f"{j}. {catalyst.catalyst_type.upper()}: {catalyst.description}")
                        print(f"   Impact: {catalyst.impact_level} | Timeframe: {catalyst.timeframe}")
                        if catalyst.supporting_evidence:
                            print(f"   Evidence: {', '.join(catalyst.supporting_evidence[:2])}")
                        print()
            
            # Show investment thesis
            print(f"\n💡 INVESTMENT THESIS:")
            print(f"{analysis.investment_thesis}")
            
            # Show criteria summary
            print(f"\n✅ CRITERIA SUMMARY:")
            print(f"{analysis.criteria_summary}")
            
            # Show why this strategy
            print(f"\n🧠 WHY THIS STRATEGY:")
            print(f"{analysis.why_this_strategy}")
            
            # Pause between analyses
            if i < len(symbols):
                input(f"\nPress Enter to continue to next symbol...")
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {str(e)}")
            import traceback
            traceback.print_exc()
            continue
    
    print(f"\n✅ Enhanced Catalyst Analysis Demo completed successfully!")
    
    # Show catalyst analysis components summary
    print(f"\n📊 CATALYST ANALYSIS COMPONENTS:")
    print("=" * 80)
    
    print(f"\n1. 💡 STRATEGY JUSTIFICATION:")
    print("   • WHY the recommended strategy was selected")
    print("   • Specific market conditions and IV rank supporting the choice")
    print("   • Reference to Erica's methodology rules")
    print("   • Confidence level with primary reasoning")
    
    print(f"\n2. 📈 BULLISH CATALYSTS:")
    print("   • 2-3 specific positive catalysts supporting the stock")
    print("   • Fundamental drivers (earnings, product launches, market expansion)")
    print("   • Technical factors (breaking resistance, relative strength)")
    print("   • Upcoming events driving price higher")
    
    print(f"\n3. ⚠️  RISK FACTORS:")
    print("   • 2-3 key risks or headwinds facing the stock")
    print("   • Regulatory concerns, competitive threats, sector issues")
    print("   • Technical risks (resistance levels, earnings volatility)")
    print("   • Events that could create downside pressure")
    
    print(f"\n4. 📊 ANALYST SENTIMENT:")
    print("   • Current analyst consensus (Buy/Hold/Sell ratings)")
    print("   • Specific price targets with upside/downside percentages")
    print("   • Recent analyst upgrades/downgrades")
    print("   • Real analyst data when available")
    
    print(f"\n🎯 KEY BENEFITS:")
    print("• Complete context for strategy selection")
    print("• Understanding of fundamental and technical drivers")
    print("• Awareness of key risks and mitigation strategies")
    print("• Professional analyst perspective and targets")
    print("• Confidence in strategy choice based on comprehensive analysis")

def show_catalyst_examples():
    """Show examples of catalyst analysis output"""
    
    print(f"\n📋 CATALYST ANALYSIS EXAMPLES:")
    print("=" * 60)
    
    print(f"\n💡 STRATEGY JUSTIFICATION EXAMPLE:")
    print("Rich Woman's Covered Call selected because moderate IV rank of 40% provides")
    print("reasonable premium collection opportunity with post-earnings timing reducing")
    print("volatility risk. Capital efficiency advantage over owning 100 shares.")
    print("Confidence Level: MODERATE")
    
    print(f"\n📈 BULLISH CATALYST EXAMPLE:")
    print("• iPhone Cycle Strength (High Impact)")
    print("  Strong iPhone 15 Pro demand and upcoming iPhone 16 launch driving revenue growth")
    print("  Type: Fundamental | Timeframe: Next 2 quarters")
    print("  Supporting Data: iPhone revenue up 6% YoY, Pro model mix improving")
    
    print(f"\n⚠️  RISK FACTOR EXAMPLE:")
    print("• China Market Dependency (High Severity)")
    print("  Significant revenue exposure to China market amid geopolitical tensions")
    print("  Type: Regulatory | Probability: Medium")
    print("  Mitigation: Diversifying supply chain and market presence")
    
    print(f"\n📊 ANALYST SENTIMENT EXAMPLE:")
    print("Consensus: Buy | Average Target: $240.00 (****% upside)")
    print("Analyst Coverage: 35 analysts")
    print("Target Range: $200.00 - $275.00")
    print("Recent Changes:")
    print("  • Morgan Stanley upgrade to Overweight")
    print("  • Wedbush raises target to $275")

if __name__ == "__main__":
    demonstrate_catalyst_analysis()
    show_catalyst_examples()
