2025-08-18 10:42:16,592 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): financialmodelingprep.com:443
2025-08-18 10:42:16,821 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/AAPL?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,409 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/AMD?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,491 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/AMD?timeseries=60&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,547 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/NVDA?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,610 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/NVDA?timeseries=60&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,666 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/GOOGL?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,745 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/GOOGL?timeseries=60&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,802 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/AAPL?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,863 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/AAPL?timeseries=60&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,918 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/AMZN?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,980 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/AMZN?timeseries=60&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:56,981 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): financialmodelingprep.com:443
2025-08-18 10:42:57,198 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/%5EVIX?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,264 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/%5EVIX?timeseries=252&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,321 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/SPY?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,377 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/QQQ?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,432 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/IWM?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,493 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/options/SPY?apikey=******************************** HTTP/1.1" 200 2
2025-08-18 10:42:57,552 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/options/QQQ?apikey=******************************** HTTP/1.1" 200 2
2025-08-18 10:42:57,613 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/options/IWM?apikey=******************************** HTTP/1.1" 200 2
2025-08-18 10:42:57,669 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLK?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,730 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLK?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,786 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/SPY?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,842 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLV?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,902 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLV?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:57,958 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLF?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,018 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLF?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,077 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLE?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,139 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLE?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,202 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLY?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,263 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLY?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,319 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLP?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:58,379 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLP?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,408 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLI?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,470 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLI?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,526 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLB?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,587 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLB?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,645 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLU?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,705 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLU?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,779 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLRE?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,840 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLRE?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:42:59,897 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/quote/XLC?apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:00,012 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/historical-price-full/XLC?timeseries=30&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:00,133 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/economic_calendar?from=2025-08-18&to=2025-08-25&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:00,206 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/stock_news?tickers=AMD&limit=50&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:01,565 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/earning_calendar?symbol=AMD&from=2025-08-18&to=2025-11-16&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:03,152 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/options/AMD?apikey=******************************** HTTP/1.1" 200 2
2025-08-18 10:43:03,407 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/earning_calendar?symbol=NVDA&from=2025-08-18&to=2025-11-16&apikey=******************************** HTTP/1.1" 200 None
2025-08-18 10:43:03,600 - urllib3.connectionpool - DEBUG - https://financialmodelingprep.com:443 "GET /api/v3/options/NVDA?apikey=******************************** HTTP/1.1" 200 2
