"""
Time-Sequenced Trading Itinerary Generator
Creates detailed daily trading itineraries with specific execution instructions

This module generates comprehensive trading itineraries that provide:
1. Time-sequenced action plans for each stock
2. Specific entry/exit times based on market conditions
3. Strike prices and expiration dates for options strategies
4. Position sizes calculated for risk management
5. Step-by-step execution instructions
6. Priority rankings and timing considerations

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta, time
from enum import Enum
import logging

from intelligent_strategy_engine import StrategyOfTheDay, MarketEnvironmentReport
from market_analysis_engine import MarketFactors, StockSpecificFactors
from daily_outline import StrategyType
from risk_management import RiskManager, PositionRisk

class ExecutionPriority(Enum):
    IMMEDIATE = "immediate"
    MARKET_OPEN = "market_open"
    MORNING = "morning"
    MIDDAY = "midday"
    AFTERNOON = "afternoon"
    MARKET_CLOSE = "market_close"
    AFTER_HOURS = "after_hours"

class ExecutionTiming(Enum):
    PRE_MARKET = "pre_market"  # 4:00-9:30 AM ET
    MARKET_OPEN = "market_open"  # 9:30-10:00 AM ET
    MORNING_SESSION = "morning_session"  # 10:00-11:30 AM ET
    MIDDAY_SESSION = "midday_session"  # 11:30 AM-2:00 PM ET
    AFTERNOON_SESSION = "afternoon_session"  # 2:00-3:30 PM ET
    MARKET_CLOSE = "market_close"  # 3:30-4:00 PM ET
    AFTER_HOURS = "after_hours"  # 4:00-8:00 PM ET

@dataclass
class TradingAction:
    """Individual trading action with specific execution details"""
    action_id: str
    symbol: str
    action_type: str  # BUY, SELL, CLOSE, ADJUST
    strategy: StrategyType
    
    # Timing
    priority: ExecutionPriority
    execution_window: ExecutionTiming
    target_time: Optional[time]
    deadline: Optional[time]
    
    # Trade details
    instrument: str  # STOCK, CALL, PUT, SPREAD
    strike_price: Optional[float]
    expiration_date: Optional[datetime]
    quantity: int
    position_size_pct: float
    
    # Execution instructions
    order_type: str  # MARKET, LIMIT, STOP
    limit_price: Optional[float]
    stop_price: Optional[float]
    
    # Context
    reasoning: str
    prerequisites: List[str] = field(default_factory=list)
    risk_considerations: List[str] = field(default_factory=list)
    success_criteria: List[str] = field(default_factory=list)
    
    # Monitoring
    conditions_to_watch: List[str] = field(default_factory=list)
    exit_triggers: List[str] = field(default_factory=list)

@dataclass
class TradingItinerary:
    """Complete daily trading itinerary for a symbol"""
    symbol: str
    date: datetime
    strategy: StrategyOfTheDay
    
    # Actions organized by timing
    pre_market_actions: List[TradingAction] = field(default_factory=list)
    market_open_actions: List[TradingAction] = field(default_factory=list)
    morning_actions: List[TradingAction] = field(default_factory=list)
    midday_actions: List[TradingAction] = field(default_factory=list)
    afternoon_actions: List[TradingAction] = field(default_factory=list)
    market_close_actions: List[TradingAction] = field(default_factory=list)
    after_hours_actions: List[TradingAction] = field(default_factory=list)
    
    # Summary
    total_actions: int = 0
    estimated_duration: timedelta = field(default_factory=lambda: timedelta(hours=6.5))
    risk_level: str = "MODERATE"
    complexity_score: float = 0.5
    
    # Execution notes
    preparation_checklist: List[str] = field(default_factory=list)
    key_risks: List[str] = field(default_factory=list)
    success_metrics: List[str] = field(default_factory=list)

@dataclass
class DailyTradingPlan:
    """Complete daily trading plan for all symbols"""
    date: datetime
    market_environment: MarketEnvironmentReport
    
    # Individual itineraries
    symbol_itineraries: Dict[str, TradingItinerary] = field(default_factory=dict)
    
    # Daily schedule
    daily_schedule: List[Tuple[time, str, str]] = field(default_factory=list)  # (time, symbol, action)
    
    # Summary metrics
    total_trades: int = 0
    total_risk_exposure: float = 0.0
    portfolio_complexity: float = 0.0
    
    # Daily briefing
    morning_briefing: str = ""
    key_focus_areas: List[str] = field(default_factory=list)
    risk_warnings: List[str] = field(default_factory=list)

class TradingItineraryGenerator:
    """Generates detailed time-sequenced trading itineraries"""
    
    def __init__(self, risk_manager: RiskManager = None):
        self.risk_manager = risk_manager or RiskManager()
        self.logger = logging.getLogger(__name__)
        
        # Market timing preferences
        self.market_open_time = time(9, 30)  # 9:30 AM ET
        self.market_close_time = time(16, 0)  # 4:00 PM ET
        
        # Strategy timing preferences
        self.strategy_timing_preferences = {
            StrategyType.COVERED_CALL: ExecutionTiming.MORNING_SESSION,
            StrategyType.CREDIT_SPREAD: ExecutionTiming.MIDDAY_SESSION,
            StrategyType.LEAPS: ExecutionTiming.MARKET_OPEN,
            StrategyType.PREMIUM_SELLING: ExecutionTiming.AFTERNOON_SESSION
        }
    
    def generate_daily_plan(self, strategies: List[StrategyOfTheDay], 
                          market_environment: MarketEnvironmentReport,
                          account_size: float = 100000) -> DailyTradingPlan:
        """
        Generate complete daily trading plan with time-sequenced itineraries
        
        Args:
            strategies: List of strategy recommendations for each symbol
            market_environment: Current market environment analysis
            account_size: Total account size for position sizing
            
        Returns:
            Complete daily trading plan with detailed itineraries
        """
        
        plan_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Create daily plan
        daily_plan = DailyTradingPlan(
            date=plan_date,
            market_environment=market_environment
        )
        
        # Generate itinerary for each strategy
        for strategy in strategies:
            itinerary = self.generate_symbol_itinerary(strategy, market_environment, account_size)
            daily_plan.symbol_itineraries[strategy.symbol] = itinerary
        
        # Create consolidated daily schedule
        daily_plan.daily_schedule = self._create_daily_schedule(daily_plan.symbol_itineraries)
        
        # Calculate summary metrics
        daily_plan.total_trades = sum(len(self._get_all_actions(itinerary)) 
                                    for itinerary in daily_plan.symbol_itineraries.values())
        
        # Generate morning briefing
        daily_plan.morning_briefing = self._generate_morning_briefing(daily_plan)
        daily_plan.key_focus_areas = self._identify_key_focus_areas(daily_plan)
        daily_plan.risk_warnings = self._generate_risk_warnings(daily_plan)
        
        return daily_plan
    
    def generate_symbol_itinerary(self, strategy: StrategyOfTheDay, 
                                market_environment: MarketEnvironmentReport,
                                account_size: float) -> TradingItinerary:
        """
        Generate detailed trading itinerary for a single symbol
        
        Args:
            strategy: Strategy recommendation for the symbol
            market_environment: Current market environment
            account_size: Account size for position sizing
            
        Returns:
            Detailed trading itinerary with time-sequenced actions
        """
        
        itinerary = TradingItinerary(
            symbol=strategy.symbol,
            date=strategy.date,
            strategy=strategy
        )
        
        # Generate actions based on strategy type
        if strategy.recommended_strategy == StrategyType.COVERED_CALL:
            self._generate_covered_call_itinerary(itinerary, strategy, account_size)
        elif strategy.recommended_strategy == StrategyType.CREDIT_SPREAD:
            self._generate_credit_spread_itinerary(itinerary, strategy, account_size)
        elif strategy.recommended_strategy == StrategyType.LEAPS:
            self._generate_leaps_itinerary(itinerary, strategy, account_size)
        elif strategy.recommended_strategy == StrategyType.PREMIUM_SELLING:
            self._generate_premium_selling_itinerary(itinerary, strategy, account_size)
        
        # Add preparation checklist
        itinerary.preparation_checklist = self._generate_preparation_checklist(strategy)
        
        # Calculate complexity and risk
        itinerary.complexity_score = self._calculate_complexity_score(itinerary)
        itinerary.risk_level = self._determine_risk_level(itinerary, strategy)
        
        # Count total actions
        itinerary.total_actions = len(self._get_all_actions(itinerary))
        
        return itinerary

    def _generate_covered_call_itinerary(self, itinerary: TradingItinerary,
                                       strategy: StrategyOfTheDay, account_size: float):
        """Generate covered call specific itinerary"""

        # Pre-market: Review positions and market conditions
        itinerary.pre_market_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_CC_REVIEW",
            symbol=strategy.symbol,
            action_type="REVIEW",
            strategy=StrategyType.COVERED_CALL,
            priority=ExecutionPriority.IMMEDIATE,
            execution_window=ExecutionTiming.PRE_MARKET,
            target_time=time(8, 30),
            deadline=time(9, 15),
            instrument="ANALYSIS",
            quantity=0,
            position_size_pct=0.0,
            order_type="N/A",
            reasoning="Review overnight news, pre-market activity, and IV levels",
            prerequisites=["Check earnings calendar", "Review overnight news", "Verify IV rank"],
            conditions_to_watch=["Pre-market volume", "Gap up/down", "IV changes"]
        ))

        # Morning: Execute covered call if conditions are met
        position_size = self._calculate_position_size(strategy, account_size, 0.02)  # 2% risk
        strike_price = self._calculate_covered_call_strike(strategy)
        expiration = self._get_optimal_expiration(strategy, 30, 45)  # 30-45 DTE

        itinerary.morning_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_CC_SELL",
            symbol=strategy.symbol,
            action_type="SELL",
            strategy=StrategyType.COVERED_CALL,
            priority=ExecutionPriority.MORNING,
            execution_window=ExecutionTiming.MORNING_SESSION,
            target_time=time(10, 30),
            deadline=time(11, 30),
            instrument="CALL",
            strike_price=strike_price,
            expiration_date=expiration,
            quantity=position_size // 100,  # Options contracts
            position_size_pct=2.0,
            order_type="LIMIT",
            limit_price=self._calculate_target_premium(strategy, strike_price),
            reasoning=f"Sell {strategy.symbol} covered calls at optimal strike for premium collection",
            prerequisites=["Own underlying shares", "Verify IV rank > 50%", "Check earnings calendar"],
            risk_considerations=["Assignment risk", "Upside limitation", "IV crush risk"],
            success_criteria=["Premium > $0.30 per share", "Delta ~0.30", "Good liquidity"],
            conditions_to_watch=["IV rank changes", "Technical breakout", "News events"],
            exit_triggers=["50% profit target", "21 DTE management", "Technical breakdown"]
        ))

        # Afternoon: Monitor and adjust if needed
        itinerary.afternoon_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_CC_MONITOR",
            symbol=strategy.symbol,
            action_type="MONITOR",
            strategy=StrategyType.COVERED_CALL,
            priority=ExecutionPriority.AFTERNOON,
            execution_window=ExecutionTiming.AFTERNOON_SESSION,
            target_time=time(14, 30),
            deadline=time(15, 30),
            instrument="CALL",
            quantity=0,
            position_size_pct=0.0,
            order_type="N/A",
            reasoning="Monitor position for profit-taking or adjustment opportunities",
            conditions_to_watch=["Option price", "Delta changes", "Time decay", "Underlying movement"],
            exit_triggers=["50% profit achieved", "Technical breakdown", "Assignment risk high"]
        ))

    def _generate_credit_spread_itinerary(self, itinerary: TradingItinerary,
                                        strategy: StrategyOfTheDay, account_size: float):
        """Generate credit spread specific itinerary"""

        # Market open: Check conditions
        itinerary.market_open_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_CS_CHECK",
            symbol=strategy.symbol,
            action_type="REVIEW",
            strategy=StrategyType.CREDIT_SPREAD,
            priority=ExecutionPriority.MARKET_OPEN,
            execution_window=ExecutionTiming.MARKET_OPEN,
            target_time=time(9, 45),
            deadline=time(10, 15),
            instrument="ANALYSIS",
            quantity=0,
            position_size_pct=0.0,
            order_type="N/A",
            reasoning="Verify bullish bias and market conditions for credit spreads",
            prerequisites=["Confirm bullish sentiment", "Check support levels", "Verify IV rank"],
            conditions_to_watch=["Market direction", "VIX level", "Sector performance"]
        ))

        # Midday: Execute credit spread
        position_size = self._calculate_position_size(strategy, account_size, 0.015)  # 1.5% risk
        short_strike = self._calculate_credit_spread_strikes(strategy)[0]
        long_strike = self._calculate_credit_spread_strikes(strategy)[1]
        expiration = self._get_optimal_expiration(strategy, 30, 45)

        itinerary.midday_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_CS_EXECUTE",
            symbol=strategy.symbol,
            action_type="SELL",
            strategy=StrategyType.CREDIT_SPREAD,
            priority=ExecutionPriority.MIDDAY,
            execution_window=ExecutionTiming.MIDDAY_SESSION,
            target_time=time(12, 30),
            deadline=time(13, 30),
            instrument="SPREAD",
            strike_price=short_strike,
            expiration_date=expiration,
            quantity=position_size // 100,
            position_size_pct=1.5,
            order_type="LIMIT",
            limit_price=self._calculate_spread_credit(strategy, short_strike, long_strike),
            reasoning=f"Execute {strategy.symbol} put credit spread for income generation",
            prerequisites=["Bullish bias confirmed", "IV rank > 60%", "Technical support identified"],
            risk_considerations=["Assignment risk", "Downside movement", "Volatility expansion"],
            success_criteria=["Credit > 1/3 of spread width", "Good liquidity", "Delta ~0.20"],
            conditions_to_watch=["Underlying price", "Support levels", "Market sentiment"],
            exit_triggers=["50% profit target", "Support break", "21 DTE management"]
        ))

    def _generate_leaps_itinerary(self, itinerary: TradingItinerary,
                                strategy: StrategyOfTheDay, account_size: float):
        """Generate LEAPS specific itinerary"""

        # Market open: Execute LEAPS entry
        position_size = self._calculate_position_size(strategy, account_size, 0.03)  # 3% risk
        strike_price = self._calculate_leaps_strike(strategy)
        expiration = self._get_leaps_expiration(strategy)

        itinerary.market_open_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_LEAPS_BUY",
            symbol=strategy.symbol,
            action_type="BUY",
            strategy=StrategyType.LEAPS,
            priority=ExecutionPriority.MARKET_OPEN,
            execution_window=ExecutionTiming.MARKET_OPEN,
            target_time=time(9, 45),
            deadline=time(10, 30),
            instrument="CALL",
            strike_price=strike_price,
            expiration_date=expiration,
            quantity=position_size // (strike_price * 100),  # Calculate contracts
            position_size_pct=3.0,
            order_type="LIMIT",
            limit_price=self._calculate_leaps_target_price(strategy, strike_price),
            reasoning=f"Purchase {strategy.symbol} LEAPS for long-term leveraged exposure",
            prerequisites=["Strong technical setup", "Low IV environment", "Bullish thesis"],
            risk_considerations=["Time decay", "Volatility risk", "Trend reversal"],
            success_criteria=["Delta > 0.70", "Good liquidity", "Reasonable time value"],
            conditions_to_watch=["Technical levels", "Trend strength", "IV changes"],
            exit_triggers=["Technical breakdown", "Thesis invalidation", "Profit target hit"]
        ))

    def _generate_premium_selling_itinerary(self, itinerary: TradingItinerary,
                                          strategy: StrategyOfTheDay, account_size: float):
        """Generate premium selling specific itinerary"""

        # Afternoon: Execute premium selling strategy
        position_size = self._calculate_position_size(strategy, account_size, 0.02)  # 2% risk

        itinerary.afternoon_actions.append(TradingAction(
            action_id=f"{strategy.symbol}_PS_EXECUTE",
            symbol=strategy.symbol,
            action_type="SELL",
            strategy=StrategyType.PREMIUM_SELLING,
            priority=ExecutionPriority.AFTERNOON,
            execution_window=ExecutionTiming.AFTERNOON_SESSION,
            target_time=time(14, 0),
            deadline=time(15, 0),
            instrument="PUT",
            strike_price=self._calculate_premium_selling_strike(strategy),
            expiration_date=self._get_optimal_expiration(strategy, 15, 30),
            quantity=position_size // 100,
            position_size_pct=2.0,
            order_type="LIMIT",
            limit_price=self._calculate_target_premium(strategy, self._calculate_premium_selling_strike(strategy)),
            reasoning=f"Sell {strategy.symbol} puts systematically for premium collection",
            prerequisites=["High IV rank", "Systematic approach", "Risk management in place"],
            risk_considerations=["Assignment risk", "Volatility collapse", "Market crash"],
            success_criteria=["Premium > 1% of strike", "Good liquidity", "Appropriate delta"],
            conditions_to_watch=["IV rank", "Support levels", "Market volatility"],
            exit_triggers=["50% profit", "Support break", "IV rank collapse"]
        ))

    # Helper methods for calculations

    def _calculate_position_size(self, strategy: StrategyOfTheDay, account_size: float, risk_pct: float) -> int:
        """Calculate appropriate position size based on risk management"""
        risk_amount = account_size * risk_pct
        # Simplified position sizing - in practice would use more sophisticated methods
        return int(risk_amount / 100) * 100  # Round to nearest 100 shares

    def _calculate_covered_call_strike(self, strategy: StrategyOfTheDay) -> float:
        """Calculate optimal covered call strike price"""
        # Simplified calculation - would use current stock price + technical analysis
        return 150.0  # Placeholder - would calculate based on current price and delta target

    def _calculate_credit_spread_strikes(self, strategy: StrategyOfTheDay) -> Tuple[float, float]:
        """Calculate credit spread strike prices (short, long)"""
        # Simplified calculation
        short_strike = 145.0  # Would calculate based on delta target (~0.20)
        long_strike = short_strike - 5.0  # 5-point spread
        return short_strike, long_strike

    def _calculate_leaps_strike(self, strategy: StrategyOfTheDay) -> float:
        """Calculate LEAPS strike price for high delta"""
        # Target ~0.75 delta for LEAPS
        return 120.0  # Would calculate based on current price and delta target

    def _calculate_premium_selling_strike(self, strategy: StrategyOfTheDay) -> float:
        """Calculate premium selling strike price"""
        # Target ~0.15-0.20 delta
        return 140.0  # Would calculate based on support levels and delta

    def _get_optimal_expiration(self, strategy: StrategyOfTheDay, min_dte: int, max_dte: int) -> datetime:
        """Get optimal expiration date within DTE range"""
        target_dte = (min_dte + max_dte) // 2
        return datetime.now() + timedelta(days=target_dte)

    def _get_leaps_expiration(self, strategy: StrategyOfTheDay) -> datetime:
        """Get LEAPS expiration (12+ months)"""
        return datetime.now() + timedelta(days=365)  # 1 year out

    def _calculate_target_premium(self, strategy: StrategyOfTheDay, strike_price: float) -> float:
        """Calculate target premium for option sale"""
        # Simplified calculation - would use option pricing models
        return 3.50  # Placeholder premium

    def _calculate_spread_credit(self, strategy: StrategyOfTheDay, short_strike: float, long_strike: float) -> float:
        """Calculate target credit for spread"""
        spread_width = short_strike - long_strike
        return spread_width * 0.33  # Target 1/3 of spread width

    def _calculate_leaps_target_price(self, strategy: StrategyOfTheDay, strike_price: float) -> float:
        """Calculate target price for LEAPS purchase"""
        # Would use option pricing models
        return 25.00  # Placeholder

    def _generate_preparation_checklist(self, strategy: StrategyOfTheDay) -> List[str]:
        """Generate preparation checklist for the strategy"""
        base_checklist = [
            "Review overnight news and pre-market activity",
            "Check earnings calendar for the week",
            "Verify account buying power and margin",
            "Review current positions and risk exposure",
            "Check market volatility (VIX) levels"
        ]

        strategy_specific = {
            StrategyType.COVERED_CALL: [
                "Verify ownership of underlying shares",
                "Check IV rank and percentile",
                "Review technical resistance levels"
            ],
            StrategyType.CREDIT_SPREAD: [
                "Confirm bullish market bias",
                "Identify key support levels",
                "Check sector performance"
            ],
            StrategyType.LEAPS: [
                "Verify strong technical setup",
                "Confirm low IV environment",
                "Review long-term thesis"
            ],
            StrategyType.PREMIUM_SELLING: [
                "Verify high IV rank environment",
                "Check systematic approach criteria",
                "Review assignment comfort level"
            ]
        }

        return base_checklist + strategy_specific.get(strategy.recommended_strategy, [])

    def _calculate_complexity_score(self, itinerary: TradingItinerary) -> float:
        """Calculate complexity score for the itinerary"""
        total_actions = len(self._get_all_actions(itinerary))

        # Base complexity on number of actions and strategy type
        base_score = min(total_actions / 10.0, 1.0)  # Normalize to 0-1

        # Adjust for strategy complexity
        strategy_complexity = {
            StrategyType.COVERED_CALL: 0.3,
            StrategyType.CREDIT_SPREAD: 0.6,
            StrategyType.LEAPS: 0.4,
            StrategyType.PREMIUM_SELLING: 0.5
        }

        complexity_adj = strategy_complexity.get(itinerary.strategy.recommended_strategy, 0.5)
        return (base_score + complexity_adj) / 2

    def _determine_risk_level(self, itinerary: TradingItinerary, strategy: StrategyOfTheDay) -> str:
        """Determine risk level for the itinerary"""
        if strategy.confidence >= 0.8:
            return "LOW"
        elif strategy.confidence >= 0.6:
            return "MODERATE"
        elif strategy.confidence >= 0.4:
            return "HIGH"
        else:
            return "VERY_HIGH"

    def _get_all_actions(self, itinerary: TradingItinerary) -> List[TradingAction]:
        """Get all actions from an itinerary"""
        all_actions = []
        all_actions.extend(itinerary.pre_market_actions)
        all_actions.extend(itinerary.market_open_actions)
        all_actions.extend(itinerary.morning_actions)
        all_actions.extend(itinerary.midday_actions)
        all_actions.extend(itinerary.afternoon_actions)
        all_actions.extend(itinerary.market_close_actions)
        all_actions.extend(itinerary.after_hours_actions)
        return all_actions

    def _create_daily_schedule(self, itineraries: Dict[str, TradingItinerary]) -> List[Tuple[time, str, str]]:
        """Create consolidated daily schedule from all itineraries"""
        schedule = []

        for symbol, itinerary in itineraries.items():
            for action in self._get_all_actions(itinerary):
                if action.target_time:
                    schedule.append((action.target_time, symbol, action.action_type))

        # Sort by time
        schedule.sort(key=lambda x: x[0])
        return schedule

    def _generate_morning_briefing(self, plan: DailyTradingPlan) -> str:
        """Generate morning briefing for the trading day"""
        briefing = f"Daily Trading Plan - {plan.date.strftime('%B %d, %Y')}\n\n"
        briefing += f"Market Environment: {plan.market_environment.overall_assessment}\n"
        briefing += f"Total Planned Trades: {plan.total_trades}\n"
        briefing += f"Active Symbols: {', '.join(plan.symbol_itineraries.keys())}\n\n"

        briefing += "Key Actions Today:\n"
        for time_slot, symbol, action in plan.daily_schedule[:5]:  # Top 5 actions
            briefing += f"• {time_slot.strftime('%H:%M')} - {symbol}: {action}\n"

        return briefing

    def _identify_key_focus_areas(self, plan: DailyTradingPlan) -> List[str]:
        """Identify key focus areas for the day"""
        focus_areas = [
            "Monitor VIX levels and volatility changes",
            "Watch for earnings announcements",
            "Track sector rotation patterns",
            "Observe options flow and unusual activity"
        ]

        # Add strategy-specific focus areas
        strategies = [itinerary.strategy.recommended_strategy for itinerary in plan.symbol_itineraries.values()]

        if StrategyType.COVERED_CALL in strategies:
            focus_areas.append("Monitor IV rank for covered call opportunities")
        if StrategyType.CREDIT_SPREAD in strategies:
            focus_areas.append("Watch support levels for credit spread execution")
        if StrategyType.LEAPS in strategies:
            focus_areas.append("Track long-term technical setups")

        return focus_areas

    def _generate_risk_warnings(self, plan: DailyTradingPlan) -> List[str]:
        """Generate risk warnings for the day"""
        warnings = []

        # Check for high-risk conditions
        if plan.total_trades > 10:
            warnings.append("High number of planned trades - ensure adequate attention to each")

        # Add market-specific warnings
        if hasattr(plan.market_environment, 'vix_level') and plan.market_environment.vix_level > 25:
            warnings.append("Elevated VIX - expect increased volatility and adjust position sizes")

        # Strategy-specific warnings
        for itinerary in plan.symbol_itineraries.values():
            if itinerary.risk_level in ["HIGH", "VERY_HIGH"]:
                warnings.append(f"{itinerary.symbol} strategy has elevated risk - monitor closely")

        return warnings
