"""
News and Sentiment Analysis Module
Comprehensive news sentiment scoring and social media analysis for stock-specific insights

This module provides:
- Real-time news sentiment analysis for each stock
- Earnings calendar integration with move estimates
- Social media sentiment tracking
- Analyst upgrade/downgrade monitoring
- Event impact assessment
- Sentiment trend analysis

Features:
- Multi-source news aggregation
- Natural language processing for sentiment scoring
- Earnings surprise probability estimation
- Social sentiment momentum tracking
- News impact quantification
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import re
import json

class SentimentLevel(Enum):
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"

class NewsCategory(Enum):
    EARNINGS = "earnings"
    PRODUCT_LAUNCH = "product_launch"
    REGULATORY = "regulatory"
    ANALYST_RATING = "analyst_rating"
    PARTNERSHIP = "partnership"
    FINANCIAL_RESULTS = "financial_results"
    MANAGEMENT_CHANGE = "management_change"
    MARKET_GENERAL = "market_general"

@dataclass
class NewsItem:
    """Individual news item with sentiment analysis"""
    title: str
    content: str
    source: str
    published_date: datetime
    category: NewsCategory
    sentiment_score: float  # -1 to 1
    sentiment_level: SentimentLevel
    relevance_score: float  # 0 to 1
    impact_estimate: str  # low/medium/high
    keywords: List[str]
    url: Optional[str] = None

@dataclass
class EarningsEvent:
    """Earnings event with analysis"""
    symbol: str
    earnings_date: datetime
    days_until_earnings: int
    estimated_move: float  # Expected percentage move
    consensus_eps: Optional[float]
    consensus_revenue: Optional[float]
    surprise_probability: float  # 0 to 1
    iv_rank_pre_earnings: float
    historical_moves: List[float]  # Past earnings moves
    analyst_sentiment: str  # bullish/bearish/neutral

@dataclass
class SocialSentiment:
    """Social media sentiment analysis"""
    platform: str  # twitter/reddit/stocktwits
    mention_count: int
    sentiment_score: float  # -1 to 1
    sentiment_trend: str  # improving/declining/stable
    key_themes: List[str]
    influencer_sentiment: float  # Sentiment from key influencers
    volume_trend: str  # increasing/decreasing/stable

@dataclass
class AnalystActivity:
    """Analyst rating changes and activity"""
    firm: str
    analyst_name: str
    action: str  # upgrade/downgrade/initiate/maintain
    old_rating: Optional[str]
    new_rating: str
    price_target: Optional[float]
    date: datetime
    reasoning: str
    impact_score: float  # 0 to 1

@dataclass
class StockSentimentAnalysis:
    """Complete sentiment analysis for a stock"""
    symbol: str
    analysis_date: datetime
    
    # Overall sentiment
    overall_sentiment_score: float  # -1 to 1
    overall_sentiment_level: SentimentLevel
    sentiment_confidence: float  # 0 to 1
    
    # News analysis
    recent_news: List[NewsItem]
    news_sentiment_trend: str  # improving/declining/stable
    key_news_themes: List[str]
    
    # Earnings analysis
    next_earnings: Optional[EarningsEvent]
    earnings_sentiment: str  # bullish/bearish/neutral
    
    # Social sentiment
    social_sentiment: List[SocialSentiment]
    social_momentum: str  # building/fading/stable
    
    # Analyst activity
    recent_analyst_activity: List[AnalystActivity]
    analyst_consensus: str  # bullish/bearish/neutral
    
    # Impact assessment
    sentiment_impact_on_price: str  # positive/negative/neutral
    volatility_impact: str  # increasing/decreasing/stable
    
    # Trading implications
    strategy_implications: List[str]
    risk_factors: List[str]

class NewsSentimentAnalyzer:
    """Comprehensive news and sentiment analysis system"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.sentiment_keywords = self._load_sentiment_keywords()
        self.earnings_calendar = {}
        
    def analyze_stock_sentiment(self, symbol: str, lookback_days: int = 7) -> StockSentimentAnalysis:
        """
        Perform comprehensive sentiment analysis for a stock
        
        Args:
            symbol: Stock symbol to analyze
            lookback_days: Number of days to look back for news/sentiment
            
        Returns:
            Complete sentiment analysis with trading implications
        """
        
        # Gather news data
        recent_news = self._fetch_recent_news(symbol, lookback_days)
        
        # Analyze news sentiment
        news_sentiment_score, news_trend = self._analyze_news_sentiment(recent_news)
        
        # Get earnings information
        earnings_event = self._get_earnings_information(symbol)
        
        # Analyze social sentiment
        social_sentiment = self._analyze_social_sentiment(symbol, lookback_days)
        
        # Get analyst activity
        analyst_activity = self._get_analyst_activity(symbol, lookback_days)
        
        # Calculate overall sentiment
        overall_sentiment = self._calculate_overall_sentiment(
            news_sentiment_score, social_sentiment, analyst_activity
        )
        
        # Assess impact and implications
        price_impact, volatility_impact = self._assess_market_impact(
            overall_sentiment, recent_news, earnings_event
        )
        
        # Generate strategy implications
        strategy_implications = self._generate_strategy_implications(
            symbol, overall_sentiment, earnings_event, social_sentiment
        )
        
        # Identify risk factors
        risk_factors = self._identify_sentiment_risks(
            recent_news, earnings_event, social_sentiment
        )
        
        return StockSentimentAnalysis(
            symbol=symbol,
            analysis_date=datetime.now(),
            overall_sentiment_score=overall_sentiment,
            overall_sentiment_level=self._classify_sentiment_level(overall_sentiment),
            sentiment_confidence=self._calculate_sentiment_confidence(recent_news, social_sentiment),
            recent_news=recent_news,
            news_sentiment_trend=news_trend,
            key_news_themes=self._extract_key_themes(recent_news),
            next_earnings=earnings_event,
            earnings_sentiment=self._assess_earnings_sentiment(earnings_event),
            social_sentiment=social_sentiment,
            social_momentum=self._assess_social_momentum(social_sentiment),
            recent_analyst_activity=analyst_activity,
            analyst_consensus=self._determine_analyst_consensus(analyst_activity),
            sentiment_impact_on_price=price_impact,
            volatility_impact=volatility_impact,
            strategy_implications=strategy_implications,
            risk_factors=risk_factors
        )
    
    def _fetch_recent_news(self, symbol: str, lookback_days: int) -> List[NewsItem]:
        """Fetch recent news for the symbol"""
        
        # In production, this would fetch from news APIs
        # For demonstration, return sample news items
        
        sample_news = [
            NewsItem(
                title=f"{symbol} Reports Strong Q4 Earnings Beat",
                content=f"{symbol} exceeded analyst expectations with strong revenue growth...",
                source="Reuters",
                published_date=datetime.now() - timedelta(days=1),
                category=NewsCategory.EARNINGS,
                sentiment_score=0.7,
                sentiment_level=SentimentLevel.POSITIVE,
                relevance_score=0.9,
                impact_estimate="high",
                keywords=["earnings", "beat", "revenue", "growth"]
            ),
            NewsItem(
                title=f"Analyst Upgrades {symbol} on Innovation Pipeline",
                content=f"Goldman Sachs upgrades {symbol} citing strong product pipeline...",
                source="Bloomberg",
                published_date=datetime.now() - timedelta(days=2),
                category=NewsCategory.ANALYST_RATING,
                sentiment_score=0.5,
                sentiment_level=SentimentLevel.POSITIVE,
                relevance_score=0.8,
                impact_estimate="medium",
                keywords=["upgrade", "analyst", "innovation", "pipeline"]
            ),
            NewsItem(
                title=f"Regulatory Concerns Impact {symbol} Outlook",
                content=f"New regulations may affect {symbol}'s business model...",
                source="Wall Street Journal",
                published_date=datetime.now() - timedelta(days=3),
                category=NewsCategory.REGULATORY,
                sentiment_score=-0.3,
                sentiment_level=SentimentLevel.NEGATIVE,
                relevance_score=0.7,
                impact_estimate="medium",
                keywords=["regulatory", "concerns", "impact", "business"]
            )
        ]
        
        return sample_news
    
    def _analyze_news_sentiment(self, news_items: List[NewsItem]) -> Tuple[float, str]:
        """Analyze overall news sentiment and trend"""
        
        if not news_items:
            return 0.0, "stable"
        
        # Calculate weighted sentiment score
        total_score = 0.0
        total_weight = 0.0
        
        for item in news_items:
            weight = item.relevance_score * self._get_recency_weight(item.published_date)
            total_score += item.sentiment_score * weight
            total_weight += weight
        
        overall_sentiment = total_score / total_weight if total_weight > 0 else 0.0
        
        # Determine trend
        if len(news_items) >= 3:
            recent_sentiment = sum(item.sentiment_score for item in news_items[:2]) / 2
            older_sentiment = sum(item.sentiment_score for item in news_items[2:]) / len(news_items[2:])
            
            if recent_sentiment > older_sentiment + 0.2:
                trend = "improving"
            elif recent_sentiment < older_sentiment - 0.2:
                trend = "declining"
            else:
                trend = "stable"
        else:
            trend = "stable"
        
        return overall_sentiment, trend
    
    def _get_earnings_information(self, symbol: str) -> Optional[EarningsEvent]:
        """Get earnings information and analysis"""
        
        # In production, would fetch from earnings calendar API
        # Return sample earnings event
        
        earnings_date = datetime.now() + timedelta(days=12)
        days_until = 12
        
        return EarningsEvent(
            symbol=symbol,
            earnings_date=earnings_date,
            days_until_earnings=days_until,
            estimated_move=0.08,  # 8% expected move
            consensus_eps=2.45,
            consensus_revenue=85.2e9,  # $85.2B
            surprise_probability=0.65,  # 65% chance of surprise
            iv_rank_pre_earnings=0.75,
            historical_moves=[0.06, 0.12, 0.04, 0.09, 0.07],  # Past 5 earnings moves
            analyst_sentiment="bullish"
        )
    
    def _analyze_social_sentiment(self, symbol: str, lookback_days: int) -> List[SocialSentiment]:
        """Analyze social media sentiment"""
        
        # Sample social sentiment data
        return [
            SocialSentiment(
                platform="twitter",
                mention_count=1250,
                sentiment_score=0.3,
                sentiment_trend="improving",
                key_themes=["earnings", "growth", "innovation"],
                influencer_sentiment=0.4,
                volume_trend="increasing"
            ),
            SocialSentiment(
                platform="reddit",
                mention_count=890,
                sentiment_score=0.1,
                sentiment_trend="stable",
                key_themes=["valuation", "competition", "market"],
                influencer_sentiment=0.2,
                volume_trend="stable"
            ),
            SocialSentiment(
                platform="stocktwits",
                mention_count=2100,
                sentiment_score=0.5,
                sentiment_trend="improving",
                key_themes=["bullish", "breakout", "momentum"],
                influencer_sentiment=0.6,
                volume_trend="increasing"
            )
        ]
    
    def _get_analyst_activity(self, symbol: str, lookback_days: int) -> List[AnalystActivity]:
        """Get recent analyst activity"""
        
        return [
            AnalystActivity(
                firm="Goldman Sachs",
                analyst_name="John Smith",
                action="upgrade",
                old_rating="Hold",
                new_rating="Buy",
                price_target=180.0,
                date=datetime.now() - timedelta(days=2),
                reasoning="Strong product pipeline and market expansion",
                impact_score=0.8
            ),
            AnalystActivity(
                firm="Morgan Stanley",
                analyst_name="Jane Doe",
                action="maintain",
                old_rating="Buy",
                new_rating="Buy",
                price_target=175.0,
                date=datetime.now() - timedelta(days=5),
                reasoning="Solid fundamentals despite market headwinds",
                impact_score=0.6
            )
        ]
    
    def _calculate_overall_sentiment(self, news_sentiment: float, 
                                   social_sentiment: List[SocialSentiment],
                                   analyst_activity: List[AnalystActivity]) -> float:
        """Calculate overall sentiment score"""
        
        # Weight different sources
        news_weight = 0.4
        social_weight = 0.3
        analyst_weight = 0.3
        
        # News sentiment (already calculated)
        weighted_sentiment = news_sentiment * news_weight
        
        # Social sentiment (average across platforms)
        if social_sentiment:
            avg_social = sum(s.sentiment_score for s in social_sentiment) / len(social_sentiment)
            weighted_sentiment += avg_social * social_weight
        
        # Analyst sentiment
        if analyst_activity:
            analyst_scores = []
            for activity in analyst_activity:
                if activity.action == "upgrade":
                    analyst_scores.append(0.7)
                elif activity.action == "downgrade":
                    analyst_scores.append(-0.7)
                elif activity.action == "initiate" and "buy" in activity.new_rating.lower():
                    analyst_scores.append(0.5)
                else:
                    analyst_scores.append(0.0)
            
            if analyst_scores:
                avg_analyst = sum(analyst_scores) / len(analyst_scores)
                weighted_sentiment += avg_analyst * analyst_weight
        
        return max(-1.0, min(1.0, weighted_sentiment))
    
    def _classify_sentiment_level(self, sentiment_score: float) -> SentimentLevel:
        """Classify sentiment score into level"""
        
        if sentiment_score >= 0.6:
            return SentimentLevel.VERY_POSITIVE
        elif sentiment_score >= 0.2:
            return SentimentLevel.POSITIVE
        elif sentiment_score >= -0.2:
            return SentimentLevel.NEUTRAL
        elif sentiment_score >= -0.6:
            return SentimentLevel.NEGATIVE
        else:
            return SentimentLevel.VERY_NEGATIVE
    
    def _calculate_sentiment_confidence(self, news_items: List[NewsItem], 
                                      social_sentiment: List[SocialSentiment]) -> float:
        """Calculate confidence in sentiment analysis"""
        
        confidence = 0.5  # Base confidence
        
        # More news items increase confidence
        if len(news_items) >= 5:
            confidence += 0.2
        elif len(news_items) >= 3:
            confidence += 0.1
        
        # High relevance scores increase confidence
        if news_items:
            avg_relevance = sum(item.relevance_score for item in news_items) / len(news_items)
            confidence += avg_relevance * 0.2
        
        # Consistent sentiment across sources increases confidence
        if social_sentiment:
            sentiment_variance = self._calculate_sentiment_variance(social_sentiment)
            if sentiment_variance < 0.3:  # Low variance = high consistency
                confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _extract_key_themes(self, news_items: List[NewsItem]) -> List[str]:
        """Extract key themes from news"""
        
        theme_counts = {}
        for item in news_items:
            for keyword in item.keywords:
                theme_counts[keyword] = theme_counts.get(keyword, 0) + 1
        
        # Return top themes
        sorted_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)
        return [theme for theme, count in sorted_themes[:5]]
    
    def _assess_earnings_sentiment(self, earnings_event: Optional[EarningsEvent]) -> str:
        """Assess sentiment around upcoming earnings"""
        
        if not earnings_event:
            return "neutral"
        
        if earnings_event.surprise_probability > 0.7:
            return "bullish"
        elif earnings_event.surprise_probability < 0.3:
            return "bearish"
        else:
            return "neutral"
    
    def _assess_social_momentum(self, social_sentiment: List[SocialSentiment]) -> str:
        """Assess social sentiment momentum"""
        
        if not social_sentiment:
            return "stable"
        
        improving_count = sum(1 for s in social_sentiment if s.sentiment_trend == "improving")
        declining_count = sum(1 for s in social_sentiment if s.sentiment_trend == "declining")
        
        if improving_count > declining_count:
            return "building"
        elif declining_count > improving_count:
            return "fading"
        else:
            return "stable"
    
    def _determine_analyst_consensus(self, analyst_activity: List[AnalystActivity]) -> str:
        """Determine analyst consensus"""
        
        if not analyst_activity:
            return "neutral"
        
        bullish_count = sum(1 for a in analyst_activity if a.action == "upgrade" or 
                           ("buy" in a.new_rating.lower() and a.action == "initiate"))
        bearish_count = sum(1 for a in analyst_activity if a.action == "downgrade")
        
        if bullish_count > bearish_count:
            return "bullish"
        elif bearish_count > bullish_count:
            return "bearish"
        else:
            return "neutral"
    
    def _assess_market_impact(self, sentiment_score: float, news_items: List[NewsItem],
                            earnings_event: Optional[EarningsEvent]) -> Tuple[str, str]:
        """Assess impact on price and volatility"""
        
        # Price impact
        if sentiment_score > 0.4:
            price_impact = "positive"
        elif sentiment_score < -0.4:
            price_impact = "negative"
        else:
            price_impact = "neutral"
        
        # Volatility impact
        volatility_impact = "stable"
        
        # High impact news increases volatility
        high_impact_news = [item for item in news_items if item.impact_estimate == "high"]
        if high_impact_news:
            volatility_impact = "increasing"
        
        # Upcoming earnings increases volatility
        if earnings_event and earnings_event.days_until_earnings <= 7:
            volatility_impact = "increasing"
        
        return price_impact, volatility_impact
    
    def _generate_strategy_implications(self, symbol: str, sentiment_score: float,
                                      earnings_event: Optional[EarningsEvent],
                                      social_sentiment: List[SocialSentiment]) -> List[str]:
        """Generate strategy implications based on sentiment"""
        
        implications = []
        
        # Sentiment-based implications
        if sentiment_score > 0.5:
            implications.append("Positive sentiment supports bullish strategies (LEAPS, Credit Spreads)")
            implications.append("Consider covered calls if expecting consolidation after run-up")
        elif sentiment_score < -0.5:
            implications.append("Negative sentiment favors defensive strategies (Covered Calls, Premium Selling)")
            implications.append("Avoid bullish directional plays until sentiment improves")
        else:
            implications.append("Neutral sentiment favors income strategies (Covered Calls, Wheel)")
        
        # Earnings implications
        if earnings_event and earnings_event.days_until_earnings <= 14:
            if earnings_event.surprise_probability > 0.7:
                implications.append("High earnings surprise probability - consider pre-earnings premium selling")
            implications.append("Elevated IV before earnings - good for premium collection strategies")
        
        # Social momentum implications
        social_momentum = self._assess_social_momentum(social_sentiment)
        if social_momentum == "building":
            implications.append("Building social momentum may support price appreciation")
        elif social_momentum == "fading":
            implications.append("Fading social interest may limit upside momentum")
        
        return implications
    
    def _identify_sentiment_risks(self, news_items: List[NewsItem],
                                earnings_event: Optional[EarningsEvent],
                                social_sentiment: List[SocialSentiment]) -> List[str]:
        """Identify sentiment-related risks"""
        
        risks = []
        
        # News-based risks
        negative_news = [item for item in news_items if item.sentiment_score < -0.3]
        if negative_news:
            risks.append("Recent negative news may pressure stock price")
        
        # Regulatory risks
        regulatory_news = [item for item in news_items if item.category == NewsCategory.REGULATORY]
        if regulatory_news:
            risks.append("Regulatory concerns may create uncertainty")
        
        # Earnings risks
        if earnings_event and earnings_event.days_until_earnings <= 7:
            risks.append("Earnings volatility risk - large moves possible")
        
        # Social sentiment risks
        if social_sentiment:
            avg_social = sum(s.sentiment_score for s in social_sentiment) / len(social_sentiment)
            if avg_social < -0.3:
                risks.append("Negative social sentiment may amplify selling pressure")
        
        return risks
    
    def _get_recency_weight(self, published_date: datetime) -> float:
        """Calculate recency weight for news items"""
        
        days_ago = (datetime.now() - published_date).days
        
        if days_ago == 0:
            return 1.0
        elif days_ago == 1:
            return 0.8
        elif days_ago <= 3:
            return 0.6
        elif days_ago <= 7:
            return 0.4
        else:
            return 0.2
    
    def _calculate_sentiment_variance(self, social_sentiment: List[SocialSentiment]) -> float:
        """Calculate variance in sentiment across social platforms"""
        
        if len(social_sentiment) < 2:
            return 0.0
        
        scores = [s.sentiment_score for s in social_sentiment]
        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
        
        return variance ** 0.5  # Return standard deviation
    
    def _load_sentiment_keywords(self) -> Dict[str, float]:
        """Load sentiment keywords and their weights"""
        
        return {
            # Positive keywords
            "beat": 0.7, "exceed": 0.6, "strong": 0.5, "growth": 0.6,
            "upgrade": 0.8, "bullish": 0.7, "positive": 0.5, "outperform": 0.6,
            "innovation": 0.4, "expansion": 0.5, "partnership": 0.4,
            
            # Negative keywords
            "miss": -0.7, "disappoint": -0.6, "weak": -0.5, "decline": -0.6,
            "downgrade": -0.8, "bearish": -0.7, "negative": -0.5, "underperform": -0.6,
            "concern": -0.4, "risk": -0.3, "challenge": -0.4
        }
