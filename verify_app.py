#!/usr/bin/env python3
"""
Verification script for the enhanced Erica trading system
This script verifies that all components are working correctly
"""

import sys
import os
import time
import tkinter as tk
from tkinter import messagebox

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing module imports...")
    
    try:
        import desktop_app
        print("✅ desktop_app imported successfully")
    except Exception as e:
        print(f"❌ desktop_app import failed: {e}")
        return False
    
    try:
        import ai_assistant
        print("✅ ai_assistant imported successfully")
    except Exception as e:
        print(f"❌ ai_assistant import failed: {e}")
        return False
    
    try:
        import enhanced_fmp_api
        print("✅ enhanced_fmp_api imported successfully")
    except Exception as e:
        print(f"❌ enhanced_fmp_api import failed: {e}")
        return False
    
    try:
        import realtime_data_manager
        print("✅ realtime_data_manager imported successfully")
    except Exception as e:
        print(f"❌ realtime_data_manager import failed: {e}")
        return False
    
    return True

def test_gui_creation():
    """Test GUI creation without showing window"""
    print("\n🖥️ Testing GUI creation...")
    
    try:
        # Create root window but don't show it
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Import and create the app
        from desktop_app import TradingSystemGUI
        app = TradingSystemGUI()
        
        # Check that key components exist
        assert hasattr(app, 'notebook'), "Notebook not found"
        assert hasattr(app, 'ai_assistant_frame'), "AI Assistant frame not found"
        assert hasattr(app, 'data_manager'), "Data manager attribute not found"
        
        print("✅ GUI components created successfully")
        
        # Clean up
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False

def test_api_clients():
    """Test API client creation"""
    print("\n🔌 Testing API clients...")
    
    try:
        from enhanced_fmp_api import EnhancedFMPClient
        client = EnhancedFMPClient("test_key")
        print("✅ Enhanced FMP client created successfully")
    except Exception as e:
        print(f"❌ Enhanced FMP client creation failed: {e}")
        return False
    
    try:
        from ai_assistant import AIAssistantConfig, DataAccessLayer
        config = AIAssistantConfig(openai_api_key="test_key")
        data_access = DataAccessLayer("test_fmp_key")
        print("✅ AI Assistant components created successfully")
    except Exception as e:
        print(f"❌ AI Assistant component creation failed: {e}")
        return False
    
    return True

def show_app_info():
    """Show information about the enhanced application"""
    print("\n" + "="*60)
    print("🎉 ERICA TRADING SYSTEM - ENHANCED VERSION")
    print("="*60)
    
    print("\n🤖 AI ASSISTANT FEATURES:")
    print("   • ChatGPT-4 integration for intelligent trading guidance")
    print("   • Real-time market data analysis and interpretation")
    print("   • Natural language interface for asking trading questions")
    print("   • Erica's complete methodology and strategy knowledge")
    print("   • Contextual explanations and alternative strategy suggestions")
    
    print("\n📊 LIVE DATA INTEGRATION:")
    print("   • Real-time stock prices (updated every minute)")
    print("   • Live market indicators (VIX, put-call ratio, sentiment)")
    print("   • Current options data (IV rank, volume, bid-ask spreads)")
    print("   • News sentiment analysis with real feeds")
    print("   • Earnings calendar with actual dates")
    print("   • Sector performance and rotation signals")
    
    print("\n🚀 HOW TO USE:")
    print("   1. The desktop application should be running now")
    print("   2. Go to Settings tab and enter your OpenAI API key")
    print("   3. Go to AI Assistant tab and click 'Initialize AI Assistant'")
    print("   4. Enable Auto-Refresh in Settings for live data")
    print("   5. Start asking the AI questions about trading strategies!")
    
    print("\n💡 EXAMPLE AI QUESTIONS:")
    print("   • 'What's the best strategy for AAPL today?'")
    print("   • 'Explain the current market environment'")
    print("   • 'What are the key risks I should watch for?'")
    print("   • 'Why was this strategy recommended?'")
    
    print("\n🔑 REQUIRED API KEYS:")
    print("   • OpenAI API Key: Get from https://platform.openai.com/api-keys")
    print("   • FMP API Key: Already configured in your system")
    
    print("\n" + "="*60)

def main():
    """Main verification function"""
    print("🔍 VERIFYING ENHANCED ERICA TRADING SYSTEM")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Please check your installation.")
        return False
    
    # Test GUI creation
    if not test_gui_creation():
        print("\n❌ GUI tests failed. Please check tkinter installation.")
        return False
    
    # Test API clients
    if not test_api_clients():
        print("\n❌ API client tests failed. Please check dependencies.")
        return False
    
    print("\n✅ ALL VERIFICATION TESTS PASSED!")
    
    # Show app information
    show_app_info()
    
    print("\n🎯 VERIFICATION COMPLETE!")
    print("Your enhanced Erica trading system is ready to use!")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 You can now use the enhanced trading system with:")
        print("   • AI-powered trading assistant")
        print("   • Real-time market data feeds")
        print("   • Professional-grade analysis tools")
        
        # Check if the main app is running
        try:
            import psutil
            python_processes = [p for p in psutil.process_iter(['pid', 'name', 'cmdline']) 
                              if p.info['name'] and 'python' in p.info['name'].lower()]
            
            desktop_app_running = any('desktop_app.py' in ' '.join(p.info['cmdline'] or []) 
                                    for p in python_processes)
            
            if desktop_app_running:
                print("\n✅ Desktop application is currently running!")
            else:
                print("\n⚠️  Desktop application not detected. Run: python desktop_app.py")
                
        except ImportError:
            print("\n💡 To check if the app is running, look for the GUI window")
    
    else:
        print("\n❌ Verification failed. Please check the error messages above.")
        sys.exit(1)
