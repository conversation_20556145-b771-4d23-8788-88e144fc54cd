"""
Catalyst Analysis Display Module
Formats and displays comprehensive catalyst analysis for stocks

This module provides:
1. Formatted display of strategy justification
2. Bullish catalysts presentation
3. Risk factors analysis
4. Analyst sentiment summary
5. Complete catalyst analysis formatting

Date: August 18, 2025
"""

from typing import Optional
from enhanced_strategy_analyzer import DetailedStrategyAnalysis, ComprehensiveCatalystAnalysis

def display_comprehensive_catalyst_analysis(analysis: DetailedStrategyAnalysis) -> str:
    """Display comprehensive catalyst analysis in formatted output"""
    
    if not analysis.comprehensive_catalyst_analysis:
        return "❌ Comprehensive catalyst analysis not available"
    
    catalyst_analysis = analysis.comprehensive_catalyst_analysis
    output = []
    
    # Header
    output.append(f"🚀 CATALYST ANALYSIS - {catalyst_analysis.symbol}")
    output.append("=" * 80)
    
    # 1. Strategy Justification
    output.append(f"\n💡 STRATEGY JUSTIFICATION:")
    justification = catalyst_analysis.strategy_justification
    output.append(f"{justification.strategy_name} selected because {justification.primary_reasoning}")
    output.append(f"Confidence Level: {justification.confidence_level}")
    
    output.append(f"\nSupporting Factors:")
    for i, factor in enumerate(justification.supporting_factors, 1):
        output.append(f"  {i}. {factor}")
    
    output.append(f"\nErica's Methodology: {justification.erica_methodology_reference}")
    
    # 2. Bullish Catalysts
    output.append(f"\n📈 BULLISH CATALYSTS:")
    for i, catalyst in enumerate(catalyst_analysis.bullish_catalysts, 1):
        output.append(f"• {catalyst.catalyst_name} ({catalyst.impact_assessment} Impact)")
        output.append(f"  {catalyst.description}")
        output.append(f"  Type: {catalyst.catalyst_type} | Timeframe: {catalyst.timeframe}")
        if catalyst.supporting_data:
            output.append(f"  Supporting Data: {', '.join(catalyst.supporting_data[:2])}")
        output.append("")
    
    # 3. Risk Factors
    output.append(f"⚠️  RISK FACTORS:")
    for i, risk in enumerate(catalyst_analysis.risk_factors, 1):
        output.append(f"• {risk.risk_name} ({risk.severity} Severity)")
        output.append(f"  {risk.description}")
        output.append(f"  Type: {risk.risk_type} | Probability: {risk.probability}")
        output.append(f"  Mitigation: {risk.mitigation_strategy}")
        output.append("")
    
    # 4. Analyst Sentiment
    output.append(f"📊 ANALYST SENTIMENT:")
    sentiment = catalyst_analysis.analyst_sentiment
    output.append(f"Consensus: {sentiment.consensus_rating} | Average Target: ${sentiment.average_price_target:.2f} ({sentiment.upside_downside_percent:+.1f}% from current)")
    output.append(f"Analyst Coverage: {sentiment.number_of_analysts} analysts")
    output.append(f"Target Range: ${sentiment.target_range[0]:.2f} - ${sentiment.target_range[1]:.2f}")
    
    if sentiment.recent_changes:
        output.append(f"Recent Changes:")
        for change in sentiment.recent_changes[:2]:
            output.append(f"  • {change}")
    
    # 5. Overall Outlook
    output.append(f"\n🎯 OVERALL OUTLOOK:")
    output.append(f"{catalyst_analysis.overall_outlook}")
    
    return "\n".join(output)

def display_catalyst_summary(analysis: DetailedStrategyAnalysis) -> str:
    """Display a condensed catalyst summary"""
    
    if not analysis.comprehensive_catalyst_analysis:
        return "Catalyst analysis not available"
    
    catalyst_analysis = analysis.comprehensive_catalyst_analysis
    
    # Count high-impact catalysts and high-severity risks
    high_impact_catalysts = len([c for c in catalyst_analysis.bullish_catalysts if c.impact_assessment == "High"])
    high_severity_risks = len([r for r in catalyst_analysis.risk_factors if r.severity == "High"])
    
    summary = f"📊 CATALYST SUMMARY: {high_impact_catalysts} high-impact catalysts, {high_severity_risks} high-severity risks"
    summary += f" | Analyst consensus: {catalyst_analysis.analyst_sentiment.consensus_rating}"
    summary += f" | Target: ${catalyst_analysis.analyst_sentiment.average_price_target:.2f} ({catalyst_analysis.analyst_sentiment.upside_downside_percent:+.1f}%)"
    
    return summary

def print_catalyst_analysis(analysis: DetailedStrategyAnalysis):
    """Print comprehensive catalyst analysis to console"""
    print(display_comprehensive_catalyst_analysis(analysis))

def get_catalyst_highlights(analysis: DetailedStrategyAnalysis) -> dict:
    """Get key catalyst highlights for API/programmatic use"""
    
    if not analysis.comprehensive_catalyst_analysis:
        return {"error": "Catalyst analysis not available"}
    
    catalyst_analysis = analysis.comprehensive_catalyst_analysis
    
    return {
        "strategy_justification": {
            "strategy": catalyst_analysis.strategy_justification.strategy_name,
            "reasoning": catalyst_analysis.strategy_justification.primary_reasoning,
            "confidence": catalyst_analysis.strategy_justification.confidence_level
        },
        "top_bullish_catalyst": {
            "name": catalyst_analysis.bullish_catalysts[0].catalyst_name if catalyst_analysis.bullish_catalysts else "None",
            "impact": catalyst_analysis.bullish_catalysts[0].impact_assessment if catalyst_analysis.bullish_catalysts else "None"
        },
        "top_risk": {
            "name": catalyst_analysis.risk_factors[0].risk_name if catalyst_analysis.risk_factors else "None",
            "severity": catalyst_analysis.risk_factors[0].severity if catalyst_analysis.risk_factors else "None"
        },
        "analyst_sentiment": {
            "consensus": catalyst_analysis.analyst_sentiment.consensus_rating,
            "target": catalyst_analysis.analyst_sentiment.average_price_target,
            "upside": catalyst_analysis.analyst_sentiment.upside_downside_percent
        },
        "overall_outlook": catalyst_analysis.overall_outlook
    }

# Example usage functions
def demo_catalyst_display():
    """Demo function showing catalyst analysis display"""
    print("🚀 CATALYST ANALYSIS DISPLAY MODULE")
    print("This module formats comprehensive catalyst analysis including:")
    print("• Strategy justification with confidence levels")
    print("• Bullish catalysts with impact assessments")
    print("• Risk factors with severity ratings")
    print("• Analyst sentiment and price targets")
    print("• Overall outlook determination")
    print("\nUse with enhanced strategy analyzer results for complete analysis.")

if __name__ == "__main__":
    demo_catalyst_display()
