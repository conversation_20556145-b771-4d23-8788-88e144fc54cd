"""
Technical Analysis Components for <PERSON>'s Trading System

Implements key technical indicators that support <PERSON>'s trading decisions:
- RSI (Relative Strength Index)
- Moving Averages (20, 50, 200 day)
- Volatility Analysis (ATR, Historical Volatility)
- Support/Resistance Levels
- Trend Analysis
- Market Condition Assessment

These indicators help determine optimal entry/exit points and strategy selection.
"""

import math
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from daily_outline import MarketAnalysis, MarketCondition

@dataclass
class TechnicalIndicators:
    """Container for all technical analysis results"""
    rsi: Optional[float] = None
    rsi_signal: str = "NEUTRAL"  # OVERSOLD, OVERBOUGHT, NEUTRAL
    
    ma_20: Optional[float] = None
    ma_50: Optional[float] = None
    ma_200: Optional[float] = None
    ma_signal: str = "NEUTRAL"  # BULLISH, BEARISH, NEUTRAL
    
    atr_14: Optional[float] = None
    historical_volatility: Optional[float] = None
    volatility_rank: Optional[float] = None  # 0-1 scale
    
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    
    trend_strength: str = "NEUTRAL"  # STRONG_UPTREND, UPTREND, NEUTRAL, DOWNTREND, STRONG_DOWNTREND
    trend_score: float = 0.0  # -1 to 1 scale
    
    market_condition: MarketCondition = MarketCondition.NEUTRAL

class TechnicalAnalyzer:
    """Technical analysis engine for Erica's trading system"""
    
    def __init__(self):
        self.lookback_periods = {
            'rsi': 14,
            'ma_short': 20,
            'ma_medium': 50,
            'ma_long': 200,
            'atr': 14,
            'volatility': 30
        }
    
    def analyze(self, symbol: str, price_data: List[Dict], current_price: float) -> TechnicalIndicators:
        """
        Perform comprehensive technical analysis
        
        Args:
            symbol: Stock symbol
            price_data: List of historical price data (OHLCV)
            current_price: Current stock price
            
        Returns:
            TechnicalIndicators object with all analysis results
        """
        if not price_data or len(price_data) < 20:
            return TechnicalIndicators()
        
        indicators = TechnicalIndicators()
        
        # Calculate RSI
        indicators.rsi = self._calculate_rsi(price_data)
        indicators.rsi_signal = self._interpret_rsi(indicators.rsi)
        
        # Calculate Moving Averages
        indicators.ma_20 = self._calculate_sma(price_data, 20)
        indicators.ma_50 = self._calculate_sma(price_data, 50)
        indicators.ma_200 = self._calculate_sma(price_data, 200)
        indicators.ma_signal = self._interpret_moving_averages(
            current_price, indicators.ma_20, indicators.ma_50, indicators.ma_200
        )
        
        # Calculate Volatility Metrics
        indicators.atr_14 = self._calculate_atr(price_data)
        indicators.historical_volatility = self._calculate_historical_volatility(price_data)
        indicators.volatility_rank = self._calculate_volatility_rank(price_data, indicators.historical_volatility)
        
        # Calculate Support/Resistance
        indicators.support_level, indicators.resistance_level = self._calculate_support_resistance(price_data)
        
        # Determine Trend
        indicators.trend_strength, indicators.trend_score = self._analyze_trend(
            current_price, indicators.ma_20, indicators.ma_50, indicators.ma_200, price_data
        )
        
        # Determine Overall Market Condition
        indicators.market_condition = self._determine_market_condition(indicators)
        
        return indicators
    
    def _calculate_rsi(self, price_data: List[Dict], period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index"""
        if len(price_data) < period + 1:
            return None
        
        closes = [float(bar['close']) for bar in price_data[-period-1:]]
        
        gains = []
        losses = []
        
        for i in range(1, len(closes)):
            change = closes[i] - closes[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return None
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return round(rsi, 2)
    
    def _interpret_rsi(self, rsi: Optional[float]) -> str:
        """Interpret RSI signal"""
        if rsi is None:
            return "NEUTRAL"
        
        if rsi >= 70:
            return "OVERBOUGHT"
        elif rsi <= 30:
            return "OVERSOLD"
        else:
            return "NEUTRAL"
    
    def _calculate_sma(self, price_data: List[Dict], period: int) -> Optional[float]:
        """Calculate Simple Moving Average"""
        if len(price_data) < period:
            return None
        
        closes = [float(bar['close']) for bar in price_data[-period:]]
        return round(sum(closes) / len(closes), 2)
    
    def _interpret_moving_averages(self, current_price: float, ma_20: Optional[float], 
                                 ma_50: Optional[float], ma_200: Optional[float]) -> str:
        """Interpret moving average signals"""
        if not all([ma_20, ma_50]):
            return "NEUTRAL"
        
        # Price above/below moving averages
        above_ma20 = current_price > ma_20
        above_ma50 = current_price > ma_50
        
        # Moving average alignment
        ma_bullish_alignment = ma_20 > ma_50
        if ma_200:
            ma_bullish_alignment = ma_bullish_alignment and ma_50 > ma_200
        
        if above_ma20 and above_ma50 and ma_bullish_alignment:
            return "BULLISH"
        elif not above_ma20 and not above_ma50 and not ma_bullish_alignment:
            return "BEARISH"
        else:
            return "NEUTRAL"
    
    def _calculate_atr(self, price_data: List[Dict], period: int = 14) -> Optional[float]:
        """Calculate Average True Range"""
        if len(price_data) < period + 1:
            return None
        
        true_ranges = []
        
        for i in range(1, len(price_data)):
            high = float(price_data[i]['high'])
            low = float(price_data[i]['low'])
            prev_close = float(price_data[i-1]['close'])
            
            tr = max(
                high - low,
                abs(high - prev_close),
                abs(low - prev_close)
            )
            true_ranges.append(tr)
        
        if len(true_ranges) < period:
            return None
        
        # Wilder's smoothing method
        atr = sum(true_ranges[-period:]) / period
        
        return round(atr, 2)
    
    def _calculate_historical_volatility(self, price_data: List[Dict], period: int = 30) -> Optional[float]:
        """Calculate historical volatility (annualized)"""
        if len(price_data) < period + 1:
            return None
        
        closes = [float(bar['close']) for bar in price_data[-period-1:]]
        
        # Calculate daily returns
        returns = []
        for i in range(1, len(closes)):
            daily_return = math.log(closes[i] / closes[i-1])
            returns.append(daily_return)
        
        if len(returns) < 2:
            return None
        
        # Calculate standard deviation
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / (len(returns) - 1)
        std_dev = math.sqrt(variance)
        
        # Annualize (252 trading days)
        annual_volatility = std_dev * math.sqrt(252)
        
        return round(annual_volatility, 4)
    
    def _calculate_volatility_rank(self, price_data: List[Dict], current_vol: Optional[float]) -> Optional[float]:
        """Calculate volatility rank (current vol vs 1-year range)"""
        if not current_vol or len(price_data) < 252:
            return None
        
        # Calculate volatility for each 30-day period over the past year
        volatilities = []
        for i in range(30, min(len(price_data), 252)):
            period_data = price_data[i-30:i]
            vol = self._calculate_historical_volatility(period_data, 30)
            if vol:
                volatilities.append(vol)
        
        if len(volatilities) < 10:
            return None
        
        min_vol = min(volatilities)
        max_vol = max(volatilities)
        
        if max_vol == min_vol:
            return 0.5
        
        vol_rank = (current_vol - min_vol) / (max_vol - min_vol)
        return round(vol_rank, 2)
    
    def _calculate_support_resistance(self, price_data: List[Dict]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate support and resistance levels using pivot points"""
        if len(price_data) < 20:
            return None, None
        
        # Use recent 20 days for pivot calculation
        recent_data = price_data[-20:]
        
        highs = [float(bar['high']) for bar in recent_data]
        lows = [float(bar['low']) for bar in recent_data]
        closes = [float(bar['close']) for bar in recent_data]
        
        # Simple pivot point calculation
        pivot = (max(highs) + min(lows) + closes[-1]) / 3
        
        # Support and resistance based on recent price action
        resistance = max(highs[-10:])  # Recent 10-day high
        support = min(lows[-10:])      # Recent 10-day low
        
        return round(support, 2), round(resistance, 2)
    
    def _analyze_trend(self, current_price: float, ma_20: Optional[float], 
                      ma_50: Optional[float], ma_200: Optional[float], 
                      price_data: List[Dict]) -> Tuple[str, float]:
        """Analyze trend strength and direction"""
        if not ma_20 or not ma_50:
            return "NEUTRAL", 0.0
        
        trend_score = 0.0
        
        # Price vs moving averages
        if current_price > ma_20:
            trend_score += 0.3
        else:
            trend_score -= 0.3
        
        if current_price > ma_50:
            trend_score += 0.3
        else:
            trend_score -= 0.3
        
        if ma_200 and current_price > ma_200:
            trend_score += 0.2
        elif ma_200:
            trend_score -= 0.2
        
        # Moving average alignment
        if ma_20 > ma_50:
            trend_score += 0.2
        else:
            trend_score -= 0.2
        
        # Recent price momentum (last 5 days)
        if len(price_data) >= 5:
            recent_closes = [float(bar['close']) for bar in price_data[-5:]]
            if recent_closes[-1] > recent_closes[0]:
                trend_score += 0.1
            else:
                trend_score -= 0.1
        
        # Determine trend strength
        if trend_score >= 0.7:
            trend_strength = "STRONG_UPTREND"
        elif trend_score >= 0.3:
            trend_strength = "UPTREND"
        elif trend_score <= -0.7:
            trend_strength = "STRONG_DOWNTREND"
        elif trend_score <= -0.3:
            trend_strength = "DOWNTREND"
        else:
            trend_strength = "NEUTRAL"
        
        return trend_strength, round(trend_score, 2)
    
    def _determine_market_condition(self, indicators: TechnicalIndicators) -> MarketCondition:
        """Determine overall market condition for strategy selection"""
        
        # High volatility condition
        if indicators.volatility_rank and indicators.volatility_rank > 0.8:
            return MarketCondition.HIGH_VOLATILITY
        elif indicators.volatility_rank and indicators.volatility_rank < 0.2:
            return MarketCondition.LOW_VOLATILITY
        
        # Trend-based conditions
        if indicators.trend_strength in ["STRONG_UPTREND", "UPTREND"]:
            return MarketCondition.BULLISH
        elif indicators.trend_strength in ["STRONG_DOWNTREND", "DOWNTREND"]:
            return MarketCondition.BEARISH
        else:
            return MarketCondition.NEUTRAL
    
    def get_strategy_recommendations(self, indicators: TechnicalIndicators) -> List[str]:
        """Get strategy recommendations based on technical analysis"""
        recommendations = []
        
        # RSI-based recommendations
        if indicators.rsi_signal == "OVERSOLD":
            recommendations.append("Consider bullish strategies (LEAPS, Cash-Secured Puts)")
        elif indicators.rsi_signal == "OVERBOUGHT":
            recommendations.append("Consider bearish strategies (Covered Calls, Credit Spreads)")
        
        # Volatility-based recommendations
        if indicators.volatility_rank and indicators.volatility_rank > 0.7:
            recommendations.append("High volatility favors premium selling strategies")
        elif indicators.volatility_rank and indicators.volatility_rank < 0.3:
            recommendations.append("Low volatility favors directional strategies (LEAPS)")
        
        # Trend-based recommendations
        if indicators.trend_strength in ["STRONG_UPTREND", "UPTREND"]:
            recommendations.append("Strong uptrend supports bullish strategies")
        elif indicators.trend_strength in ["STRONG_DOWNTREND", "DOWNTREND"]:
            recommendations.append("Downtrend suggests defensive strategies")
        
        return recommendations
