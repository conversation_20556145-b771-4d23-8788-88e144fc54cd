"""
Example Usage of the AI-Powered Daily Stock Investment Planning System

This script demonstrates how to use the system programmatically
and shows example outputs for different analysis modes.
"""

import os
from datetime import datetime
from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
from daily_recommendations import DailyRecommendationGenerator

def example_basic_usage():
    """Example of basic system usage"""
    print("=" * 60)
    print("EXAMPLE 1: Basic Market Data Analysis")
    print("=" * 60)
    
    # Get API key
    apikey = resolve_fmp_key(None)
    if not apikey:
        print("Please set your FMP API key first!")
        return
    
    # Analyze a single symbol
    symbol = "AAPL"
    quote = fmp_quote(symbol, apikey)
    
    if quote:
        print(f"\n{symbol} Current Data:")
        print(f"  Price: ${quote.get('price', 0):.2f}")
        print(f"  Change: {quote.get('changesPercentage', 0):.2f}%")
        print(f"  Volume: {quote.get('volume', 0):,}")
        print(f"  52W Range: ${quote.get('yearLow', 0):.2f} - ${quote.get('yearHigh', 0):.2f}")
    else:
        print(f"Could not fetch data for {symbol}")

def example_strategy_analysis():
    """Example of full strategy analysis"""
    print("\n" + "=" * 60)
    print("EXAMPLE 2: Full Strategy Analysis")
    print("=" * 60)
    
    # Get API key
    apikey = resolve_fmp_key(None)
    if not apikey:
        print("Please set your FMP API key first!")
        return
    
    # Set up the recommendation generator
    account_size = 100000  # $100k account
    symbols = ["AAPL", "NVDA"]
    
    generator = DailyRecommendationGenerator(account_size, symbols)
    
    # Prepare market data
    market_data = {}
    historical_data = {}
    
    print(f"\nFetching data for {', '.join(symbols)}...")
    
    for symbol in symbols:
        # Get current quote
        quote = fmp_quote(symbol, apikey)
        if quote:
            market_data[symbol] = {"quote": quote}
        
        # Get historical data
        hist = fmp_historical_daily(symbol, apikey, limit=60)
        if hist:
            historical_data[symbol] = hist
    
    if market_data and historical_data:
        try:
            # Generate daily report
            print("Generating comprehensive analysis...")
            daily_report = generator.generate_daily_report(market_data, historical_data)
            
            # Display formatted report
            formatted_report = generator.format_daily_report(daily_report)
            print("\n" + formatted_report)
            
        except Exception as e:
            print(f"Error generating report: {e}")
            print("This might be due to missing options data or API limitations.")
    else:
        print("Could not fetch sufficient data for analysis.")

def example_custom_parameters():
    """Example of using custom parameters"""
    print("\n" + "=" * 60)
    print("EXAMPLE 3: Custom Risk Parameters")
    print("=" * 60)
    
    from risk_management import RiskManager, RiskParameters
    from erica_strategies import StrategyParameters
    
    # Custom risk parameters for conservative approach
    conservative_risk = RiskParameters(
        max_account_risk=0.015,      # 1.5% max risk per trade (vs default 2%)
        max_portfolio_risk=0.15,     # 15% max portfolio risk (vs default 20%)
        max_position_size=0.08,      # 8% max position size (vs default 10%)
        cash_reserve=0.25            # Keep 25% cash (vs default 20%)
    )
    
    # Custom strategy parameters
    conservative_strategy = StrategyParameters(
        min_premium=0.40,            # Higher minimum premium
        max_dte=30,                  # Shorter expiration preference
        target_delta=0.25,           # More conservative delta
        profit_target=0.40           # Take profits earlier at 40%
    )
    
    print("Conservative Risk Parameters:")
    print(f"  Max risk per trade: {conservative_risk.max_account_risk:.1%}")
    print(f"  Max portfolio risk: {conservative_risk.max_portfolio_risk:.1%}")
    print(f"  Cash reserve: {conservative_risk.cash_reserve:.1%}")
    
    print("\nConservative Strategy Parameters:")
    print(f"  Minimum premium: ${conservative_strategy.min_premium:.2f}")
    print(f"  Max days to expiration: {conservative_strategy.max_dte}")
    print(f"  Target delta: {conservative_strategy.target_delta:.2f}")
    print(f"  Profit target: {conservative_strategy.profit_target:.0%}")
    
    # Create risk manager with custom parameters
    risk_manager = RiskManager(100000, conservative_risk)
    print(f"\nRisk manager created for ${risk_manager.account_size:,} account")

def example_individual_strategy_analysis():
    """Example of analyzing individual strategies"""
    print("\n" + "=" * 60)
    print("EXAMPLE 4: Individual Strategy Analysis")
    print("=" * 60)
    
    from erica_strategies import EricaStrategies, OptionData, OptionType
    from daily_outline import MarketAnalysis, MarketCondition
    
    # Create sample market analysis
    market_analysis = MarketAnalysis(
        symbol="AAPL",
        current_price=230.0,
        trend="UPTREND",
        volatility=0.28,
        rsi=58.0,
        ma_20=228.0,
        ma_50=225.0,
        support_level=225.0,
        resistance_level=235.0,
        market_condition=MarketCondition.BULLISH
    )
    
    # Create sample options data
    options_chain = [
        OptionData(
            symbol="AAPL",
            strike=235.0,
            expiration="2024-03-15",
            option_type=OptionType.CALL,
            bid=3.20,
            ask=3.40,
            volume=2500,
            open_interest=8000,
            implied_volatility=0.28,
            delta=0.32
        ),
        OptionData(
            symbol="AAPL",
            strike=225.0,
            expiration="2024-03-15",
            option_type=OptionType.PUT,
            bid=2.10,
            ask=2.30,
            volume=1800,
            open_interest=5500,
            implied_volatility=0.25,
            delta=-0.18
        )
    ]
    
    # Analyze strategies
    strategies = EricaStrategies()
    
    print(f"Analyzing {market_analysis.symbol} at ${market_analysis.current_price:.2f}")
    print(f"Market Condition: {market_analysis.market_condition.value}")
    print(f"Volatility: {market_analysis.volatility:.1%}")
    print(f"RSI: {market_analysis.rsi}")
    
    # Covered Call Analysis
    covered_call = strategies.analyze_covered_call_opportunity(
        market_analysis, options_chain, 100000
    )
    
    if covered_call:
        print(f"\nCovered Call Signal:")
        print(f"  Action: {covered_call.action}")
        print(f"  Confidence: {covered_call.confidence:.0%}")
        print(f"  Reasoning: {covered_call.reasoning}")
        print(f"  Entry Price: ${covered_call.entry_price:.2f}")
        if covered_call.target_price:
            print(f"  Target Price: ${covered_call.target_price:.2f}")
    else:
        print("\nNo covered call opportunity found")
    
    # Credit Spread Analysis
    credit_spread = strategies.analyze_credit_spread_opportunity(
        market_analysis, options_chain, 100000
    )
    
    if credit_spread:
        print(f"\nCredit Spread Signal:")
        print(f"  Action: {credit_spread.action}")
        print(f"  Confidence: {credit_spread.confidence:.0%}")
        print(f"  Reasoning: {credit_spread.reasoning}")
        print(f"  Max Profit: ${credit_spread.entry_price:.2f}")
    else:
        print("\nNo credit spread opportunity found")

def example_risk_analysis():
    """Example of risk analysis"""
    print("\n" + "=" * 60)
    print("EXAMPLE 5: Risk Analysis")
    print("=" * 60)
    
    from risk_management import RiskManager
    from daily_outline import TradingSignal, StrategyType
    
    # Create risk manager
    risk_manager = RiskManager(100000)  # $100k account
    
    # Create sample trading signal
    signal = TradingSignal(
        symbol="AAPL",
        strategy=StrategyType.COVERED_CALL,
        action="SELL",
        confidence=0.78,
        reasoning="High IV covered call opportunity",
        entry_price=3.20,
        target_price=1.60,
        risk_reward_ratio=2.0
    )
    
    # Calculate position size
    position_size, reasoning = risk_manager.calculate_position_size(
        signal, 0.28  # 28% volatility
    )
    
    print(f"Signal Analysis:")
    print(f"  Symbol: {signal.symbol}")
    print(f"  Strategy: {signal.strategy.value}")
    print(f"  Confidence: {signal.confidence:.0%}")
    print(f"  Risk/Reward: {signal.risk_reward_ratio:.1f}")
    
    print(f"\nPosition Sizing:")
    print(f"  Recommended Size: ${position_size:,.0f}")
    print(f"  Percentage of Account: {(position_size/100000)*100:.1f}%")
    print(f"  Reasoning: {reasoning}")
    
    # Assess position risk
    position_risk = risk_manager.assess_position_risk(signal, position_size, 0.28)
    
    print(f"\nRisk Assessment:")
    print(f"  Max Gain: ${position_risk.max_gain:,.0f}")
    print(f"  Max Loss: ${position_risk.max_loss:,.0f}")
    print(f"  Win Probability: {position_risk.probability_of_profit:.0%}")
    print(f"  Volatility Risk: {position_risk.volatility_risk}")

def main():
    """Run all examples"""
    print("AI-POWERED DAILY STOCK INVESTMENT PLANNING SYSTEM")
    print("Example Usage Demonstrations")
    print("=" * 60)
    
    # Check if API key is available
    apikey = resolve_fmp_key(None)
    if not apikey:
        print("\n⚠️  WARNING: No FMP API key found!")
        print("Set your API key using one of these methods:")
        print("  export FMP_API_KEY='your_key_here'")
        print("  python example_usage.py --fmp-key your_key_here")
        print("  echo 'your_key_here' > fmp.key")
        print("\nRunning examples with mock data where possible...\n")
    
    try:
        # Run examples
        example_basic_usage()
        example_strategy_analysis()
        example_custom_parameters()
        example_individual_strategy_analysis()
        example_risk_analysis()
        
        print("\n" + "=" * 60)
        print("EXAMPLES COMPLETED")
        print("=" * 60)
        print("\nNext Steps:")
        print("1. Set up your FMP API key")
        print("2. Run: python daily_outline.py --analysis-mode full")
        print("3. Review the daily recommendations")
        print("4. Implement signals based on your risk tolerance")
        print("5. Track performance and adjust parameters as needed")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        print("This might be due to missing API key or network issues.")

if __name__ == "__main__":
    main()
