"""
Intelligent Strategy Engine Demonstration
Shows the enhanced multi-factor analysis and decision-making capabilities

This demo showcases how the system now thinks like <PERSON> when selecting strategies,
considering all market factors she discusses in her educational content.
"""

from intelligent_strategy_engine import IntelligentStrategyEngine
from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime, SentimentLevel
from strategy_decision_tree import StrategyConfidence
from daily_outline import StrategyType

def create_sample_market_scenarios():
    """Create different market scenarios to demonstrate decision-making"""
    
    scenarios = {
        "high_volatility_bull": MarketFactors(
            vix_level=28.5,
            vix_percentile=85.0,
            put_call_ratio=0.75,
            market_breadth=1.6,
            volatility_regime=VolatilityRegime.HIGH_VOL,
            iv_rank_spy=0.82,
            iv_term_structure="backwardation",
            market_regime=MarketRegime.BULL_MARKET,
            trend_strength=0.7,
            regime_confidence=0.8,
            fear_greed_index=72.0,
            sentiment_level=SentimentLevel.GREED,
            social_sentiment=0.4,
            spy_technical_score=0.75,
            sector_rotation_signal="growth_rotation",
            relative_strength_leaders=["Technology", "Communication"],
            earnings_season_intensity=0.8,
            fed_meeting_proximity=12,
            major_events_this_week=["CPI Report"],
            bullish_factors=0.75,
            bearish_factors=0.25,
            volatility_factors=0.85,
            uncertainty_factors=0.4
        ),
        
        "low_volatility_sideways": MarketFactors(
            vix_level=14.2,
            vix_percentile=20.0,
            put_call_ratio=1.1,
            market_breadth=1.0,
            volatility_regime=VolatilityRegime.LOW_VOL,
            iv_rank_spy=0.25,
            iv_term_structure="contango",
            market_regime=MarketRegime.SIDEWAYS_MARKET,
            trend_strength=0.1,
            regime_confidence=0.7,
            fear_greed_index=45.0,
            sentiment_level=SentimentLevel.NEUTRAL,
            social_sentiment=0.0,
            spy_technical_score=0.5,
            sector_rotation_signal="neutral",
            relative_strength_leaders=[],
            earnings_season_intensity=0.3,
            fed_meeting_proximity=25,
            major_events_this_week=[],
            bullish_factors=0.4,
            bearish_factors=0.4,
            volatility_factors=0.2,
            uncertainty_factors=0.3
        ),
        
        "extreme_fear_bear": MarketFactors(
            vix_level=35.8,
            vix_percentile=95.0,
            put_call_ratio=1.8,
            market_breadth=0.4,
            volatility_regime=VolatilityRegime.EXTREME_VOL,
            iv_rank_spy=0.95,
            iv_term_structure="steep_backwardation",
            market_regime=MarketRegime.BEAR_MARKET,
            trend_strength=-0.8,
            regime_confidence=0.9,
            fear_greed_index=15.0,
            sentiment_level=SentimentLevel.EXTREME_FEAR,
            social_sentiment=-0.7,
            spy_technical_score=0.2,
            sector_rotation_signal="defensive_rotation",
            relative_strength_leaders=["Utilities", "Consumer Staples"],
            earnings_season_intensity=0.6,
            fed_meeting_proximity=5,
            major_events_this_week=["Fed Meeting", "Jobs Report"],
            bullish_factors=0.15,
            bearish_factors=0.85,
            volatility_factors=0.95,
            uncertainty_factors=0.8
        )
    }
    
    return scenarios

def create_sample_stock_scenarios():
    """Create different stock-specific scenarios"""
    
    scenarios = {
        "AAPL_strong_setup": StockSpecificFactors(
            symbol="AAPL",
            news_sentiment_score=0.4,
            earnings_days_away=25,
            earnings_move_estimate=0.06,
            recent_analyst_changes=[],
            relative_strength_vs_spy=0.3,
            technical_confluence_score=0.8,
            support_resistance_clarity=0.9,
            trend_alignment="strong_up",
            unusual_options_activity=False,
            iv_rank=0.65,
            iv_vs_hv_ratio=1.1,
            options_flow_sentiment="bullish",
            sector_performance=0.2,
            sector_rotation_impact="positive",
            wheel_suitability_score=0.8,
            covered_call_attractiveness=0.9,
            credit_spread_opportunity=0.7,
            leaps_opportunity=0.8
        ),
        
        "NVDA_high_volatility": StockSpecificFactors(
            symbol="NVDA",
            news_sentiment_score=0.1,
            earnings_days_away=8,
            earnings_move_estimate=0.15,
            recent_analyst_changes=[{"type": "upgrade", "firm": "Goldman"}],
            relative_strength_vs_spy=0.5,
            technical_confluence_score=0.6,
            support_resistance_clarity=0.4,
            trend_alignment="up",
            unusual_options_activity=True,
            iv_rank=0.85,
            iv_vs_hv_ratio=1.4,
            options_flow_sentiment="neutral",
            sector_performance=0.3,
            sector_rotation_impact="positive",
            wheel_suitability_score=0.3,
            covered_call_attractiveness=0.8,
            credit_spread_opportunity=0.6,
            leaps_opportunity=0.5
        ),
        
        "AMD_earnings_setup": StockSpecificFactors(
            symbol="AMD",
            news_sentiment_score=-0.2,
            earnings_days_away=3,
            earnings_move_estimate=0.12,
            recent_analyst_changes=[],
            relative_strength_vs_spy=0.1,
            technical_confluence_score=0.5,
            support_resistance_clarity=0.7,
            trend_alignment="neutral",
            unusual_options_activity=True,
            iv_rank=0.9,
            iv_vs_hv_ratio=1.6,
            options_flow_sentiment="bearish",
            sector_performance=0.1,
            sector_rotation_impact="neutral",
            wheel_suitability_score=0.7,
            covered_call_attractiveness=0.9,
            credit_spread_opportunity=0.4,
            leaps_opportunity=0.3
        )
    }
    
    return scenarios

def demonstrate_intelligent_decision_making():
    """Demonstrate the intelligent decision-making process"""
    
    print("🧠 INTELLIGENT STRATEGY ENGINE DEMONSTRATION")
    print("=" * 60)
    print("Showcasing multi-factor analysis and Erica's decision framework")
    print("=" * 60)
    print()
    
    # Create mock engine (without real API)
    from strategy_decision_tree import StrategyDecisionTree
    decision_tree = StrategyDecisionTree()
    
    # Get scenarios
    market_scenarios = create_sample_market_scenarios()
    stock_scenarios = create_sample_stock_scenarios()
    
    # Demonstrate different market environments
    for scenario_name, market_factors in market_scenarios.items():
        print(f"📊 SCENARIO: {scenario_name.replace('_', ' ').upper()}")
        print("-" * 40)
        
        # Market environment summary
        print(f"Market Regime: {market_factors.market_regime.value}")
        print(f"Volatility: {market_factors.volatility_regime.value} (VIX: {market_factors.vix_level})")
        print(f"Sentiment: {market_factors.sentiment_level.value} (F&G: {market_factors.fear_greed_index})")
        print(f"Bullish Factors: {market_factors.bullish_factors:.0%}")
        print(f"Volatility Opportunity: {market_factors.volatility_factors:.0%}")
        print()
        
        # Test each stock in this market environment
        for stock_name, stock_factors in stock_scenarios.items():
            print(f"  📈 {stock_factors.symbol} Analysis:")
            
            # Get recommendation
            recommendation = decision_tree.recommend_strategy(
                stock_factors.symbol, market_factors, stock_factors
            )
            
            # Display recommendation
            confidence_emoji = {
                StrategyConfidence.VERY_HIGH: "🟢",
                StrategyConfidence.HIGH: "🔵",
                StrategyConfidence.MODERATE: "🟡",
                StrategyConfidence.LOW: "🟠",
                StrategyConfidence.VERY_LOW: "🔴"
            }.get(recommendation.confidence_level, "⚪")
            
            print(f"    {confidence_emoji} **{recommendation.primary_strategy.value.upper()}** "
                  f"({recommendation.confidence:.0%} confidence)")
            
            # Show key factors
            print(f"    📊 Factor Scores:")
            print(f"      • Market Environment: {recommendation.market_environment_score:.0%}")
            print(f"      • Stock-Specific: {recommendation.stock_specific_score:.0%}")
            print(f"      • Erica's Criteria: {recommendation.erica_criteria_score:.0%}")
            print(f"      • Risk-Adjusted: {recommendation.risk_adjusted_score:.0%}")
            
            # Show supporting factors
            if recommendation.key_supporting_factors:
                print(f"    ✅ Key Supporting Factors:")
                for factor in recommendation.key_supporting_factors[:2]:
                    print(f"      • {factor}")
            
            # Show risks
            if recommendation.key_risk_factors:
                print(f"    ⚠️  Key Risks:")
                for risk in recommendation.key_risk_factors[:2]:
                    print(f"      • {risk}")
            
            # Show execution parameters
            dte_min, dte_max = recommendation.recommended_dte
            delta_min, delta_max = recommendation.recommended_delta
            print(f"    🎯 Execution: {dte_min}-{dte_max} DTE, Δ {delta_min:.2f}-{delta_max:.2f}")
            
            print()
        
        print("=" * 60)
        print()

def demonstrate_strategy_comparison():
    """Demonstrate how different market conditions favor different strategies"""
    
    print("🔄 STRATEGY SELECTION COMPARISON")
    print("=" * 50)
    print("How market conditions influence strategy selection")
    print("=" * 50)
    print()
    
    from strategy_decision_tree import StrategyDecisionTree
    decision_tree = StrategyDecisionTree()
    
    # Use AAPL as test case across different market conditions
    aapl_factors = create_sample_stock_scenarios()["AAPL_strong_setup"]
    market_scenarios = create_sample_market_scenarios()
    
    print(f"📈 {aapl_factors.symbol} - Strategy Selection Across Market Conditions")
    print("-" * 50)
    
    results = []
    
    for scenario_name, market_factors in market_scenarios.items():
        recommendation = decision_tree.recommend_strategy(
            aapl_factors.symbol, market_factors, aapl_factors
        )
        
        results.append({
            'scenario': scenario_name.replace('_', ' ').title(),
            'strategy': recommendation.primary_strategy.value.replace('_', ' ').title(),
            'confidence': recommendation.confidence,
            'market_score': recommendation.market_environment_score,
            'volatility': market_factors.volatility_factors
        })
    
    # Display comparison table
    print(f"{'Market Condition':<20} {'Strategy':<15} {'Confidence':<12} {'Vol Opp':<8}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['scenario']:<20} {result['strategy']:<15} "
              f"{result['confidence']:.0%}{'':>7} {result['volatility']:.0%}")
    
    print()
    print("💡 Key Insights:")
    print("• High volatility environments favor premium selling strategies")
    print("• Bull markets with strong trends favor directional strategies (LEAPS)")
    print("• Sideways markets favor income generation (Covered Calls)")
    print("• Extreme fear creates premium selling opportunities")
    print("• System adapts strategy selection to market regime automatically")
    print()

def demonstrate_erica_criteria():
    """Demonstrate how Erica's specific criteria influence decisions"""
    
    print("👩‍🏫 ERICA'S DECISION CRITERIA DEMONSTRATION")
    print("=" * 50)
    print("How Erica's specific rules influence strategy selection")
    print("=" * 50)
    print()
    
    criteria_examples = [
        {
            "rule": "Covered Calls: High IV + 30-45 DTE + 0.30 Delta",
            "scenario": "High IV environment with AAPL",
            "explanation": "System detects high IV rank (65%) and recommends covered calls with appropriate DTE and delta targets"
        },
        {
            "rule": "Credit Spreads: Bullish bias + High IV + 0.15-0.20 Delta",
            "scenario": "Bull market with elevated volatility",
            "explanation": "Bullish market factors (75%) + high volatility (85%) = high confidence credit spread recommendation"
        },
        {
            "rule": "LEAPS: Strong trend + 12+ months + 0.70-0.80 Delta",
            "scenario": "Strong bull market with low volatility",
            "explanation": "Strong bullish trend + low option prices = LEAPS opportunity with high confidence"
        },
        {
            "rule": "Premium Selling: IV Rank > 70% + Systematic approach",
            "scenario": "Extreme volatility with fear",
            "explanation": "Extreme volatility (95% IV rank) triggers systematic premium selling across strategies"
        }
    ]
    
    for i, example in enumerate(criteria_examples, 1):
        print(f"{i}. {example['rule']}")
        print(f"   Scenario: {example['scenario']}")
        print(f"   System Logic: {example['explanation']}")
        print()
    
    print("🎯 Erica's Risk Management Integration:")
    print("• Never risk more than 2% of account per trade")
    print("• Take profits at 50% of maximum gain")
    print("• Close positions at 21 DTE if not profitable")
    print("• Systematic approach beats emotional decisions")
    print("• Position sizing adjusts based on market volatility")
    print()

def main():
    """Run the complete demonstration"""
    
    print("🚀 ENHANCED AI-POWERED TRADING SYSTEM")
    print("Multi-Factor Analysis & Intelligent Strategy Selection")
    print("Based on Erica's Millionaire Mentorship Framework")
    print("=" * 70)
    print()
    
    # Run demonstrations
    demonstrate_intelligent_decision_making()
    demonstrate_strategy_comparison()
    demonstrate_erica_criteria()
    
    print("🎊 SYSTEM CAPABILITIES DEMONSTRATED:")
    print("✅ Multi-factor market analysis (VIX, sentiment, breadth, etc.)")
    print("✅ Stock-specific factor analysis (technicals, news, options flow)")
    print("✅ Intelligent decision tree with confidence scoring")
    print("✅ Erica's specific criteria and risk management rules")
    print("✅ Market regime detection and strategy adaptation")
    print("✅ Detailed reasoning and factor transparency")
    print("✅ Real-time monitoring and alert capabilities")
    print()
    
    print("🎯 NEXT STEPS:")
    print("1. Test with live data: python daily_outline.py --analysis-mode full")
    print("2. Launch desktop app: python launch_app.py")
    print("3. Monitor real-time recommendations throughout trading day")
    print("4. Validate strategy selections with paper trading")
    print("5. Customize decision tree weights based on performance")
    print()
    
    print("💡 The system now thinks like Erica when selecting strategies,")
    print("   considering all the market factors she discusses in her content!")

if __name__ == "__main__":
    main()
