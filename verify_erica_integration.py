"""
Verification Script for <PERSON>'s Methodology Integration
Tests all components to ensure proper implementation and display

This script verifies:
1. <PERSON>'s strategy engine integration
2. Best Strategies tab functionality
3. Strategy selection logic with ticker overrides
4. UI display of <PERSON>'s exact parameters
5. Roll management and risk controls

Date: August 18, 2025
"""

import sys
import traceback
from datetime import datetime

def test_erica_strategy_engine():
    """Test <PERSON>'s strategy engine implementation"""
    print("🧪 Testing Erica's Strategy Engine...")
    
    try:
        from erica_strategy_engine import (
            EricaStrategyEngine, EricaSignals, MarketBias, 
            EricaStrategy, create_erica_engine
        )
        
        # Create engine
        engine = create_erica_engine()
        print("✅ Erica strategy engine created successfully")
        
        # Test AAPL fast money preference
        aapl_signals = EricaSignals(
            spot_price=175.50,
            earnings_days_away=45,
            iv_rank=25.0,
            iv_percentile=28.0,
            expected_move=None,
            atr=3.50,
            support_level=170.0,
            resistance_level=180.0
        )
        
        aapl_setups = engine.analyze_setup("AAPL", aapl_signals, MarketBias.NEUTRAL)
        
        if aapl_setups:
            top_setup = aapl_setups[0]
            print(f"✅ AAPL analysis: {top_setup.strategy.value} (Delta: {top_setup.target_delta:.2f})")
            
            # Check for fast money preference
            fast_money_found = any(s.strategy == EricaStrategy.FAST_MONEY_CC for s in aapl_setups)
            if fast_money_found:
                print("✅ AAPL fast money preference detected")
            else:
                print("⚠️ AAPL fast money preference not found")
        else:
            print("❌ No AAPL setups generated")
        
        # Test NVDA conservative delta
        nvda_signals = EricaSignals(
            spot_price=450.00,
            earnings_days_away=8,
            iv_rank=75.0,
            iv_percentile=78.0,
            expected_move=35.0,
            atr=15.0,
            support_level=430.0,
            resistance_level=470.0
        )
        
        nvda_setups = engine.analyze_setup("NVDA", nvda_signals, MarketBias.MILDLY_BULLISH)
        
        if nvda_setups:
            top_setup = nvda_setups[0]
            print(f"✅ NVDA analysis: {top_setup.strategy.value} (Delta: {top_setup.target_delta:.2f})")
            
            # Check for conservative delta (should be ≤0.15 for earnings)
            if top_setup.target_delta <= 0.15:
                print("✅ NVDA conservative delta override applied")
            else:
                print(f"⚠️ NVDA delta {top_setup.target_delta:.2f} may not be conservative enough")
        else:
            print("❌ No NVDA setups generated")
        
        # Test roll management
        print("\n🔄 Testing Roll Management...")
        sample_setup = aapl_setups[0] if aapl_setups else None
        if sample_setup:
            # Simulate conditions requiring roll
            current_signals = EricaSignals(
                spot_price=182.0,  # Above strike
                earnings_days_away=30,
                iv_rank=45.0,
                iv_percentile=48.0,
                expected_move=None,
                atr=3.50,
                support_level=178.0,
                resistance_level=185.0
            )
            
            roll_conditions = engine.check_roll_conditions(sample_setup, current_signals)
            print(f"✅ Roll analysis: Should roll = {roll_conditions['should_roll']}")
            if roll_conditions['should_roll']:
                print(f"   Reason: {roll_conditions['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erica strategy engine test failed: {e}")
        traceback.print_exc()
        return False

def test_intelligent_strategy_integration():
    """Test integration with intelligent strategy engine"""
    print("\n🧪 Testing Intelligent Strategy Engine Integration...")
    
    try:
        from intelligent_strategy_engine import IntelligentStrategyEngine
        from market_analysis_engine import MarketAnalysisEngine
        
        # Create engines
        strategy_engine = IntelligentStrategyEngine("demo_key")
        print("✅ Intelligent strategy engine created with Erica integration")
        
        # Check if Erica engine is properly initialized
        if hasattr(strategy_engine, 'erica_engine'):
            print("✅ Erica engine properly integrated")
        else:
            print("❌ Erica engine not found in intelligent strategy engine")
            return False
        
        # Test strategy generation
        market_report, daily_strategies = strategy_engine.generate_daily_recommendations(["AAPL", "NVDA"])
        
        if daily_strategies:
            print(f"✅ Generated {len(daily_strategies)} strategy recommendations")
            
            for strategy in daily_strategies:
                print(f"   {strategy.symbol}: {strategy.recommended_strategy.value} (Confidence: {strategy.confidence:.0%})")
        else:
            print("❌ No daily strategies generated")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Intelligent strategy integration test failed: {e}")
        traceback.print_exc()
        return False

def test_best_strategy_dashboard():
    """Test best strategy dashboard with Erica's methodology"""
    print("\n🧪 Testing Best Strategy Dashboard...")
    
    try:
        from best_strategy_dashboard import BestStrategyDashboard, create_best_strategy_dashboard
        import tkinter as tk
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create dashboard
        dashboard = create_best_strategy_dashboard(root, "demo_key", ["AAPL", "NVDA"])
        print("✅ Best strategy dashboard created successfully")
        
        # Check if Erica engine is integrated
        if hasattr(dashboard, 'erica_engine'):
            print("✅ Erica engine integrated in dashboard")
        else:
            print("⚠️ Erica engine not directly accessible in dashboard")
        
        # Test refresh functionality
        try:
            dashboard.refresh_all_strategies()
            print("✅ Dashboard refresh functionality works")
        except Exception as e:
            print(f"⚠️ Dashboard refresh issue: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Best strategy dashboard test failed: {e}")
        traceback.print_exc()
        return False

def test_strategy_criteria_display():
    """Test strategy criteria display with Erica's criteria"""
    print("\n🧪 Testing Strategy Criteria Display...")
    
    try:
        from strategy_criteria_display import StrategyCriteriaAnalyzer, StrategyCriteriaAnalysis
        from strategy_decision_tree import StrategyRecommendation, StrategyConfidence
        from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime, SentimentLevel
        from daily_outline import StrategyType
        
        # Create analyzer
        analyzer = StrategyCriteriaAnalyzer()
        print("✅ Strategy criteria analyzer created")
        
        # Check if Erica framework is integrated
        if hasattr(analyzer, 'erica_framework'):
            print("✅ Erica decision framework integrated")
        else:
            print("❌ Erica decision framework not found")
            return False
        
        # Create mock data for testing
        mock_strategy_rec = StrategyRecommendation(
            symbol="AAPL",
            primary_strategy=StrategyType.COVERED_CALL,
            confidence=0.85,
            confidence_level=StrategyConfidence.HIGH,
            market_environment_score=0.8,
            stock_specific_score=0.85,
            erica_criteria_score=0.9,
            risk_adjusted_score=0.75,
            key_supporting_factors=["High IV rank", "Strong technical setup"],
            key_risk_factors=["Market volatility"],
            alternative_strategies=[],
            recommended_dte=(21, 45),
            recommended_delta=(0.20, 0.30),
            position_size_multiplier=1.0,
            invalidation_triggers=[],
            upgrade_triggers=[]
        )
        
        # Mock market factors
        mock_market_factors = MarketFactors(
            vix_level=20.0,
            vix_percentile=45.0,
            put_call_ratio=0.85,
            market_breadth=1.2,
            volatility_regime=VolatilityRegime.NORMAL_VOL,
            iv_rank_spy=0.5,
            iv_term_structure="contango",
            market_regime=MarketRegime.BULL_MARKET,
            trend_strength=0.7,
            regime_confidence=0.8,
            fear_greed_index=65.0,
            sentiment_level=SentimentLevel.GREED,
            social_sentiment=0.3,
            spy_technical_score=0.75,
            sector_rotation_signal="growth",
            relative_strength_leaders=["Technology", "Healthcare"],
            earnings_season_intensity=0.4,
            fed_meeting_proximity=15,
            major_events_this_week=["FOMC Minutes"],
            bullish_factors=0.7,
            bearish_factors=0.3,
            volatility_factors=0.5,
            uncertainty_factors=0.2
        )
        
        # Mock stock factors
        mock_stock_factors = StockSpecificFactors(
            symbol="AAPL",
            news_sentiment_score=0.2,
            earnings_days_away=35,
            earnings_move_estimate=0.08,
            recent_analyst_changes=[],
            relative_strength_vs_spy=0.15,
            technical_confluence_score=0.75,
            support_resistance_clarity=0.8,
            trend_alignment="up",
            unusual_options_activity=False,
            iv_rank=45.0,
            iv_percentile=48.0,
            iv_vs_hv_ratio=1.2,
            options_flow_sentiment="neutral",
            sector_performance=0.05,
            sector_rotation_impact="positive",
            wheel_suitability_score=0.7,
            covered_call_attractiveness=0.8,
            credit_spread_opportunity=0.6,
            leaps_opportunity=0.75
        )
        
        # Analyze criteria
        analysis = analyzer.analyze_strategy_criteria(
            mock_strategy_rec, mock_market_factors, mock_stock_factors
        )
        
        print(f"✅ Criteria analysis completed: {len(analysis.criteria_items)} total criteria")
        print(f"   Market criteria: {len(analysis.market_criteria)}")
        print(f"   Stock criteria: {len(analysis.stock_criteria)}")
        print(f"   Erica criteria: {len(analysis.erica_criteria)}")
        print(f"   Risk criteria: {len(analysis.risk_criteria)}")
        print(f"   Overall score: {analysis.overall_score:.0%}")
        
        # Check if Erica criteria are present
        if analysis.erica_criteria:
            print("✅ Erica's criteria properly analyzed")
            for criteria in analysis.erica_criteria[:3]:  # Show first 3
                print(f"   • {criteria.name}: {criteria.status.value}")
        else:
            print("❌ No Erica criteria found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy criteria display test failed: {e}")
        traceback.print_exc()
        return False

def test_parameter_compliance():
    """Test compliance with Erica's exact parameters"""
    print("\n🧪 Testing Parameter Compliance...")
    
    try:
        from erica_strategy_engine import EricaParameters
        
        params = EricaParameters()
        print("✅ Erica parameters loaded")
        
        # Check covered call parameters
        baseline_cc = params.COVERED_CALLS["baseline"]
        print(f"✅ Baseline CC - Delta: {baseline_cc['targetDelta']}, DTE: {baseline_cc['dte']}")
        
        earnings_cc = params.COVERED_CALLS["earnings"]
        print(f"✅ Earnings CC - Delta: {earnings_cc['targetDelta']}, DTE: {earnings_cc['dte']}")
        
        # Check ticker overrides
        print("\n📋 Ticker-Specific Overrides:")
        for ticker, overrides in params.TICKER_OVERRIDES.items():
            print(f"   {ticker}: {overrides}")
        
        # Verify specific overrides
        if params.TICKER_OVERRIDES["NVDA"]["earningsDeltaMax"] == 0.15:
            print("✅ NVDA conservative delta override (0.15) confirmed")
        else:
            print("❌ NVDA delta override incorrect")
            return False
        
        if params.TICKER_OVERRIDES["AAPL"].get("fastMoneyPreferred", False):
            print("✅ AAPL fast money preference confirmed")
        else:
            print("❌ AAPL fast money preference not set")
            return False
        
        # Check roll parameters
        roll_params = params.ROLLS
        print(f"✅ Roll triggers - Delta: {roll_params['deltaBreach']}, Time: {roll_params['timeFloorDTE']} DTE")
        
        return True
        
    except Exception as e:
        print(f"❌ Parameter compliance test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all verification tests"""
    print("🚀 ERICA'S METHODOLOGY INTEGRATION VERIFICATION")
    print("=" * 60)
    print(f"Test Date: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
    print()
    
    tests = [
        ("Erica Strategy Engine", test_erica_strategy_engine),
        ("Intelligent Strategy Integration", test_intelligent_strategy_integration),
        ("Best Strategy Dashboard", test_best_strategy_dashboard),
        ("Strategy Criteria Display", test_strategy_criteria_display),
        ("Parameter Compliance", test_parameter_compliance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.0f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Erica's methodology is properly integrated and functional!")
    else:
        print(f"\n⚠️ {total-passed} tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
