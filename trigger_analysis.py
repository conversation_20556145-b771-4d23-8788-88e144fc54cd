#!/usr/bin/env python3
"""
Manual analysis trigger for the Erica trading system
This script manually triggers the analysis and updates the GUI
"""

import tkinter as tk
from tkinter import messagebox
import threading
import time

def find_running_app():
    """Find the running desktop app instance"""
    try:
        import psutil
        import sys
        
        # Look for python processes running desktop_app.py
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('desktop_app.py' in cmd for cmd in proc.info['cmdline']):
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        return None
    except Exception:
        return None

def trigger_analysis_via_gui():
    """Create a simple GUI to trigger analysis"""
    
    def run_analysis():
        """Run the analysis in a separate thread"""
        try:
            status_label.config(text="🔄 Running analysis...")
            root.update()
            
            # Import and run the analysis
            from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
            from daily_recommendations import DailyRecommendationGenerator
            from intelligent_strategy_engine import IntelligentStrategyEngine
            from market_analysis_engine import MarketAnalysisEngine
            
            api_key = resolve_fmp_key(None)
            if not api_key:
                status_label.config(text="❌ No API key found")
                return
            
            symbols = ["AAPL", "NVDA", "AMD", "GOOGL", "AMZN"]
            
            # Step 1: Fetch market data
            status_label.config(text="📊 Fetching market data...")
            root.update()
            
            market_data = {}
            historical_data = {}
            
            for symbol in symbols:
                quote = fmp_quote(symbol, api_key)
                if quote:
                    market_data[symbol] = {"quote": quote}
                
                hist = fmp_historical_daily(symbol, api_key, limit=60)
                if hist:
                    historical_data[symbol] = hist
            
            # Step 2: Generate traditional recommendations
            status_label.config(text="🎯 Generating recommendations...")
            root.update()
            
            generator = DailyRecommendationGenerator(100000, symbols)
            daily_report = generator.generate_daily_report(market_data, historical_data)
            formatted_report = generator.format_daily_report(daily_report)
            
            # Step 3: Generate enhanced recommendations
            status_label.config(text="🧠 Running enhanced analysis...")
            root.update()
            
            intelligent_engine = IntelligentStrategyEngine(api_key)
            market_report, strategy_recommendations = intelligent_engine.generate_daily_recommendations(symbols)
            
            # Step 4: Display results
            status_label.config(text="✅ Analysis complete!")
            
            # Show summary
            summary = f"""
📊 ANALYSIS RESULTS:

Market Data: {len(market_data)} symbols
Traditional Recommendations: {len(daily_report.stock_recommendations)} stocks
Enhanced Strategies: {len(strategy_recommendations)} strategies

Enhanced Strategy Summary:
"""
            for rec in strategy_recommendations:
                summary += f"• {rec.symbol}: {rec.recommended_strategy.value} ({rec.confidence:.0%} confidence)\n"
            
            summary += f"\nMarket Environment: {daily_report.market_environment.market_outlook}"
            summary += f"\nVolatility Regime: {daily_report.market_environment.volatility_regime}"
            
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, summary)
            
            # Save results for manual inspection
            with open("analysis_results.txt", "w") as f:
                f.write("ERICA TRADING SYSTEM ANALYSIS RESULTS\n")
                f.write("=" * 50 + "\n\n")
                f.write(summary)
                f.write("\n\nFull Traditional Report:\n")
                f.write(formatted_report)
            
            status_label.config(text="✅ Results saved to analysis_results.txt")
            
        except Exception as e:
            status_label.config(text=f"❌ Error: {str(e)}")
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, f"Error during analysis:\n{str(e)}")
    
    def run_analysis_thread():
        """Run analysis in background thread"""
        threading.Thread(target=run_analysis, daemon=True).start()
    
    # Create GUI
    root = tk.Tk()
    root.title("Erica Trading System - Manual Analysis Trigger")
    root.geometry("600x500")
    
    # Header
    header_frame = tk.Frame(root, bg='navy')
    header_frame.pack(fill=tk.X, padx=5, pady=5)
    
    title_label = tk.Label(header_frame, text="🚀 ERICA TRADING SYSTEM", 
                          font=('Arial', 16, 'bold'), fg='white', bg='navy')
    title_label.pack(pady=10)
    
    subtitle_label = tk.Label(header_frame, text="Manual Analysis Trigger", 
                             font=('Arial', 12), fg='lightblue', bg='navy')
    subtitle_label.pack()
    
    # Status
    status_frame = tk.Frame(root)
    status_frame.pack(fill=tk.X, padx=10, pady=5)
    
    # Check if app is running
    app_pid = find_running_app()
    if app_pid:
        app_status = f"✅ Desktop app running (PID: {app_pid})"
    else:
        app_status = "⚠️ Desktop app not detected"
    
    tk.Label(status_frame, text=f"App Status: {app_status}", font=('Arial', 10)).pack(anchor=tk.W)
    
    status_label = tk.Label(status_frame, text="Ready to run analysis", 
                           font=('Arial', 10, 'bold'), fg='green')
    status_label.pack(anchor=tk.W)
    
    # Controls
    control_frame = tk.Frame(root)
    control_frame.pack(fill=tk.X, padx=10, pady=10)
    
    analyze_btn = tk.Button(control_frame, text="🔄 Run Analysis", 
                           command=run_analysis_thread, font=('Arial', 12, 'bold'),
                           bg='green', fg='white', padx=20, pady=10)
    analyze_btn.pack(side=tk.LEFT, padx=5)
    
    refresh_btn = tk.Button(control_frame, text="🔄 Refresh Status", 
                           command=lambda: status_label.config(text="Ready to run analysis"),
                           font=('Arial', 10))
    refresh_btn.pack(side=tk.LEFT, padx=5)
    
    # Results
    result_frame = tk.LabelFrame(root, text="Analysis Results", font=('Arial', 10, 'bold'))
    result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    result_text = tk.Text(result_frame, wrap=tk.WORD, font=('Courier', 9))
    scrollbar = tk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_text.yview)
    result_text.configure(yscrollcommand=scrollbar.set)
    
    result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # Instructions
    instructions = """
🎯 INSTRUCTIONS:

1. Click "Run Analysis" to manually trigger the trading analysis
2. The analysis will fetch real-time market data and generate recommendations
3. Results will be displayed here and saved to analysis_results.txt
4. This can help diagnose why the main app tabs might be empty

Note: This runs the same analysis pipeline as the main desktop app.
"""
    
    result_text.insert(tk.END, instructions)
    
    root.mainloop()

if __name__ == "__main__":
    trigger_analysis_via_gui()
