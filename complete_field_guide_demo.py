"""
Complete Field Guide Implementation Demo
Demonstrates all strategies from <PERSON>'s field guide with live market data

This demo shows:
1. Live market data integration
2. All field guide strategies implemented
3. Ticker-specific guardrails for AMD/NVDA/GOOGL/AAPL/AMZN
4. Real-time strategy selection
5. Concrete setup templates and management rules

Usage: python complete_field_guide_demo.py
"""

import os
import sys
from datetime import datetime
from typing import Dict, List
import argparse

from field_guide_strategy_selector import FieldGuideStrategySelector, FieldGuideStrategy, create_field_guide_selector
from live_market_data_integration import LiveMarketDataProvider, create_live_data_provider
from daily_outline import resolve_fmp_key

def print_live_market_data(live_data):
    """Print live market data in readable format"""
    print(f"\n📊 LIVE MARKET DATA - {live_data.symbol}")
    print("=" * 60)
    print(f"Current Price: ${live_data.current_price:.2f}")
    print(f"Change: {live_data.change_percent:+.2%}")
    print(f"Volume: {live_data.volume:,} (Avg: {live_data.avg_volume:,})")
    
    print(f"\n📈 OPTIONS METRICS:")
    print(f"IV Rank: {live_data.iv_rank:.0%}")
    print(f"IV 30-day: {live_data.iv_30:.1%}")
    print(f"HV 30-day: {live_data.hv_30:.1%}")
    
    print(f"\n🎯 TECHNICAL LEVELS:")
    print(f"Support: ${live_data.support_level:.2f}" if live_data.support_level else "Support: N/A")
    print(f"Resistance: ${live_data.resistance_level:.2f}" if live_data.resistance_level else "Resistance: N/A")
    print(f"ATR (14): ${live_data.atr_14:.2f}")
    
    print(f"\n📅 EARNINGS INFO:")
    if live_data.earnings_date:
        print(f"Next Earnings: {live_data.earnings_date.strftime('%Y-%m-%d')}")
        print(f"Days Away: {live_data.earnings_days_away}")
        if live_data.expected_move:
            print(f"Expected Move: ${live_data.expected_move:.2f} ({live_data.expected_move/live_data.current_price:.1%})")
    else:
        print("Earnings: No upcoming earnings found")
    
    print(f"\n🔄 RELATIVE METRICS:")
    print(f"Beta: {live_data.beta:.2f}")
    print(f"Relative Strength vs SPY: {live_data.relative_strength_spy:+.2%}")

def print_strategy_setup(setup):
    """Print complete strategy setup"""
    print(f"\n🎯 OPTIMAL STRATEGY: {setup.strategy.value.upper().replace('_', ' ')}")
    print(f"Confidence: {setup.confidence:.0%}")
    print("=" * 80)
    
    print(f"\n📋 STRATEGY SETUP:")
    print(f"Primary Action: {setup.primary_action}")
    if setup.secondary_action:
        print(f"Secondary Action: {setup.secondary_action}")
    
    if setup.strike_1:
        print(f"Strike 1: ${setup.strike_1:.2f}")
    if setup.strike_2:
        print(f"Strike 2: ${setup.strike_2:.2f}")
    
    print(f"DTE: {setup.dte} days")
    print(f"Delta Target: {setup.delta_target:.2f}")
    
    print(f"\n💰 RISK/REWARD:")
    print(f"Max Risk: ${setup.max_risk:.2f}")
    print(f"Max Profit: ${setup.max_profit:.2f}")
    print(f"Breakeven: ${setup.breakeven:.2f}")
    
    if setup.alarm_price:
        print(f"Alarm Price: ${setup.alarm_price:.2f}")
    
    print(f"\n🎯 MANAGEMENT RULES:")
    print(f"Profit Target: {setup.profit_target_pct:.0%} of max profit")
    print(f"Time Exit: {setup.time_exit_dte} DTE")
    print(f"Roll Trigger: {setup.roll_trigger}")
    
    print(f"\n💡 WHY THIS STRATEGY:")
    print(f"{setup.why_selected}")
    
    print(f"\n🔑 KEY FACTORS:")
    for i, factor in enumerate(setup.key_factors, 1):
        print(f"  {i}. {factor}")
    
    print(f"\n⚠️  RISK FACTORS:")
    for i, risk in enumerate(setup.risk_factors, 1):
        print(f"  {i}. {risk}")
    
    print(f"\n📝 MANAGEMENT NOTES:")
    print(f"{setup.management_notes}")

def demonstrate_field_guide_strategies(no_pause: bool = False):
    """Demonstrate all field guide strategies"""
    
    print("🚀 COMPLETE FIELD GUIDE IMPLEMENTATION DEMO")
    print("Real-time Strategy Selection with Live Market Data")
    print("=" * 80)
    
    # Get API key
    api_key = resolve_fmp_key(None)
    if not api_key:
        print("❌ Error: FMP API key not found. Please set FMP_API_KEY environment variable.")
        return
    
    # Create components
    try:
        data_provider = create_live_data_provider(api_key)
        strategy_selector = create_field_guide_selector(api_key)
        print("✅ Live data provider and strategy selector initialized")
    except Exception as e:
        print(f"❌ Error initializing components: {str(e)}")
        return
    
    # Target symbols with specific guardrails
    symbols = ["AAPL", "NVDA", "AMD", "GOOGL", "AMZN"]
    
    print(f"\n📈 Analyzing {len(symbols)} symbols with live market data...")
    
    for symbol in symbols:
        try:
            print(f"\n{'='*100}")
            print(f"🔍 ANALYZING {symbol} - LIVE DATA & STRATEGY SELECTION")
            print(f"{'='*100}")
            
            # Get live market data
            print(f"\n📡 Fetching live market data for {symbol}...")
            live_data = data_provider.get_live_data(symbol)
            
            # Display live market data
            print_live_market_data(live_data)
            
            # Select optimal strategy
            print(f"\n🧠 Selecting optimal strategy using field guide logic...")
            optimal_setup = strategy_selector.select_optimal_strategy(symbol)
            
            # Display strategy setup
            print_strategy_setup(optimal_setup)
            
            # Show ticker-specific guardrails
            guardrails = strategy_selector.ticker_guardrails.get(symbol)
            if guardrails:
                print(f"\n🛡️  {symbol}-SPECIFIC GUARDRAILS:")
                print(f"CSP Delta Range: {guardrails['csp_delta_range'][0]:.2f} - {guardrails['csp_delta_range'][1]:.2f}")
                print(f"CC DTE Range: {guardrails['cc_dte_range'][0]} - {guardrails['cc_dte_range'][1]} days")
                print(f"Alarm Offset: ${guardrails['alarm_offset']:.2f}")
                print(f"Earnings Buffer: {guardrails['earnings_buffer']:.1%}")
                print(f"Preferred Strategies: {', '.join(guardrails['preferred_strategies'])}")
            
            # Pause between analyses unless disabled
            if not no_pause:
                try:
                    input(f"\nPress Enter to continue to next symbol...")
                except EOFError:
                    pass
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {str(e)}")
            continue
    
    print(f"\n✅ Field Guide Demo completed successfully!")
    
    # Show strategy summary
    print(f"\n📊 FIELD GUIDE STRATEGY SUMMARY:")
    print("=" * 60)
    
    strategy_descriptions = {
        FieldGuideStrategy.CASH_SECURED_PUT: "Sell puts at strikes you want stock or are paid to wait",
        FieldGuideStrategy.COVERED_CALL: "Own shares, sell OTM calls for income (2-10 DTE cycles)",
        FieldGuideStrategy.PUT_CREDIT_SPREAD: "Neutral-bullish; sell put + buy protection",
        FieldGuideStrategy.BULLISH_SPREAD: "Defined risk bullish with conversion option",
        FieldGuideStrategy.BEAR_CALL_SPREAD: "Mean reversion near resistance with alarms",
        FieldGuideStrategy.WHEEL_STRATEGY: "Systematic CSP → CC → repeat cycle",
        FieldGuideStrategy.RICH_WOMANS_CC: "LEAPS + short calls for capital efficiency",
        FieldGuideStrategy.POOR_MANS_CC: "Cheap LEAPS + short calls minimal capital",
        FieldGuideStrategy.CALENDAR_SPREADS: "Recurring income from same long call",
        FieldGuideStrategy.LEAPS_STRATEGY: "Long-term bullish with leverage"
    }
    
    for strategy, description in strategy_descriptions.items():
        print(f"\n{strategy.value.upper().replace('_', ' ')}:")
        print(f"  {description}")
    
    print(f"\n🎯 KEY FIELD GUIDE PRINCIPLES:")
    print("• Use live market data for real-time decisions")
    print("• Apply ticker-specific guardrails (AMD/NVDA/GOOGL/AAPL/AMZN)")
    print("• Set alarms: $1 (low price), $2.50 (mid), $5 (high price)")
    print("• Profit targets: 50-70% of max profit")
    print("• Roll management: up/out on strength, down/in on weakness")
    print("• Time exits: 5 DTE if unclear")
    print("• Earnings adjustments: skip or reduce size near earnings")
    print("• IV rank thresholds: >60% for selling, <40% for buying")
    print("• Support/resistance awareness for strike selection")
    print("• Market condition adaptation (bullish/bearish/range-bound)")

def show_concrete_templates():
    """Show concrete setup templates from field guide"""
    
    print(f"\n📋 CONCRETE SETUP TEMPLATES:")
    print("=" * 60)
    
    print(f"\n1. CSP TEMPLATE:")
    print("   • Pick support (20-day low or VWAP band)")
    print("   • Strike just below support, Δ ≈ 0.15-0.30")
    print("   • DTE 7-14, target 50-70% max in 1-5 days")
    print("   • If Δ > 0.35 and trend weakens → roll out/down")
    
    print(f"\n2. COVERED CALL TEMPLATE:")
    print("   • On up day, sell 2-8 DTE, 20-30Δ")
    print("   • Strike ≥ recent swing high or 0.5-1.0 ATR above")
    print("   • If price > strike by >0.5 ATR → roll up/out")
    
    print(f"\n3. BEAR CALL SPREAD TEMPLATE:")
    print("   • Short call just above resistance")
    print("   • Buy long call +1-3 strikes higher")
    print("   • Alarm per price band ($1/$2.50/$5)")
    print("   • If triggered → buy 100 shares, sell long call")
    
    print(f"\n4. WHEEL TEMPLATE:")
    print("   • CSP below support → assignment → CC above resistance")
    print("   • Systematic approach, 50-70% profit targets")
    print("   • Avoid in downtrends (underwater shares)")
    
    print(f"\n5. RICH WOMAN'S CC TEMPLATE:")
    print("   • Buy LEAPS ≥365 DTE, Δ 0.50-0.75")
    print("   • Sell short calls on up days")
    print("   • Alarms $1-2 below short strike")
    print("   • Roll or convert if tested")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Complete Field Guide Implementation Demo")
    parser.add_argument("--no-pause", action="store_true", help="Run without waiting for Enter between symbols")
    args = parser.parse_args()

    demonstrate_field_guide_strategies(no_pause=args.no_pause)
    show_concrete_templates()
