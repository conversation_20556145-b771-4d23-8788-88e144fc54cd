"""
Enhanced Strategy Analysis Demo
Demonstrates the comprehensive strategy-based stock classification system

This demo shows how the enhanced system provides detailed analysis including:
1. Strategy classification with exact criteria matching
2. Detailed explanations and investment thesis
3. Key catalysts and supporting metrics
4. <PERSON>'s specific methodology integration
5. Comprehensive execution guidance

Usage: python enhanced_strategy_demo.py
"""

import os
import sys
from datetime import datetime
from enhanced_strategy_analyzer import EnhancedStrategyAnalyzer, AnalysisDepth, create_enhanced_analyzer
import argparse
from daily_outline import resolve_fmp_key

def print_detailed_analysis(analysis):
    """Print comprehensive analysis in a readable format"""
    
    print("=" * 100)
    print(f"COMPREHENSIVE STRATEGY ANALYSIS - {analysis.symbol}")
    print(f"Analysis Date: {analysis.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    # Primary Strategy Classification
    print(f"\n🎯 PRIMARY STRATEGY: {analysis.primary_strategy.value.upper().replace('_', ' ')}")
    print(f"Confidence Level: {analysis.strategy_confidence.value.upper().replace('_', ' ')} ({analysis.strategy_score:.1f}/100)")
    print(f"Strategy Score: {analysis.strategy_score:.1f}%")
    
    # Why This Strategy
    print(f"\n📋 WHY THIS STRATEGY:")
    print(f"{analysis.why_this_strategy}")
    
    # Criteria Analysis
    print(f"\n✅ CRITERIA ANALYSIS:")
    print(f"Summary: {analysis.criteria_summary}")
    print("\nDetailed Criteria Matching:")
    
    for i, criteria in enumerate(analysis.criteria_matches, 1):
        status = "✅ MET" if criteria.is_met else "❌ NOT MET"
        importance = criteria.importance.upper()
        print(f"  {i}. {criteria.criterion_name} [{importance}] - {status}")
        print(f"     Required: {criteria.required_value}")
        print(f"     Actual: {criteria.actual_value}")
        print(f"     Score: {criteria.score:.1%}")
        print(f"     Explanation: {criteria.explanation}")
        print()
    
    # Investment Thesis
    print(f"💡 INVESTMENT THESIS:")
    print(f"{analysis.investment_thesis}")
    
    # Key Catalysts
    if analysis.key_catalysts:
        print(f"\n🚀 KEY CATALYSTS:")
        for i, catalyst in enumerate(analysis.key_catalysts, 1):
            print(f"  {i}. {catalyst.catalyst_type.upper()}: {catalyst.description}")
            print(f"     Impact: {catalyst.impact_level.upper()} | Timeframe: {catalyst.timeframe.replace('_', ' ').title()}")
            print(f"     Probability: {catalyst.probability:.0%}")
            print(f"     Evidence: {', '.join(catalyst.supporting_evidence)}")
            print()
    
    # Erica's Specific Methodology
    print(f"🧠 ERICA'S METHODOLOGY:")
    print(f"Reasoning: {analysis.erica_reasoning}")
    
    if analysis.erica_trade_setup:
        setup = analysis.erica_trade_setup
        print(f"\nTrade Setup Details:")
        print(f"  Strategy: {setup.strategy.value if hasattr(setup.strategy, 'value') else setup.strategy}")
        print(f"  Target Delta: {setup.target_delta:.2f}")
        print(f"  Days to Expiration: {setup.dte}")
        if setup.short_strike:
            print(f"  Short Strike: ${setup.short_strike:.2f}")
        if setup.credit_received:
            print(f"  Credit Received: ${setup.credit_received:.2f}")
        print(f"  Max Profit: ${setup.max_profit:.2f}")
        print(f"  Profit Target: {setup.profit_target_pct:.0%}")
        print(f"  Management Notes: {setup.management_notes}")
    
    print(f"\nStock-Specific Rules:")
    for key, value in analysis.erica_specific_rules.items():
        print(f"  {key}: {value}")
    
    # Supporting Metrics
    print(f"\n📊 SUPPORTING METRICS:")
    
    print("Technical Indicators:")
    for key, value in analysis.supporting_metrics.technical_indicators.items():
        print(f"  {key}: {value}")
    
    print("\nOptions Metrics:")
    for key, value in analysis.supporting_metrics.options_metrics.items():
        print(f"  {key}: {value}")
    
    print("\nMarket Context:")
    for key, value in analysis.supporting_metrics.market_metrics.items():
        print(f"  {key}: {value}")
    
    # Risk Factors
    if analysis.risk_factors:
        print(f"\n⚠️  RISK FACTORS:")
        for i, risk in enumerate(analysis.risk_factors, 1):
            print(f"  {i}. {risk}")
    
    # Execution Details
    print(f"\n🎯 EXECUTION GUIDANCE:")
    
    print("Entry Criteria:")
    for i, criteria in enumerate(analysis.entry_criteria, 1):
        print(f"  {i}. {criteria}")
    
    print("\nExit Criteria:")
    for i, criteria in enumerate(analysis.exit_criteria, 1):
        print(f"  {i}. {criteria}")
    
    print(f"\nPosition Sizing:")
    sizing = analysis.position_sizing
    print(f"  Recommended Contracts: {sizing['recommended_contracts']}")
    print(f"  Final Multiplier: {sizing['final_multiplier']:.2f}")
    print(f"  Risk Considerations: {', '.join(sizing['risk_considerations'])}")
    
    # Timing Considerations
    if analysis.timing_considerations:
        print(f"\n⏰ TIMING CONSIDERATIONS:")
        for i, timing in enumerate(analysis.timing_considerations, 1):
            print(f"  {i}. {timing}")
    
    # Market Environment
    print(f"\n🌍 MARKET ENVIRONMENT:")
    print(f"{analysis.market_environment}")
    print(f"Sector Analysis: {analysis.sector_analysis}")
    print(f"Relative Strength: {analysis.relative_strength}")
    
    # Alternative Strategies
    if analysis.alternative_strategies:
        print(f"\n🔄 ALTERNATIVE STRATEGIES:")
        for i, (strategy, score, reason) in enumerate(analysis.alternative_strategies, 1):
            print(f"  {i}. {strategy.value.replace('_', ' ').title()} ({score:.1%}) - {reason}")
    
    # Financial Projections
    print(f"\n💰 ANALYST SENTIMENT: {analysis.analyst_sentiment}")
    
    print("\n" + "=" * 100)

def main(no_pause: bool = False):
    """Main demo function"""
    
    print("🚀 Enhanced Strategy Analysis System Demo")
    print("Based on Erica's Documented Investment Strategies")
    print("=" * 60)
    
    # Get API key
    api_key = resolve_fmp_key(None)
    if not api_key:
        print("❌ Error: FMP API key not found. Please set FMP_API_KEY environment variable.")
        return
    
    # Create enhanced analyzer
    try:
        analyzer = create_enhanced_analyzer(api_key)
        print("✅ Enhanced Strategy Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing analyzer: {str(e)}")
        return
    
    # Demo symbols
    demo_symbols = ["AAPL", "NVDA", "AMD"]
    
    print(f"\n📈 Analyzing {len(demo_symbols)} stocks with comprehensive strategy classification...")
    
    for symbol in demo_symbols:
        try:
            print(f"\n🔍 Analyzing {symbol}...")
            
            # Perform comprehensive analysis
            analysis = analyzer.analyze_stock_comprehensive(
                symbol=symbol,
                analysis_depth=AnalysisDepth.COMPREHENSIVE
            )
            
            # Display results
            print_detailed_analysis(analysis)
            
            # Pause between analyses unless disabled
            if not no_pause:
                try:
                    input(f"\nPress Enter to continue to next analysis...")
                except EOFError:
                    pass
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {str(e)}")
            continue
    
    print("\n✅ Demo completed successfully!")
    print("\nThe Enhanced Strategy Analysis System provides:")
    print("• Detailed strategy classification based on Erica's documented criteria")
    print("• Comprehensive explanations of why each stock fits its strategy")
    print("• Key catalysts driving the investment thesis")
    print("• Supporting metrics and technical analysis")
    print("• Specific execution guidance with entry/exit criteria")
    print("• Integration with Erica's exact methodology")
    print("• Risk assessment and position sizing recommendations")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Enhanced Strategy Analysis Demo")
    parser.add_argument("--no-pause", action="store_true", help="Run without waiting for Enter between analyses")
    args = parser.parse_args()

    main(no_pause=args.no_pause)
