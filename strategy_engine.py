"""
Strategy Analysis Engine for <PERSON>'s Trading System

This is the core engine that:
1. Integrates market data, technical analysis, and options data
2. Applies <PERSON>'s trading strategies systematically
3. Generates actionable investment recommendations
4. Provides risk assessment and position sizing
5. Creates daily action items for portfolio management

The engine follows <PERSON>'s systematic approach to options trading with
focus on high-probability trades and consistent income generation.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

from daily_outline import TradingSignal, StrategyType, MarketCondition, MarketAnalysis
from erica_strategies import EricaStrategies, OptionData, StrategyParameters
from technical_analysis import TechnicalAnalyzer, TechnicalIndicators

@dataclass
class PortfolioPosition:
    """Represents a current portfolio position"""
    symbol: str
    strategy: StrategyType
    entry_date: datetime
    entry_price: float
    quantity: int
    current_value: float
    unrealized_pnl: float
    days_held: int
    target_exit: Optional[float] = None
    stop_loss: Optional[float] = None

@dataclass
class DailyRecommendation:
    """Complete daily recommendation for a symbol"""
    symbol: str
    current_analysis: MarketAnalysis
    technical_indicators: TechnicalIndicators
    primary_signal: Optional[TradingSignal]
    alternative_signals: List[TradingSignal]
    risk_assessment: str
    action_items: List[str]
    market_outlook: str
    confidence_score: float

@dataclass
class PortfolioSummary:
    """Daily portfolio summary and recommendations"""
    total_value: float
    daily_pnl: float
    open_positions: List[PortfolioPosition]
    recommendations: List[DailyRecommendation]
    market_environment: str
    risk_level: str
    suggested_actions: List[str]

class StrategyAnalysisEngine:
    """Core engine for analyzing stocks and generating trading recommendations"""
    
    def __init__(self, account_size: float = 100000, risk_tolerance: str = "moderate"):
        self.account_size = account_size
        self.risk_tolerance = risk_tolerance
        self.erica_strategies = EricaStrategies()
        self.technical_analyzer = TechnicalAnalyzer()
        self.logger = logging.getLogger(__name__)
        
        # Risk management parameters
        self.max_position_size = 0.1  # Max 10% per position
        self.max_portfolio_risk = 0.2  # Max 20% portfolio at risk
        
    def analyze_symbol(self, symbol: str, market_data: dict, 
                      historical_data: List[dict], 
                      options_data: Optional[List[dict]] = None) -> DailyRecommendation:
        """
        Perform comprehensive analysis for a single symbol
        
        Args:
            symbol: Stock symbol to analyze
            market_data: Current market data (quote, profile, etc.)
            historical_data: Historical price data
            options_data: Options chain data (if available)
            
        Returns:
            DailyRecommendation with complete analysis and signals
        """
        
        # Extract current market analysis
        current_analysis = self._create_market_analysis(symbol, market_data, historical_data)
        
        # Perform technical analysis
        technical_indicators = self.technical_analyzer.analyze(
            symbol, historical_data, current_analysis.current_price
        )
        
        # Convert options data if available
        options_chain = self._convert_options_data(options_data) if options_data else []
        
        # Generate trading signals using Erica's strategies
        signals = self._generate_trading_signals(current_analysis, technical_indicators, options_chain)
        
        # Select primary signal and alternatives
        primary_signal = self._select_primary_signal(signals)
        alternative_signals = [s for s in signals if s != primary_signal]
        
        # Assess risk
        risk_assessment = self._assess_risk(current_analysis, technical_indicators, primary_signal)
        
        # Generate action items
        action_items = self._generate_action_items(current_analysis, technical_indicators, primary_signal)
        
        # Create market outlook
        market_outlook = self._create_market_outlook(current_analysis, technical_indicators)
        
        # Calculate overall confidence
        confidence_score = self._calculate_confidence_score(technical_indicators, primary_signal)
        
        return DailyRecommendation(
            symbol=symbol,
            current_analysis=current_analysis,
            technical_indicators=technical_indicators,
            primary_signal=primary_signal,
            alternative_signals=alternative_signals,
            risk_assessment=risk_assessment,
            action_items=action_items,
            market_outlook=market_outlook,
            confidence_score=confidence_score
        )
    
    def _create_market_analysis(self, symbol: str, market_data: dict, 
                               historical_data: List[dict]) -> MarketAnalysis:
        """Create MarketAnalysis object from market data"""
        
        quote = market_data.get('quote', {})
        current_price = float(quote.get('price', 0))
        
        # Calculate basic volatility from historical data
        volatility = 0.0
        if len(historical_data) >= 20:
            closes = [float(bar['close']) for bar in historical_data[-20:]]
            returns = [(closes[i] / closes[i-1] - 1) for i in range(1, len(closes))]
            if returns:
                import math
                variance = sum(r**2 for r in returns) / len(returns)
                volatility = math.sqrt(variance * 252)  # Annualized
        
        # Determine basic trend
        trend = "NEUTRAL"
        if len(historical_data) >= 10:
            recent_closes = [float(bar['close']) for bar in historical_data[-10:]]
            if recent_closes[-1] > recent_closes[0] * 1.02:
                trend = "UPTREND"
            elif recent_closes[-1] < recent_closes[0] * 0.98:
                trend = "DOWNTREND"
        
        return MarketAnalysis(
            symbol=symbol,
            current_price=current_price,
            trend=trend,
            volatility=volatility,
            rsi=None,  # Will be filled by technical analysis
            ma_20=None,
            ma_50=None,
            support_level=None,
            resistance_level=None,
            market_condition=MarketCondition.NEUTRAL
        )
    
    def _convert_options_data(self, options_data: List[dict]) -> List[OptionData]:
        """Convert raw options data to OptionData objects"""
        options_chain = []
        
        for option in options_data:
            try:
                option_data = OptionData(
                    symbol=option.get('symbol', ''),
                    strike=float(option.get('strike', 0)),
                    expiration=option.get('expiration', ''),
                    option_type=option.get('type', 'call').lower(),
                    bid=float(option.get('bid', 0)),
                    ask=float(option.get('ask', 0)),
                    volume=int(option.get('volume', 0)),
                    open_interest=int(option.get('openInterest', 0)),
                    implied_volatility=float(option.get('impliedVolatility', 0)),
                    delta=option.get('delta'),
                    theta=option.get('theta'),
                    gamma=option.get('gamma')
                )
                options_chain.append(option_data)
            except (ValueError, TypeError) as e:
                self.logger.warning(f"Error converting option data: {e}")
                continue
        
        return options_chain
    
    def _generate_trading_signals(self, market_analysis: MarketAnalysis,
                                 technical_indicators: TechnicalIndicators,
                                 options_chain: List[OptionData]) -> List[TradingSignal]:
        """Generate all possible trading signals using Erica's strategies"""
        
        signals = []
        
        # Update market analysis with technical indicators
        market_analysis.rsi = technical_indicators.rsi
        market_analysis.ma_20 = technical_indicators.ma_20
        market_analysis.ma_50 = technical_indicators.ma_50
        market_analysis.support_level = technical_indicators.support_level
        market_analysis.resistance_level = technical_indicators.resistance_level
        market_analysis.market_condition = technical_indicators.market_condition
        
        # Covered Call Analysis
        covered_call_signal = self.erica_strategies.analyze_covered_call_opportunity(
            market_analysis, options_chain, self.account_size
        )
        if covered_call_signal:
            signals.append(covered_call_signal)
        
        # Credit Spread Analysis
        credit_spread_signal = self.erica_strategies.analyze_credit_spread_opportunity(
            market_analysis, options_chain, self.account_size
        )
        if credit_spread_signal:
            signals.append(credit_spread_signal)
        
        # LEAPS Analysis
        leaps_signal = self.erica_strategies.analyze_leaps_opportunity(
            market_analysis, options_chain, self.account_size
        )
        if leaps_signal:
            signals.append(leaps_signal)
        
        # PMCC Analysis (LEAPS + short call)
        pmcc_signal = self.erica_strategies.analyze_pmcc_opportunity(
            market_analysis, options_chain, self.account_size
        )
        if pmcc_signal:
            signals.append(pmcc_signal)

        # Premium Selling Analysis
        premium_signal = self.erica_strategies.analyze_premium_selling_opportunity(
            market_analysis, options_chain
        )
        if premium_signal:
            signals.append(premium_signal)
        
        return signals
    
    def _select_primary_signal(self, signals: List[TradingSignal]) -> Optional[TradingSignal]:
        """Select the best signal based on confidence and risk/reward"""
        if not signals:
            return None
        
        # Filter signals with actionable recommendations
        actionable_signals = [s for s in signals if s.action in ["BUY", "SELL"]]
        
        if not actionable_signals:
            return signals[0] if signals else None
        
        # Select signal with highest confidence * risk_reward_ratio
        def signal_score(signal):
            rr_ratio = signal.risk_reward_ratio or 1.0
            return signal.confidence * min(rr_ratio, 3.0)  # Cap RR ratio at 3
        
        return max(actionable_signals, key=signal_score)
    
    def _assess_risk(self, market_analysis: MarketAnalysis, 
                    technical_indicators: TechnicalIndicators,
                    primary_signal: Optional[TradingSignal]) -> str:
        """Assess overall risk for the symbol"""
        
        risk_factors = []
        risk_score = 0
        
        # Volatility risk
        if market_analysis.volatility > 0.4:
            risk_factors.append("High volatility")
            risk_score += 2
        elif market_analysis.volatility > 0.25:
            risk_factors.append("Elevated volatility")
            risk_score += 1
        
        # Technical risk
        if technical_indicators.rsi_signal == "OVERBOUGHT":
            risk_factors.append("Overbought conditions")
            risk_score += 1
        elif technical_indicators.rsi_signal == "OVERSOLD":
            risk_factors.append("Oversold conditions")
            risk_score += 1
        
        # Trend risk
        if technical_indicators.trend_strength in ["STRONG_DOWNTREND", "DOWNTREND"]:
            risk_factors.append("Negative trend")
            risk_score += 2
        
        # Strategy-specific risk
        if primary_signal and primary_signal.confidence < 0.6:
            risk_factors.append("Low strategy confidence")
            risk_score += 1
        
        # Determine overall risk level
        if risk_score >= 4:
            risk_level = "HIGH"
        elif risk_score >= 2:
            risk_level = "MODERATE"
        else:
            risk_level = "LOW"
        
        risk_summary = f"{risk_level} RISK"
        if risk_factors:
            risk_summary += f" - {', '.join(risk_factors)}"
        
        return risk_summary
    
    def _generate_action_items(self, market_analysis: MarketAnalysis,
                              technical_indicators: TechnicalIndicators,
                              primary_signal: Optional[TradingSignal]) -> List[str]:
        """Generate specific action items for the trader"""
        
        action_items = []
        
        # Primary signal action
        if primary_signal and primary_signal.action in ["BUY", "SELL"]:
            action_items.append(
                f"{primary_signal.action} {primary_signal.strategy.value} - {primary_signal.reasoning}"
            )
            
            if primary_signal.target_price:
                action_items.append(f"Target price: ${primary_signal.target_price:.2f}")
            
            if primary_signal.stop_loss:
                action_items.append(f"Stop loss: ${primary_signal.stop_loss:.2f}")
        
        # Technical analysis actions
        if technical_indicators.rsi_signal == "OVERSOLD":
            action_items.append("Monitor for potential bounce from oversold levels")
        elif technical_indicators.rsi_signal == "OVERBOUGHT":
            action_items.append("Watch for potential pullback from overbought levels")
        
        # Support/resistance actions
        if technical_indicators.support_level:
            action_items.append(f"Key support level: ${technical_indicators.support_level:.2f}")
        if technical_indicators.resistance_level:
            action_items.append(f"Key resistance level: ${technical_indicators.resistance_level:.2f}")
        
        # Volatility-based actions
        if technical_indicators.volatility_rank and technical_indicators.volatility_rank > 0.8:
            action_items.append("High volatility - consider premium selling strategies")
        elif technical_indicators.volatility_rank and technical_indicators.volatility_rank < 0.2:
            action_items.append("Low volatility - consider directional strategies")
        
        return action_items
    
    def _create_market_outlook(self, market_analysis: MarketAnalysis,
                              technical_indicators: TechnicalIndicators) -> str:
        """Create market outlook summary"""
        
        outlook_components = []
        
        # Trend component
        if technical_indicators.trend_strength in ["STRONG_UPTREND", "UPTREND"]:
            outlook_components.append("Bullish trend")
        elif technical_indicators.trend_strength in ["STRONG_DOWNTREND", "DOWNTREND"]:
            outlook_components.append("Bearish trend")
        else:
            outlook_components.append("Neutral trend")
        
        # Volatility component
        if technical_indicators.volatility_rank:
            if technical_indicators.volatility_rank > 0.7:
                outlook_components.append("high volatility environment")
            elif technical_indicators.volatility_rank < 0.3:
                outlook_components.append("low volatility environment")
            else:
                outlook_components.append("moderate volatility")
        
        # Technical component
        if technical_indicators.ma_signal == "BULLISH":
            outlook_components.append("positive technical setup")
        elif technical_indicators.ma_signal == "BEARISH":
            outlook_components.append("negative technical setup")
        
        return " with ".join(outlook_components).capitalize()
    
    def _calculate_confidence_score(self, technical_indicators: TechnicalIndicators,
                                   primary_signal: Optional[TradingSignal]) -> float:
        """Calculate overall confidence score for the recommendation"""
        
        confidence = 0.5  # Base confidence
        
        # Technical analysis confidence
        if technical_indicators.trend_strength in ["STRONG_UPTREND", "STRONG_DOWNTREND"]:
            confidence += 0.2
        elif technical_indicators.trend_strength in ["UPTREND", "DOWNTREND"]:
            confidence += 0.1
        
        # Moving average alignment
        if technical_indicators.ma_signal in ["BULLISH", "BEARISH"]:
            confidence += 0.1
        
        # Strategy confidence
        if primary_signal:
            confidence = (confidence + primary_signal.confidence) / 2
        
        # Volatility clarity
        if technical_indicators.volatility_rank:
            if technical_indicators.volatility_rank > 0.8 or technical_indicators.volatility_rank < 0.2:
                confidence += 0.1  # Clear high or low volatility
        
        return min(confidence, 1.0)
        # PMCC Analysis (LEAPS + short call)
        try:
            pmcc_signal = self.erica_strategies.analyze_pmcc_opportunity(
                market_analysis, options_chain, account_size
            )
            if pmcc_signal:
                scored.append({
                    "decision": StrategyDecision(name=pmcc_signal.strategy.value if hasattr(pmcc_signal.strategy, 'value') else 'pmcc', suitable=True, confidence=pmcc_signal.confidence, rationale=pmcc_signal.reasoning or "", details=pmcc_signal.details or {}),
                    "composite_score": pmcc_signal.confidence,
                    "relevant_catalysts": [],
                })
        except Exception:
            pass


