#!/usr/bin/env python3
"""
Crash diagnostic script for the Erica trading system
This script identifies what's causing the application to crash
"""

import sys
import traceback
import logging

# Set up logging to capture all errors
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crash_log.txt'),
        logging.StreamHandler(sys.stdout)
    ]
)

def test_basic_imports():
    """Test basic imports to identify import errors"""
    print("🔍 Testing basic imports...")
    
    try:
        import tkinter as tk
        print("   ✅ tkinter imported successfully")
    except Exception as e:
        print(f"   ❌ tkinter import failed: {e}")
        return False
    
    try:
        from daily_outline import resolve_fmp_key
        print("   ✅ daily_outline imported successfully")
    except Exception as e:
        print(f"   ❌ daily_outline import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        from desktop_app import TradingSystemGUI
        print("   ✅ desktop_app imported successfully")
    except Exception as e:
        print(f"   ❌ desktop_app import failed: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_gui_creation():
    """Test GUI creation step by step"""
    print("\n🔍 Testing GUI creation...")
    
    try:
        import tkinter as tk
        
        print("   Creating root window...")
        root = tk.Tk()
        root.withdraw()  # Hide window
        print("   ✅ Root window created")
        
        print("   Importing TradingSystemGUI...")
        from desktop_app import TradingSystemGUI
        print("   ✅ TradingSystemGUI imported")
        
        print("   Creating TradingSystemGUI instance...")
        app = TradingSystemGUI()
        print("   ✅ TradingSystemGUI instance created")
        
        print("   Testing basic attributes...")
        if hasattr(app, 'root'):
            print("   ✅ app.root exists")
        if hasattr(app, 'notebook'):
            print("   ✅ app.notebook exists")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"   ❌ GUI creation failed: {e}")
        traceback.print_exc()
        return False

def test_analysis_pipeline():
    """Test the analysis pipeline that might be causing crashes"""
    print("\n🔍 Testing analysis pipeline...")
    
    try:
        from daily_outline import resolve_fmp_key, fmp_quote
        
        print("   Testing API key resolution...")
        api_key = resolve_fmp_key(None)
        if api_key:
            print(f"   ✅ API key found: {api_key[:10]}...")
        else:
            print("   ⚠️ No API key found")
        
        print("   Testing simple API call...")
        quote = fmp_quote("AAPL", api_key)
        if quote:
            print(f"   ✅ API call successful: AAPL = ${quote.get('price', 'N/A')}")
        else:
            print("   ⚠️ API call returned no data")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Analysis pipeline test failed: {e}")
        traceback.print_exc()
        return False

def test_data_structures():
    """Test data structure creation that might be causing issues"""
    print("\n🔍 Testing data structures...")
    
    try:
        from market_analysis_engine import StockSpecificFactors
        
        print("   Testing StockSpecificFactors creation...")
        factors = StockSpecificFactors(
            symbol="AAPL",
            news_sentiment_score=0.0,
            earnings_days_away=None,
            earnings_move_estimate=0.08,
            recent_analyst_changes=[],
            relative_strength_vs_spy=0.0,
            technical_confluence_score=0.5,
            support_resistance_clarity=0.5,
            trend_alignment="neutral",
            unusual_options_activity=False,
            iv_rank=50.0,
            iv_percentile=50.0,
            iv_vs_hv_ratio=1.0,
            options_flow_sentiment="neutral",
            options_volume=1000,
            bid_ask_spread=0.05,
            sector_performance=0.0,
            sector_rotation_impact="neutral",
            wheel_suitability_score=0.5,
            covered_call_attractiveness=0.5,
            credit_spread_opportunity=0.5,
            leaps_opportunity=0.5
        )
        print("   ✅ StockSpecificFactors created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Data structure test failed: {e}")
        traceback.print_exc()
        return False

def test_minimal_app():
    """Test a minimal version of the app to isolate the crash"""
    print("\n🔍 Testing minimal app...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        print("   Creating minimal GUI...")
        root = tk.Tk()
        root.title("Minimal Test")
        root.geometry("400x300")
        
        # Create a simple interface
        label = tk.Label(root, text="Minimal Test - If you see this, basic GUI works")
        label.pack(pady=20)
        
        button = tk.Button(root, text="Test Button", command=lambda: print("Button clicked"))
        button.pack(pady=10)
        
        # Test notebook
        notebook = ttk.Notebook(root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        frame1 = ttk.Frame(notebook)
        notebook.add(frame1, text="Tab 1")
        
        tk.Label(frame1, text="This is tab 1").pack(pady=20)
        
        print("   ✅ Minimal GUI created successfully")
        print("   🎯 GUI should be visible now - check for window")
        
        # Don't start mainloop in test, just show it's working
        root.update()
        root.after(2000, root.destroy)  # Auto-close after 2 seconds
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Minimal app test failed: {e}")
        traceback.print_exc()
        return False

def run_crash_analysis():
    """Run comprehensive crash analysis"""
    print("🚨 CRASH DIAGNOSTIC FOR ERICA TRADING SYSTEM")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Data Structures", test_data_structures),
        ("Analysis Pipeline", test_analysis_pipeline),
        ("GUI Creation", test_gui_creation),
        ("Minimal App", test_minimal_app)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed completely: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("📊 CRASH DIAGNOSTIC RESULTS")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    failed_tests = [name for name, passed in results.items() if not passed]
    
    if failed_tests:
        print(f"\n🚨 CRASH LIKELY CAUSED BY: {', '.join(failed_tests)}")
        print("\n🔧 RECOMMENDED ACTIONS:")
        
        if "Basic Imports" in failed_tests:
            print("   • Check for missing dependencies")
            print("   • Verify Python environment")
        
        if "Data Structures" in failed_tests:
            print("   • Fix data structure definitions")
            print("   • Check dataclass field types")
        
        if "Analysis Pipeline" in failed_tests:
            print("   • Check API connectivity")
            print("   • Verify analysis engine imports")
        
        if "GUI Creation" in failed_tests:
            print("   • Check tkinter installation")
            print("   • Verify GUI component creation")
        
        if "Minimal App" in failed_tests:
            print("   • Basic GUI framework issue")
            print("   • Check display/graphics drivers")
    
    else:
        print("\n🎉 ALL TESTS PASSED!")
        print("The crash may be caused by a specific interaction or timing issue.")
        print("Try running the application with more detailed logging.")

if __name__ == "__main__":
    try:
        run_crash_analysis()
    except Exception as e:
        print(f"\n💥 DIAGNOSTIC SCRIPT ITSELF CRASHED: {e}")
        traceback.print_exc()
        
    print(f"\n📝 Check 'crash_log.txt' for detailed error logs")
