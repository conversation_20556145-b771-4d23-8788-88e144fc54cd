"""
Real-time Data Refresh Manager
Manages automatic data refresh with appropriate intervals and error handling

This module provides:
- Real-time price updates (every minute)
- Market indicators refresh (every 5 minutes)
- Strategy analysis refresh (every 15 minutes)
- Error handling with graceful fallbacks
- Data caching and optimization
- Background refresh threads
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import queue

from enhanced_fmp_api import EnhancedFMPClient, MarketIndicators, OptionsData, SectorPerformance
from market_analysis_engine import MarketAnalysisEngine
from intelligent_strategy_engine import IntelligentStrategyEngine

class RefreshInterval(Enum):
    REAL_TIME = 60      # 1 minute
    FREQUENT = 300      # 5 minutes  
    REGULAR = 900       # 15 minutes
    HOURLY = 3600       # 1 hour
    DAILY = 86400       # 24 hours

@dataclass
class DataSubscription:
    """Data subscription configuration"""
    name: str
    refresh_function: Callable
    interval: RefreshInterval
    last_update: Optional[datetime] = None
    error_count: int = 0
    max_errors: int = 3
    enabled: bool = True

@dataclass
class DataUpdate:
    """Data update notification"""
    subscription_name: str
    data: Any
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None

class RealTimeDataManager:
    """Manages real-time data refresh for the trading system"""
    
    def __init__(self, fmp_api_key: str, symbols: List[str]):
        self.fmp_api_key = fmp_api_key
        self.symbols = symbols
        self.logger = logging.getLogger(__name__)
        
        # Initialize clients
        self.enhanced_client = EnhancedFMPClient(fmp_api_key)
        self.market_analyzer = MarketAnalysisEngine(fmp_api_key)
        self.strategy_engine = IntelligentStrategyEngine(fmp_api_key)
        
        # Data storage
        self.current_data = {}
        self.data_lock = threading.Lock()
        
        # Subscription management
        self.subscriptions: Dict[str, DataSubscription] = {}
        self.update_queue = queue.Queue()
        self.callbacks: Dict[str, List[Callable]] = {}
        
        # Threading
        self.refresh_threads: Dict[str, threading.Thread] = {}
        self.running = False
        self.stop_event = threading.Event()
        
        # Setup default subscriptions
        self._setup_default_subscriptions()
    
    def _setup_default_subscriptions(self):
        """Setup default data subscriptions"""
        
        # Real-time price updates
        self.add_subscription(
            "stock_quotes",
            self._refresh_stock_quotes,
            RefreshInterval.REAL_TIME
        )
        
        # Market indicators (VIX, put/call ratio, etc.)
        self.add_subscription(
            "market_indicators", 
            self._refresh_market_indicators,
            RefreshInterval.FREQUENT
        )
        
        # Options data
        self.add_subscription(
            "options_data",
            self._refresh_options_data,
            RefreshInterval.FREQUENT
        )
        
        # Sector performance
        self.add_subscription(
            "sector_performance",
            self._refresh_sector_performance,
            RefreshInterval.REGULAR
        )
        
        # Strategy recommendations
        self.add_subscription(
            "strategy_recommendations",
            self._refresh_strategy_recommendations,
            RefreshInterval.REGULAR
        )
        
        # News sentiment
        self.add_subscription(
            "news_sentiment",
            self._refresh_news_sentiment,
            RefreshInterval.REGULAR
        )
        
        # Earnings calendar
        self.add_subscription(
            "earnings_calendar",
            self._refresh_earnings_calendar,
            RefreshInterval.HOURLY
        )
        
        # Economic calendar
        self.add_subscription(
            "economic_calendar",
            self._refresh_economic_calendar,
            RefreshInterval.DAILY
        )
    
    def add_subscription(self, name: str, refresh_function: Callable, 
                        interval: RefreshInterval, enabled: bool = True):
        """Add a data subscription"""
        subscription = DataSubscription(
            name=name,
            refresh_function=refresh_function,
            interval=interval,
            enabled=enabled
        )
        self.subscriptions[name] = subscription
        self.callbacks[name] = []
    
    def add_callback(self, subscription_name: str, callback: Callable):
        """Add callback for data updates"""
        if subscription_name in self.callbacks:
            self.callbacks[subscription_name].append(callback)
    
    def start(self):
        """Start real-time data refresh"""
        if self.running:
            return
        
        self.running = True
        self.stop_event.clear()
        
        # Start refresh threads for each subscription
        for name, subscription in self.subscriptions.items():
            if subscription.enabled:
                thread = threading.Thread(
                    target=self._refresh_loop,
                    args=(name,),
                    daemon=True,
                    name=f"DataRefresh-{name}"
                )
                thread.start()
                self.refresh_threads[name] = thread
        
        # Start update processor thread
        processor_thread = threading.Thread(
            target=self._process_updates,
            daemon=True,
            name="UpdateProcessor"
        )
        processor_thread.start()
        
        self.logger.info("Real-time data manager started")
    
    def stop(self):
        """Stop real-time data refresh"""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        # Wait for threads to finish
        for thread in self.refresh_threads.values():
            thread.join(timeout=5.0)
        
        self.refresh_threads.clear()
        self.logger.info("Real-time data manager stopped")
    
    def _refresh_loop(self, subscription_name: str):
        """Main refresh loop for a subscription"""
        subscription = self.subscriptions[subscription_name]
        
        while self.running and not self.stop_event.is_set():
            try:
                # Check if it's time to refresh
                now = datetime.now()
                if (subscription.last_update is None or 
                    (now - subscription.last_update).total_seconds() >= subscription.interval.value):
                    
                    # Perform refresh
                    data = subscription.refresh_function()
                    
                    # Store data
                    with self.data_lock:
                        self.current_data[subscription_name] = data
                    
                    # Create update notification
                    update = DataUpdate(
                        subscription_name=subscription_name,
                        data=data,
                        timestamp=now,
                        success=True
                    )
                    
                    # Queue update for processing
                    self.update_queue.put(update)
                    
                    # Update subscription
                    subscription.last_update = now
                    subscription.error_count = 0
                    
                    self.logger.debug(f"Refreshed {subscription_name}")
                
                # Sleep for a short interval
                self.stop_event.wait(min(30, subscription.interval.value / 10))
                
            except Exception as e:
                self.logger.error(f"Error refreshing {subscription_name}: {e}")
                
                # Handle error
                subscription.error_count += 1
                
                # Create error notification
                update = DataUpdate(
                    subscription_name=subscription_name,
                    data=None,
                    timestamp=datetime.now(),
                    success=False,
                    error_message=str(e)
                )
                self.update_queue.put(update)
                
                # Disable subscription if too many errors
                if subscription.error_count >= subscription.max_errors:
                    subscription.enabled = False
                    self.logger.warning(f"Disabled {subscription_name} due to repeated errors")
                    break
                
                # Wait before retrying
                self.stop_event.wait(60)
    
    def _process_updates(self):
        """Process update notifications and call callbacks"""
        while self.running:
            try:
                # Get update from queue (with timeout)
                update = self.update_queue.get(timeout=1.0)
                
                # Call callbacks for this subscription
                for callback in self.callbacks.get(update.subscription_name, []):
                    try:
                        callback(update)
                    except Exception as e:
                        self.logger.error(f"Error in callback for {update.subscription_name}: {e}")
                
                self.update_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error processing updates: {e}")
    
    def get_current_data(self, subscription_name: str) -> Optional[Any]:
        """Get current data for a subscription"""
        with self.data_lock:
            return self.current_data.get(subscription_name)
    
    def get_all_current_data(self) -> Dict[str, Any]:
        """Get all current data"""
        with self.data_lock:
            return self.current_data.copy()
    
    def force_refresh(self, subscription_name: str) -> bool:
        """Force immediate refresh of a subscription"""
        if subscription_name not in self.subscriptions:
            return False
        
        try:
            subscription = self.subscriptions[subscription_name]
            data = subscription.refresh_function()
            
            with self.data_lock:
                self.current_data[subscription_name] = data
            
            # Create update notification
            update = DataUpdate(
                subscription_name=subscription_name,
                data=data,
                timestamp=datetime.now(),
                success=True
            )
            self.update_queue.put(update)
            
            subscription.last_update = datetime.now()
            subscription.error_count = 0
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error force refreshing {subscription_name}: {e}")
            return False
    
    # Data refresh methods
    def _refresh_stock_quotes(self) -> Dict[str, dict]:
        """Refresh real-time stock quotes"""
        return self.enhanced_client.get_real_time_quotes(self.symbols)
    
    def _refresh_market_indicators(self) -> MarketIndicators:
        """Refresh market indicators"""
        return self.enhanced_client.get_market_indicators()
    
    def _refresh_options_data(self) -> Dict[str, OptionsData]:
        """Refresh options data for all symbols"""
        options_data = {}
        for symbol in self.symbols:
            data = self.enhanced_client.get_options_data(symbol)
            if data:
                options_data[symbol] = data
        return options_data
    
    def _refresh_sector_performance(self) -> List[SectorPerformance]:
        """Refresh sector performance data"""
        return self.enhanced_client.get_sector_performance()
    
    def _refresh_strategy_recommendations(self) -> Dict[str, Any]:
        """Refresh strategy recommendations"""
        try:
            market_report, strategy_recommendations = self.strategy_engine.generate_daily_recommendations(self.symbols)
            return {
                'market_report': market_report,
                'strategy_recommendations': {rec.symbol: rec for rec in strategy_recommendations}
            }
        except Exception as e:
            self.logger.error(f"Error refreshing strategy recommendations: {e}")
            return {}
    
    def _refresh_news_sentiment(self) -> Dict[str, List[dict]]:
        """Refresh news sentiment data"""
        return self.enhanced_client.get_news_sentiment(self.symbols)
    
    def _refresh_earnings_calendar(self) -> Dict[str, dict]:
        """Refresh earnings calendar"""
        return self.enhanced_client.get_earnings_calendar(self.symbols)
    
    def _refresh_economic_calendar(self) -> List[dict]:
        """Refresh economic calendar"""
        return self.enhanced_client.get_economic_calendar()
    
    def get_subscription_status(self) -> Dict[str, dict]:
        """Get status of all subscriptions"""
        status = {}
        for name, subscription in self.subscriptions.items():
            status[name] = {
                'enabled': subscription.enabled,
                'last_update': subscription.last_update,
                'error_count': subscription.error_count,
                'interval_seconds': subscription.interval.value,
                'next_update': subscription.last_update + timedelta(seconds=subscription.interval.value) if subscription.last_update else None
            }
        return status
    
    def enable_subscription(self, name: str):
        """Enable a subscription"""
        if name in self.subscriptions:
            self.subscriptions[name].enabled = True
            self.subscriptions[name].error_count = 0
    
    def disable_subscription(self, name: str):
        """Disable a subscription"""
        if name in self.subscriptions:
            self.subscriptions[name].enabled = False
