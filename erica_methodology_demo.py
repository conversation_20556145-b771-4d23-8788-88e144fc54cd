"""
<PERSON>'s Methodology Demo - EXACT Implementation
Demonstrates the precise implementation of AbundantlyErica's strategy rules

This demo shows how the system now EXACTLY follows <PERSON>'s methodology including:
1. Precise delta targets, DTE ranges, and profit targets
2. Ticker-specific overrides for AMD, NVDA, GOOGL, AMZN, AAPL
3. Earnings proximity rules and IV rank thresholds
4. Expected move calculations and strike selection
5. Roll management and risk controls

Date: August 18, 2025
"""

from datetime import datetime, timedelta
from erica_strategy_engine import (
    EricaStrategyEngine, EricaSignals, MarketBias, EricaStrategy, 
    EricaTradeSetup, create_erica_engine
)

def demo_erica_methodology():
    """Comprehensive demo of <PERSON>'s exact methodology"""
    
    print("=" * 80)
    print("ERICA'S STRATEGY METHODOLOGY DEMO - August 18, 2025")
    print("Following AbundantlyErica's EXACT Rules and Parameters")
    print("=" * 80)
    
    # Initialize <PERSON>'s engine
    erica_engine = create_erica_engine()
    
    # Demo scenarios for each ticker with different market conditions
    demo_scenarios = [
        {
            "symbol": "AAPL",
            "scenario": "Fast Money CC (<PERSON>'s AAPL preference)",
            "signals": EricaSignals(
                spot_price=175.50,
                earnings_days_away=45,
                iv_rank=25.0,
                iv_percentile=28.0,
                expected_move=None,
                atr=3.50,
                support_level=170.0,
                resistance_level=180.0
            ),
            "market_bias": MarketBias.NEUTRAL
        },
        {
            "symbol": "NVDA",
            "scenario": "Earnings CC with Conservative Delta (NVDA override)",
            "signals": EricaSignals(
                spot_price=450.00,
                earnings_days_away=8,
                iv_rank=75.0,
                iv_percentile=78.0,
                expected_move=35.0,  # High expected move
                atr=15.0,
                support_level=430.0,
                resistance_level=470.0
            ),
            "market_bias": MarketBias.MILDLY_BULLISH
        },
        {
            "symbol": "AMD",
            "scenario": "Put Credit Spread (High IV)",
            "signals": EricaSignals(
                spot_price=125.00,
                earnings_days_away=25,
                iv_rank=68.0,
                iv_percentile=72.0,
                expected_move=None,
                atr=4.50,
                support_level=120.0,
                resistance_level=130.0
            ),
            "market_bias": MarketBias.BULLISH
        },
        {
            "symbol": "AMZN",
            "scenario": "Wheel Strategy (AMZN preference)",
            "signals": EricaSignals(
                spot_price=145.00,
                earnings_days_away=35,
                iv_rank=55.0,
                iv_percentile=58.0,
                expected_move=None,
                atr=5.00,
                support_level=140.0,
                resistance_level=150.0
            ),
            "market_bias": MarketBias.NEUTRAL
        },
        {
            "symbol": "GOOGL",
            "scenario": "Near-Earnings Conservative CC (GOOGL buffer)",
            "signals": EricaSignals(
                spot_price=135.00,
                earnings_days_away=7,
                iv_rank=62.0,
                iv_percentile=65.0,
                expected_move=8.50,
                atr=3.20,
                support_level=130.0,
                resistance_level=140.0
            ),
            "market_bias": MarketBias.MILDLY_BULLISH
        }
    ]
    
    # Run analysis for each scenario
    for i, scenario in enumerate(demo_scenarios, 1):
        print(f"\n{i}. {scenario['scenario']}")
        print("-" * 60)
        
        # Get Erica's recommendations
        setups = erica_engine.analyze_setup(
            scenario["symbol"], 
            scenario["signals"], 
            scenario["market_bias"]
        )
        
        if not setups:
            print(f"❌ No suitable setups found for {scenario['symbol']} under current conditions")
            continue
        
        # Display top recommendation
        top_setup = setups[0]
        display_erica_setup(top_setup, scenario["signals"])
        
        # Show ticker-specific adjustments
        ticker_overrides = erica_engine.params.TICKER_OVERRIDES.get(scenario["symbol"], {})
        if ticker_overrides:
            print(f"\n📋 {scenario['symbol']} Specific Overrides:")
            for key, value in ticker_overrides.items():
                print(f"   • {key}: {value}")
        
        # Show alternative setups
        if len(setups) > 1:
            print(f"\n🔄 Alternative Strategies:")
            for alt_setup in setups[1:3]:  # Show top 2 alternatives
                print(f"   • {alt_setup.strategy.value}: {alt_setup.reasoning}")
    
    # Demo roll management
    print("\n" + "=" * 80)
    print("ROLL MANAGEMENT DEMO - Erica's Exact Rules")
    print("=" * 80)
    
    demo_roll_management(erica_engine)
    
    # Demo parameter summary
    print("\n" + "=" * 80)
    print("ERICA'S PARAMETER SUMMARY")
    print("=" * 80)
    
    display_erica_parameters(erica_engine)

def display_erica_setup(setup: EricaTradeSetup, signals: EricaSignals):
    """Display a detailed breakdown of Erica's trade setup"""
    
    print(f"🎯 Strategy: {setup.strategy.value.replace('_', ' ').title()}")
    print(f"📊 Symbol: {setup.symbol}")
    
    # Entry details
    print(f"\n📈 Entry Details:")
    if setup.short_strike:
        print(f"   Short Strike: ${setup.short_strike:.2f}")
    if setup.long_strike:
        print(f"   Long Strike: ${setup.long_strike:.2f}")
    print(f"   Target Delta: {setup.target_delta:.2f}")
    print(f"   DTE: {setup.dte} days")
    
    # Risk/Reward
    print(f"\n💰 Risk/Reward:")
    print(f"   Max Profit: ${setup.max_profit:.0f}")
    if setup.max_loss < float('inf'):
        print(f"   Max Loss: ${setup.max_loss:.0f}")
    else:
        print(f"   Max Loss: Unlimited (covered strategy)")
    print(f"   Breakeven: ${setup.breakeven:.2f}")
    
    if setup.credit_received:
        print(f"   Credit Received: ${setup.credit_received:.2f}")
    if setup.debit_paid:
        print(f"   Debit Paid: ${setup.debit_paid:.2f}")
    
    # Management rules
    print(f"\n⚙️ Management Rules (Erica's Exact):")
    print(f"   Profit Target: {setup.profit_target_pct:.0%} of credit")
    print(f"   Time Exit: {setup.time_exit_dte} DTE if thesis unclear")
    print(f"   Roll Trigger: Delta > {setup.roll_trigger_delta:.2f}")
    
    # Reasoning and risks
    print(f"\n🧠 Reasoning: {setup.reasoning}")
    
    print(f"\n⚠️ Risk Factors:")
    for risk in setup.risk_factors:
        print(f"   • {risk}")
    
    print(f"\n📝 Management Notes: {setup.management_notes}")

def demo_roll_management(erica_engine: EricaStrategyEngine):
    """Demo Erica's roll management rules"""
    
    # Create a sample setup that needs rolling
    sample_setup = EricaTradeSetup(
        strategy=EricaStrategy.BASELINE_CC,
        symbol="AAPL",
        short_strike=180.0,
        long_strike=None,
        target_delta=0.25,
        dte=5,  # Low DTE
        max_profit=350.0,
        max_loss=float('inf'),
        breakeven=175.0,
        credit_received=3.50,
        debit_paid=None,
        profit_target_pct=0.6,
        time_exit_dte=5,
        roll_trigger_delta=0.35,
        reasoning="Sample covered call for roll demo",
        risk_factors=["Assignment risk"],
        management_notes="Roll if threatened",
        ticker_specific_adjustments={}
    )
    
    # Current market conditions showing need to roll
    current_signals = EricaSignals(
        spot_price=182.0,  # Above strike
        earnings_days_away=30,
        iv_rank=45.0,
        iv_percentile=48.0,
        expected_move=None,
        atr=3.50,
        support_level=178.0,
        resistance_level=185.0
    )
    
    print("📋 Sample Position:")
    print(f"   Strategy: {sample_setup.strategy.value}")
    print(f"   Short Strike: ${sample_setup.short_strike}")
    print(f"   Current Price: ${current_signals.spot_price}")
    print(f"   DTE: {sample_setup.dte}")
    
    # Check roll conditions
    roll_conditions = erica_engine.check_roll_conditions(sample_setup, current_signals)
    
    print(f"\n🔄 Roll Analysis:")
    print(f"   Should Roll: {'YES' if roll_conditions['should_roll'] else 'NO'}")
    if roll_conditions['should_roll']:
        print(f"   Roll Type: {roll_conditions['roll_type']}")
        print(f"   Reason: {roll_conditions['reason']}")
        
        print(f"\n📋 Erica's Roll Rules:")
        print(f"   • Delta Breach Threshold: {erica_engine.params.ROLLS['deltaBreach']}")
        print(f"   • Time Floor: {erica_engine.params.ROLLS['timeFloorDTE']} DTE")
        print(f"   • Price Proximity: {erica_engine.params.ROLLS['priceProximityATR']} ATR")

def display_erica_parameters(erica_engine: EricaStrategyEngine):
    """Display Erica's complete parameter set"""
    
    print("📊 Covered Call Parameters:")
    for strategy_type, params in erica_engine.params.COVERED_CALLS.items():
        print(f"   {strategy_type}:")
        for key, value in params.items():
            print(f"      {key}: {value}")
    
    print(f"\n📊 Spread Parameters:")
    for spread_type, params in erica_engine.params.SPREADS.items():
        print(f"   {spread_type}:")
        for key, value in params.items():
            print(f"      {key}: {value}")
    
    print(f"\n📊 LEAPS Parameters:")
    for key, value in erica_engine.params.LEAPS.items():
        print(f"   {key}: {value}")
    
    print(f"\n📊 Ticker-Specific Overrides:")
    for ticker, overrides in erica_engine.params.TICKER_OVERRIDES.items():
        print(f"   {ticker}:")
        for key, value in overrides.items():
            print(f"      {key}: {value}")
    
    print(f"\n📊 Risk Management:")
    for key, value in erica_engine.params.RISK.items():
        print(f"   {key}: {value}")

def main():
    """Main demo function"""
    
    print("🚀 Starting Erica's Methodology Demo...")
    print("This demo shows EXACT implementation of AbundantlyErica's strategy rules")
    print()
    
    try:
        demo_erica_methodology()
        
        print("\n" + "=" * 80)
        print("✅ DEMO COMPLETED SUCCESSFULLY")
        print("The system now follows Erica's EXACT methodology including:")
        print("• Precise delta targets and DTE ranges")
        print("• Ticker-specific overrides for AMD, NVDA, GOOGL, AMZN, AAPL")
        print("• Earnings proximity rules and IV thresholds")
        print("• Expected move calculations and strike selection")
        print("• Roll management per Erica's exact triggers")
        print("• Risk controls and position sizing rules")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        print("Please ensure all required modules are available.")

if __name__ == "__main__":
    main()
