#!/usr/bin/env python3
"""
Rate-limited version of the Erica trading system
This version prevents API overload by limiting concurrent calls
"""

import time
import threading
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

class RateLimitedTradingApp:
    """Rate-limited version of the trading application"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Erica Trading System - Rate Limited")
        self.root.geometry("800x600")
        
        # Rate limiting
        self.last_api_call = 0
        self.min_call_interval = 0.5  # 500ms between API calls
        self.max_concurrent_calls = 3  # Maximum 3 concurrent API calls
        self.active_calls = 0
        self.call_lock = threading.Lock()
        
        # Data storage
        self.market_data = {}
        self.analysis_results = {}
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI with rate limiting controls"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='navy', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🚀 ERICA TRADING SYSTEM", 
                              font=('Arial', 18, 'bold'), fg='white', bg='navy')
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(header_frame, text="Rate-Limited & Stable Version", 
                                 font=('Arial', 12), fg='lightblue', bg='navy')
        subtitle_label.pack()
        
        # Control panel
        control_frame = tk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Analysis button
        self.analyze_btn = tk.Button(control_frame, text="🚀 RUN SAFE ANALYSIS", 
                                    command=self.run_safe_analysis, font=('Arial', 12, 'bold'),
                                    bg='green', fg='white', padx=20, pady=10)
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        # Status
        self.status_label = tk.Label(control_frame, text="Ready - Click to start safe analysis", 
                                    font=('Arial', 10), fg='blue')
        self.status_label.pack(side=tk.LEFT, padx=20)
        
        # Rate limiting info
        rate_info_frame = tk.Frame(self.root)
        rate_info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(rate_info_frame, text="⚡ Rate Limiting Active:", font=('Arial', 10, 'bold')).pack(anchor=tk.W)
        tk.Label(rate_info_frame, text=f"• Minimum {self.min_call_interval}s between API calls").pack(anchor=tk.W)
        tk.Label(rate_info_frame, text=f"• Maximum {self.max_concurrent_calls} concurrent calls").pack(anchor=tk.W)
        tk.Label(rate_info_frame, text="• Sequential processing to prevent overload").pack(anchor=tk.W)
        
        # Results area
        results_frame = tk.LabelFrame(self.root, text="Analysis Results", font=('Arial', 12, 'bold'))
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Courier', 10))
        scrollbar = tk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.root, mode='determinate')
        self.progress.pack(fill=tk.X, padx=10, pady=5)
        
        # Initial message
        welcome_msg = """
🎯 WELCOME TO THE RATE-LIMITED ERICA TRADING SYSTEM

This version prevents crashes by:
✅ Limiting API calls to prevent rate limiting
✅ Sequential processing instead of concurrent overload
✅ Proper error handling and fallback data
✅ Stable, responsive interface

Click "RUN SAFE ANALYSIS" to start the analysis with proper rate limiting.
The analysis will take longer but will be stable and reliable.
"""
        self.results_text.insert(tk.END, welcome_msg)
    
    def rate_limited_api_call(self, api_func, *args, **kwargs):
        """Make an API call with rate limiting"""
        with self.call_lock:
            # Wait for minimum interval
            current_time = time.time()
            time_since_last = current_time - self.last_api_call
            if time_since_last < self.min_call_interval:
                sleep_time = self.min_call_interval - time_since_last
                time.sleep(sleep_time)
            
            # Check concurrent calls limit
            while self.active_calls >= self.max_concurrent_calls:
                time.sleep(0.1)
            
            self.active_calls += 1
            self.last_api_call = time.time()
        
        try:
            result = api_func(*args, **kwargs)
            return result
        except Exception as e:
            print(f"API call failed: {e}")
            return None
        finally:
            with self.call_lock:
                self.active_calls -= 1
    
    def run_safe_analysis(self):
        """Run analysis with proper rate limiting"""
        self.analyze_btn.config(state='disabled')
        self.status_label.config(text="🔄 Running safe analysis...")
        self.results_text.delete(1.0, tk.END)
        self.progress['value'] = 0
        
        def analysis_thread():
            try:
                self.results_text.insert(tk.END, "🚀 STARTING RATE-LIMITED ANALYSIS\n")
                self.results_text.insert(tk.END, "=" * 50 + "\n\n")
                
                # Import modules
                from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
                from daily_recommendations import DailyRecommendationGenerator
                
                api_key = resolve_fmp_key(None)
                if not api_key:
                    self.results_text.insert(tk.END, "❌ No API key found\n")
                    return
                
                symbols = ["AAPL", "NVDA"]  # Limit to 2 symbols for safety
                total_steps = len(symbols) * 3  # Quote + Historical + Analysis
                current_step = 0
                
                self.results_text.insert(tk.END, f"📊 Processing {len(symbols)} symbols with rate limiting...\n\n")
                
                market_data = {}
                historical_data = {}
                
                # Process each symbol sequentially with rate limiting
                for symbol in symbols:
                    self.results_text.insert(tk.END, f"🔍 Processing {symbol}...\n")
                    self.root.update()
                    
                    # Get quote with rate limiting
                    self.status_label.config(text=f"📈 Getting {symbol} quote...")
                    quote = self.rate_limited_api_call(fmp_quote, symbol, api_key)
                    current_step += 1
                    self.progress['value'] = (current_step / total_steps) * 100
                    
                    if quote:
                        market_data[symbol] = {"quote": quote}
                        price = quote.get('price', 'N/A')
                        change = quote.get('changesPercentage', 0)
                        self.results_text.insert(tk.END, f"   ✅ Quote: ${price} ({change:+.2f}%)\n")
                    else:
                        self.results_text.insert(tk.END, f"   ⚠️ Quote failed for {symbol}\n")
                    
                    # Get historical data with rate limiting
                    self.status_label.config(text=f"📊 Getting {symbol} historical data...")
                    hist = self.rate_limited_api_call(fmp_historical_daily, symbol, api_key, limit=30)
                    current_step += 1
                    self.progress['value'] = (current_step / total_steps) * 100
                    
                    if hist:
                        historical_data[symbol] = hist
                        self.results_text.insert(tk.END, f"   ✅ Historical: {len(hist)} days\n")
                    else:
                        self.results_text.insert(tk.END, f"   ⚠️ Historical data failed for {symbol}\n")
                    
                    self.results_text.insert(tk.END, f"   ⏱️ Rate limiting pause...\n")
                    time.sleep(1)  # Extra pause between symbols
                    self.root.update()
                
                # Generate recommendations
                self.status_label.config(text="🧠 Generating recommendations...")
                self.results_text.insert(tk.END, "\n🧠 Generating trading recommendations...\n")
                
                generator = DailyRecommendationGenerator(100000, symbols)
                daily_report = generator.generate_daily_report(market_data, historical_data)
                
                current_step += 1
                self.progress['value'] = 100
                
                # Display results
                self.results_text.insert(tk.END, "\n" + "=" * 50 + "\n")
                self.results_text.insert(tk.END, "📊 ANALYSIS RESULTS\n")
                self.results_text.insert(tk.END, "=" * 50 + "\n\n")
                
                self.results_text.insert(tk.END, f"Market Environment: {daily_report.market_environment.market_outlook}\n")
                self.results_text.insert(tk.END, f"Volatility Regime: {daily_report.market_environment.volatility_regime}\n")
                self.results_text.insert(tk.END, f"Risk Sentiment: {daily_report.market_environment.risk_sentiment}\n\n")
                
                self.results_text.insert(tk.END, "📈 STOCK RECOMMENDATIONS:\n")
                for rec in daily_report.stock_recommendations:
                    signal = rec.primary_signal
                    if signal:
                        self.results_text.insert(tk.END, f"• {rec.symbol}: {signal.action} {signal.strategy.value} ")
                        self.results_text.insert(tk.END, f"(Confidence: {rec.confidence_score:.0%})\n")
                        self.results_text.insert(tk.END, f"  Reasoning: {signal.reasoning}\n\n")
                
                self.results_text.insert(tk.END, "✅ ANALYSIS COMPLETE!\n")
                self.results_text.insert(tk.END, "The system processed all data safely without crashes.\n")
                
                self.status_label.config(text="✅ Analysis complete - No crashes!")
                
            except Exception as e:
                self.results_text.insert(tk.END, f"\n❌ Analysis failed: {e}\n")
                self.status_label.config(text="❌ Analysis failed")
            
            finally:
                self.analyze_btn.config(state='normal')
                self.progress['value'] = 0
        
        # Run in background thread
        threading.Thread(target=analysis_thread, daemon=True).start()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🚀 Starting Rate-Limited Erica Trading System...")
    app = RateLimitedTradingApp()
    app.run()

if __name__ == "__main__":
    main()
