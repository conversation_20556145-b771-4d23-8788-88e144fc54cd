#!/usr/bin/env python3
"""
Restart script for the enhanced Erica trading system
This script safely restarts the application with performance optimizations
"""

import os
import sys
import time
import subprocess
import psutil

def kill_existing_app():
    """Kill any existing desktop_app.py processes"""
    print("🔍 Checking for existing application processes...")
    
    killed_processes = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and any('desktop_app.py' in cmd for cmd in proc.info['cmdline']):
                print(f"   Terminating process {proc.info['pid']}: {' '.join(proc.info['cmdline'])}")
                proc.terminate()
                killed_processes += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if killed_processes > 0:
        print(f"✅ Terminated {killed_processes} existing process(es)")
        time.sleep(2)  # Give processes time to clean up
    else:
        print("✅ No existing processes found")

def start_new_app():
    """Start the new optimized application"""
    print("\n🚀 Starting optimized Erica trading system...")
    
    try:
        # Start the application in a new process
        process = subprocess.Popen([
            sys.executable, 'desktop_app.py'
        ], cwd=os.getcwd())
        
        print(f"✅ Application started with PID: {process.pid}")
        print("🎯 The enhanced trading system is now running with:")
        print("   • Performance optimizations")
        print("   • Error handling improvements")
        print("   • Reduced API call frequency")
        print("   • GUI responsiveness fixes")
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        return None

def main():
    """Main restart function"""
    print("🔄 RESTARTING ENHANCED ERICA TRADING SYSTEM")
    print("=" * 50)
    
    # Kill existing processes
    kill_existing_app()
    
    # Start new application
    process = start_new_app()
    
    if process:
        print("\n" + "=" * 50)
        print("🎉 RESTART COMPLETE!")
        print("=" * 50)
        print("\n💡 PERFORMANCE IMPROVEMENTS APPLIED:")
        print("   • Auto-refresh disabled by default (enable manually if needed)")
        print("   • API error handling prevents GUI freezing")
        print("   • Rate limiting prevents excessive API calls")
        print("   • Background processing for better responsiveness")
        
        print("\n🎯 NEXT STEPS:")
        print("   1. Look for the GUI window that should appear")
        print("   2. Go to Settings tab and configure your OpenAI API key")
        print("   3. Click 'Analyze' to run analysis manually")
        print("   4. Enable Auto-Refresh only if needed (Settings tab)")
        
        print(f"\n📊 Application PID: {process.pid}")
        print("   Use this PID to monitor or terminate the process if needed")
        
    else:
        print("\n❌ RESTART FAILED!")
        print("Please check the error messages above and try running manually:")
        print("   python desktop_app.py")

if __name__ == "__main__":
    main()
