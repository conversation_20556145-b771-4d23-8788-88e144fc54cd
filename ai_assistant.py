"""
AI-Powered Desktop Assistant with ChatGPT-5 Integration
Intelligent chatbot assistant for the Erica trading system

This module provides an intelligent AI assistant that can:
- Analyze ALL data displayed in the desktop application interface
- Access and query the Financial Modeling Prep (FMP) API directly
- Answer natural language questions about trading strategies, market conditions, and analysis
- Provide contextual explanations of <PERSON>'s methodology
- Suggest alternative strategies and risk management guidance

Features:
- OpenAI GPT-4 API integration (latest available model)
- Conversation context and trading session history
- Full read access to all application data and FMP API
- Embedded chatbot interface within the desktop application
- Real-time analysis of live market data
"""

from openai import OpenAI
import json
import logging
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import threading
import queue
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

# Import trading system components for data access
from daily_outline import fmp_quote, fmp_historical_daily, fmp_options_chain, fmp_earnings_next, fmp_news_today
from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
from intelligent_strategy_engine import IntelligentStrategyEngine, StrategyOfTheDay
from erica_decision_framework import EricaDecisionFramework
from strategy_decision_tree import StrategyRecommendation

class ConversationRole(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

@dataclass
class ConversationMessage:
    role: ConversationRole
    content: str
    timestamp: datetime
    context_data: Optional[Dict[str, Any]] = None

@dataclass
class AIAssistantConfig:
    openai_api_key: str
    model: str = "gpt-4"  # Latest available model
    max_tokens: int = 1500
    temperature: float = 0.7
    max_conversation_history: int = 50
    context_window_size: int = 20

class DataAccessLayer:
    """Provides AI assistant with comprehensive access to all application data"""
    
    def __init__(self, fmp_api_key: str, trading_app_instance=None):
        self.fmp_api_key = fmp_api_key
        self.trading_app = trading_app_instance
        self.market_analyzer = MarketAnalysisEngine(fmp_api_key)
        self.strategy_engine = IntelligentStrategyEngine(fmp_api_key)
        self.erica_framework = EricaDecisionFramework()
        self.logger = logging.getLogger(__name__)
        
    def get_current_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Get real-time market data for all symbols"""
        market_data = {}
        
        for symbol in symbols:
            try:
                quote = fmp_quote(symbol, self.fmp_api_key)
                if quote:
                    market_data[symbol] = {
                        'quote': quote,
                        'price': quote.get('price'),
                        'change': quote.get('change'),
                        'change_percent': quote.get('changesPercentage'),
                        'volume': quote.get('volume'),
                        'market_cap': quote.get('marketCap')
                    }
            except Exception as e:
                self.logger.error(f"Error fetching data for {symbol}: {e}")
                
        return market_data
    
    def get_strategy_recommendations(self, symbols: List[str]) -> Dict[str, StrategyOfTheDay]:
        """Get current strategy recommendations for all symbols"""
        try:
            _, strategy_recommendations = self.strategy_engine.generate_daily_recommendations(symbols)
            return {rec.symbol: rec for rec in strategy_recommendations}
        except Exception as e:
            self.logger.error(f"Error getting strategy recommendations: {e}")
            return {}
    
    def get_market_factors(self) -> MarketFactors:
        """Get comprehensive market factor analysis"""
        try:
            return self.market_analyzer.analyze_market_factors()
        except Exception as e:
            self.logger.error(f"Error getting market factors: {e}")
            return None
    
    def get_stock_analysis(self, symbol: str) -> StockSpecificFactors:
        """Get detailed analysis for specific stock"""
        try:
            return self.market_analyzer.analyze_stock_factors(symbol)
        except Exception as e:
            self.logger.error(f"Error getting stock analysis for {symbol}: {e}")
            return None
    
    def get_earnings_calendar(self, symbols: List[str]) -> Dict[str, Any]:
        """Get earnings calendar data for symbols"""
        earnings_data = {}
        
        for symbol in symbols:
            try:
                earnings = fmp_earnings_next(symbol, self.fmp_api_key)
                if earnings:
                    earnings_data[symbol] = earnings
            except Exception as e:
                self.logger.error(f"Error fetching earnings for {symbol}: {e}")
                
        return earnings_data
    
    def get_news_sentiment(self, symbols: List[str]) -> Dict[str, List[Dict]]:
        """Get latest news and sentiment for symbols"""
        try:
            news_data = fmp_news_today(symbols, self.fmp_api_key, limit=50)
            
            # Group news by symbol
            news_by_symbol = {}
            for article in news_data:
                symbol = article.get('symbol', 'GENERAL')
                if symbol not in news_by_symbol:
                    news_by_symbol[symbol] = []
                news_by_symbol[symbol].append(article)
                
            return news_by_symbol
        except Exception as e:
            self.logger.error(f"Error fetching news: {e}")
            return {}
    
    def get_application_state(self) -> Dict[str, Any]:
        """Get current application state and displayed data"""
        if not self.trading_app:
            return {}
            
        try:
            return {
                'account_size': getattr(self.trading_app, 'account_size', 100000),
                'symbols': getattr(self.trading_app, 'symbols', []),
                'last_update': getattr(self.trading_app, 'last_update', None),
                'current_data': getattr(self.trading_app, 'current_data', {}),
                'auto_refresh': getattr(self.trading_app, 'auto_refresh', False),
                'current_strategy_recommendations': getattr(self.trading_app, 'current_strategy_recommendations', {})
            }
        except Exception as e:
            self.logger.error(f"Error getting application state: {e}")
            return {}

class AIConversationManager:
    """Manages conversation context and OpenAI API interactions"""

    def __init__(self, config: AIAssistantConfig, data_access: DataAccessLayer):
        self.config = config
        self.data_access = data_access
        self.conversation_history: List[ConversationMessage] = []
        self.logger = logging.getLogger(__name__)

        # Initialize OpenAI client
        self.openai_client = OpenAI(api_key=config.openai_api_key)

        # System prompt with Erica's methodology
        self.system_prompt = self._create_system_prompt()
        self._add_system_message()
    
    def _create_system_prompt(self) -> str:
        """Create comprehensive system prompt with Erica's methodology"""
        return """You are an expert AI trading assistant for Erica's options trading system (@AbundantlyErica). 

CORE EXPERTISE:
- Erica's exact trading strategies: Covered Calls, Credit Spreads, LEAPS, Premium Selling
- Real-time market analysis and strategy selection
- Risk management and position sizing
- Options trading mechanics and Greeks
- Market timing and volatility analysis

ERICA'S KEY PRINCIPLES:
1. COVERED CALLS: 30-45 DTE, 0.30 delta, $30+ premium, close at 50% profit or 21 DTE
2. CREDIT SPREADS: Bullish markets, 0.15-0.20 delta puts, 30-45 DTE, 50% profit target
3. LEAPS: 12+ months, 0.70-0.80 delta, strong growth stocks, low volatility periods
4. PREMIUM SELLING: High IV rank (>70th percentile), 15-30 delta, systematic approach

RISK MANAGEMENT:
- Never risk more than 2% per trade
- Diversify across strategies and timeframes
- Use systematic profit-taking (50% max profit)
- Maintain cash reserves for opportunities

You have access to real-time market data, strategy recommendations, news, earnings calendar, and all application data. Provide specific, actionable advice based on current market conditions and Erica's proven methodology.

Always explain your reasoning and reference specific data points when making recommendations."""

    def _add_system_message(self):
        """Add system message to conversation history"""
        system_msg = ConversationMessage(
            role=ConversationRole.SYSTEM,
            content=self.system_prompt,
            timestamp=datetime.now()
        )
        self.conversation_history.append(system_msg)
    
    def add_user_message(self, content: str, context_data: Optional[Dict] = None) -> ConversationMessage:
        """Add user message to conversation"""
        message = ConversationMessage(
            role=ConversationRole.USER,
            content=content,
            timestamp=datetime.now(),
            context_data=context_data
        )
        self.conversation_history.append(message)
        self._trim_conversation_history()
        return message
    
    def _trim_conversation_history(self):
        """Keep conversation history within limits"""
        if len(self.conversation_history) > self.config.max_conversation_history:
            # Keep system message and recent messages
            system_messages = [msg for msg in self.conversation_history if msg.role == ConversationRole.SYSTEM]
            recent_messages = self.conversation_history[-(self.config.max_conversation_history-1):]
            self.conversation_history = system_messages + recent_messages
    
    def generate_response(self, user_question: str) -> str:
        """Generate AI response with current market context"""
        try:
            # Gather current context data
            context_data = self._gather_context_data()
            
            # Add user message with context
            self.add_user_message(user_question, context_data)
            
            # Prepare messages for OpenAI API
            messages = self._prepare_api_messages(context_data)
            
            # Call OpenAI API
            response = self.openai_client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            # Extract and store response
            ai_response = response.choices[0].message.content
            
            assistant_msg = ConversationMessage(
                role=ConversationRole.ASSISTANT,
                content=ai_response,
                timestamp=datetime.now(),
                context_data=context_data
            )
            self.conversation_history.append(assistant_msg)
            
            return ai_response
            
        except Exception as e:
            self.logger.error(f"Error generating AI response: {e}")
            return f"I apologize, but I encountered an error processing your request: {str(e)}. Please try again or rephrase your question."
    
    def _gather_context_data(self) -> Dict[str, Any]:
        """Gather comprehensive context data for AI analysis"""
        symbols = ['AAPL', 'NVDA', 'AMD', 'GOOGL', 'AMZN']  # Default symbols
        
        context = {
            'timestamp': datetime.now().isoformat(),
            'market_data': self.data_access.get_current_market_data(symbols),
            'strategy_recommendations': {},
            'market_factors': None,
            'earnings_calendar': self.data_access.get_earnings_calendar(symbols),
            'news_sentiment': self.data_access.get_news_sentiment(symbols),
            'application_state': self.data_access.get_application_state()
        }
        
        # Get strategy recommendations (convert to dict for JSON serialization)
        try:
            strategy_recs = self.data_access.get_strategy_recommendations(symbols)
            context['strategy_recommendations'] = {
                symbol: {
                    'strategy': rec.recommended_strategy.value,
                    'confidence': rec.confidence,
                    'reasoning': rec.reasoning,
                    'entry_criteria': rec.entry_criteria,
                    'key_risks': rec.key_risks
                } for symbol, rec in strategy_recs.items()
            }
        except Exception as e:
            self.logger.error(f"Error getting strategy recommendations: {e}")
        
        # Get market factors
        try:
            market_factors = self.data_access.get_market_factors()
            if market_factors:
                context['market_factors'] = {
                    'vix_level': market_factors.vix_level,
                    'market_regime': market_factors.market_regime.value,
                    'volatility_regime': market_factors.volatility_regime.value,
                    'sentiment_level': market_factors.sentiment_level.value,
                    'bullish_factors': market_factors.bullish_factors,
                    'bearish_factors': market_factors.bearish_factors,
                    'uncertainty_factors': market_factors.uncertainty_factors
                }
        except Exception as e:
            self.logger.error(f"Error getting market factors: {e}")
        
        return context
    
    def _prepare_api_messages(self, context_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Prepare messages for OpenAI API with context"""
        messages = []
        
        # Add system message with current context
        enhanced_system_prompt = f"""{self.system_prompt}

CURRENT MARKET CONTEXT:
{json.dumps(context_data, indent=2, default=str)}

Use this real-time data to provide specific, actionable advice. Reference current prices, strategy recommendations, market conditions, and news when relevant."""
        
        messages.append({
            "role": "system",
            "content": enhanced_system_prompt
        })
        
        # Add recent conversation history (excluding system messages)
        recent_messages = self.conversation_history[-self.config.context_window_size:]
        for msg in recent_messages:
            if msg.role != ConversationRole.SYSTEM:
                messages.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
        
        return messages

class AIAssistantGUI:
    """GUI component for the AI assistant chatbot interface"""
    
    def __init__(self, parent_frame, ai_manager: AIConversationManager):
        self.parent = parent_frame
        self.ai_manager = ai_manager
        self.response_queue = queue.Queue()
        
        self.setup_gui()
        self.check_response_queue()
    
    def setup_gui(self):
        """Setup the chatbot GUI interface"""
        # Main container
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Chat history area
        history_frame = ttk.LabelFrame(main_frame, text="🤖 AI Trading Assistant - Chat with Erica's AI")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.chat_history = scrolledtext.ScrolledText(
            history_frame, 
            wrap=tk.WORD, 
            font=('Arial', 10),
            state=tk.DISABLED,
            height=20
        )
        self.chat_history.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Configure text tags for styling
        self.chat_history.tag_configure("user", foreground="blue", font=('Arial', 10, 'bold'))
        self.chat_history.tag_configure("assistant", foreground="green", font=('Arial', 10))
        self.chat_history.tag_configure("timestamp", foreground="gray", font=('Arial', 8))
        
        # Input area
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Question input
        ttk.Label(input_frame, text="Ask me anything about the market, strategies, or analysis:").pack(anchor=tk.W)
        
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=tk.X, pady=(2, 0))
        
        self.question_entry = ttk.Entry(entry_frame, font=('Arial', 10))
        self.question_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.question_entry.bind('<Return>', self.on_send_question)
        
        self.send_button = ttk.Button(entry_frame, text="Send", command=self.on_send_question)
        self.send_button.pack(side=tk.RIGHT)
        
        # Quick action buttons
        quick_frame = ttk.Frame(input_frame)
        quick_frame.pack(fill=tk.X, pady=(5, 0))
        
        quick_questions = [
            "What's the best strategy for today's market?",
            "Explain current strategy recommendations",
            "What are the key risks right now?",
            "How should I adjust my portfolio?"
        ]
        
        for i, question in enumerate(quick_questions):
            btn = ttk.Button(
                quick_frame, 
                text=question[:30] + "..." if len(question) > 30 else question,
                command=lambda q=question: self.ask_quick_question(q)
            )
            btn.grid(row=i//2, column=i%2, padx=2, pady=2, sticky="ew")
        
        quick_frame.columnconfigure(0, weight=1)
        quick_frame.columnconfigure(1, weight=1)
        
        # Status indicator
        self.status_label = ttk.Label(input_frame, text="Ready to help with your trading questions!", foreground="green")
        self.status_label.pack(pady=(5, 0))
        
        # Add welcome message
        self.add_welcome_message()
    
    def add_welcome_message(self):
        """Add welcome message to chat history"""
        welcome_msg = """🎯 Welcome to Erica's AI Trading Assistant!

I can help you with:
• Current market analysis and strategy recommendations
• Explaining why specific strategies were chosen
• Risk management and position sizing guidance
• Market conditions and how they affect strategy selection
• Erica's methodology and trading principles
• Alternative strategies when conditions change

Ask me anything about the current market data, strategy recommendations, or trading concepts!"""
        
        self.add_message_to_chat("Assistant", welcome_msg, "assistant")
    
    def on_send_question(self, event=None):
        """Handle sending a question to the AI"""
        question = self.question_entry.get().strip()
        if not question:
            return
        
        # Clear input
        self.question_entry.delete(0, tk.END)
        
        # Add user message to chat
        self.add_message_to_chat("You", question, "user")
        
        # Update status
        self.status_label.config(text="🤔 Thinking...", foreground="orange")
        self.send_button.config(state="disabled")
        
        # Generate response in background thread
        threading.Thread(target=self.generate_response_thread, args=(question,), daemon=True).start()
    
    def ask_quick_question(self, question: str):
        """Ask a predefined quick question"""
        self.question_entry.delete(0, tk.END)
        self.question_entry.insert(0, question)
        self.on_send_question()
    
    def generate_response_thread(self, question: str):
        """Generate AI response in background thread"""
        try:
            response = self.ai_manager.generate_response(question)
            self.response_queue.put(("success", response))
        except Exception as e:
            self.response_queue.put(("error", str(e)))
    
    def check_response_queue(self):
        """Check for responses from background thread"""
        try:
            while True:
                status, response = self.response_queue.get_nowait()
                
                if status == "success":
                    self.add_message_to_chat("AI Assistant", response, "assistant")
                    self.status_label.config(text="Ready for your next question!", foreground="green")
                else:
                    self.add_message_to_chat("AI Assistant", f"Error: {response}", "assistant")
                    self.status_label.config(text="Error occurred. Please try again.", foreground="red")
                
                self.send_button.config(state="normal")
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.parent.after(100, self.check_response_queue)
    
    def add_message_to_chat(self, sender: str, message: str, tag: str):
        """Add a message to the chat history"""
        self.chat_history.config(state=tk.NORMAL)
        
        # Add timestamp and sender
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.chat_history.insert(tk.END, f"[{timestamp}] ", "timestamp")
        self.chat_history.insert(tk.END, f"{sender}:\n", tag)
        
        # Add message content
        self.chat_history.insert(tk.END, f"{message}\n\n")
        
        # Scroll to bottom
        self.chat_history.see(tk.END)
        self.chat_history.config(state=tk.DISABLED)
