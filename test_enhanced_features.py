"""
Test Script for Enhanced Desktop Features
Validates the strategy criteria display functionality

This script tests the new strategy criteria analysis system to ensure
all components work correctly together.

Date: August 18, 2025
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os
# Ensure StrategyType is available to all test methods
from daily_outline import StrategyType


class TestStrategyCriteriaDisplay(unittest.TestCase):
    """Test cases for the strategy criteria display system"""

    def setUp(self):
        """Set up test fixtures"""
        try:
            from strategy_criteria_display import StrategyCriteriaAnalyzer, CriteriaStatus
            from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime, SentimentLevel
            from strategy_decision_tree import StrategyRecommendation, StrategyConfidence
            from daily_outline import StrategyType

            self.analyzer = StrategyCriteriaAnalyzer()

            # Create test data
            self.market_factors = MarketFactors(
                vix_level=22.5,
                market_regime=MarketRegime.BULL_MARKET,
                volatility_regime=VolatilityRegime.HIGH_VOLATILITY,
                trend_strength=0.75,
                regime_confidence=0.85,
                bullish_factor_score=0.7,
                bearish_factor_score=0.3,
                volatility_factor_score=0.8,
                uncertainty_factor_score=0.4,
                sentiment_level=SentimentLevel.BULLISH,
                put_call_ratio=0.85,
                market_breadth_score=0.65,
                fear_greed_index=65,
                social_sentiment_score=0.6,
                spy_technical_score=0.8,
                sector_rotation_score=0.7,
                earnings_calendar_intensity=0.3,
                fed_meeting_proximity_days=45,
                major_events_impact=0.2
            )

            self.stock_factors = StockSpecificFactors(
                symbol="AAPL",
                iv_rank=0.75,
                iv_percentile=0.78,
                technical_confluence_score=0.85,
                rsi=58.5,
                macd_signal=0.65,
                bollinger_position=0.7,
                volume_profile_score=0.8,
                support_resistance_score=0.75,
                earnings_days_away=35,
                earnings_surprise_history=0.6,
                analyst_sentiment_score=0.8,
                news_sentiment_score=0.7,
                options_volume=15000,
                options_open_interest=45000,
                bid_ask_spread=0.03,
                gamma_exposure=0.4,
                delta_hedging_flow=0.2,
                sector_performance=0.65,
                sector_rotation_impact="positive",
                wheel_suitability_score=0.9,
                covered_call_attractiveness=0.85,
                credit_spread_opportunity=0.8,
                leaps_opportunity=0.7
            )

            self.strategy_rec = StrategyRecommendation(
                symbol="AAPL",
                primary_strategy=StrategyType.COVERED_CALL,
                confidence=0.85,
                confidence_level=StrategyConfidence.HIGH,
                market_environment_score=0.8,
                stock_specific_score=0.85,
                erica_criteria_score=0.9,
                risk_adjusted_score=0.75,
                key_supporting_factors=["High IV rank", "Strong technicals"],
                key_risk_factors=["Upside breakout risk"],
                alternative_strategies=[(StrategyType.CREDIT_SPREAD, 0.75)],
                recommended_dte=(30, 45),
                recommended_delta=(0.25, 0.35),
                position_size_multiplier=1.0,
                invalidation_triggers=["VIX < 15"],
                upgrade_triggers=["IV rank > 80%"]
            )

        except ImportError as e:
            self.skipTest(f"Required modules not available: {e}")

    def test_criteria_analyzer_initialization(self):
        """Test that the criteria analyzer initializes correctly"""
        self.assertIsNotNone(self.analyzer)
        self.assertIsNotNone(self.analyzer.erica_framework)

    def test_strategy_criteria_analysis(self):
        """Test complete strategy criteria analysis"""
        analysis = self.analyzer.analyze_strategy_criteria(
            self.strategy_rec, self.market_factors, self.stock_factors
        )

        # Verify analysis structure
        self.assertEqual(analysis.symbol, "AAPL")
        self.assertEqual(analysis.strategy, StrategyType.COVERED_CALL)
        self.assertIsInstance(analysis.overall_score, float)
        self.assertGreaterEqual(analysis.overall_score, 0.0)
        self.assertLessEqual(analysis.overall_score, 1.0)

        # Verify criteria categories
        self.assertIsInstance(analysis.market_criteria, list)
        self.assertIsInstance(analysis.stock_criteria, list)
        self.assertIsInstance(analysis.erica_criteria, list)
        self.assertIsInstance(analysis.risk_criteria, list)

        # Verify summary and strength
        self.assertIsInstance(analysis.summary, str)
        self.assertIn(analysis.recommendation_strength, ["STRONG", "MODERATE", "WEAK", "AVOID"])

    def test_vix_evaluation(self):
        """Test VIX level evaluation for different strategies"""
        # Test covered call with high VIX (should be good)
        status, score = self.analyzer._evaluate_vix_for_strategy(StrategyType.COVERED_CALL, 25.0)
        self.assertEqual(status.value, "met")
        self.assertEqual(score, 1.0)

        # Test LEAPS with high VIX (should be poor)
        status, score = self.analyzer._evaluate_vix_for_strategy(StrategyType.LEAPS, 25.0)
        self.assertEqual(status.value, "not_met")
        self.assertEqual(score, 0.3)

    def test_iv_rank_evaluation(self):
        """Test IV rank evaluation for different strategies"""
        # Test premium selling with high IV rank (should be good)
        status, score = self.analyzer._evaluate_iv_rank(StrategyType.PREMIUM_SELLING, 0.8)
        self.assertEqual(status.value, "met")
        self.assertEqual(score, 1.0)

        # Test LEAPS with high IV rank (should be poor)
        status, score = self.analyzer._evaluate_iv_rank(StrategyType.LEAPS, 0.8)
        self.assertEqual(status.value, "not_met")
        self.assertEqual(score, 0.2)

    def test_market_regime_evaluation(self):
        """Test market regime evaluation"""
        from market_analysis_engine import MarketRegime

        # Test LEAPS with bull market (should be good)
        status, score = self.analyzer._evaluate_market_regime(StrategyType.LEAPS, MarketRegime.BULL_MARKET)
        self.assertEqual(status.value, "met")
        self.assertEqual(score, 1.0)

        # Test LEAPS with bear market (should be poor)
        status, score = self.analyzer._evaluate_market_regime(StrategyType.LEAPS, MarketRegime.BEAR_MARKET)
        self.assertEqual(status.value, "not_met")
        self.assertEqual(score, 0.2)

class TestDesktopAppIntegration(unittest.TestCase):
    """Test cases for desktop app integration"""

    def test_criteria_widget_creation(self):
        """Test that the criteria widget can be created"""
        try:
            import tkinter as tk
            from strategy_criteria_display import StrategyCriteriaWidget

            root = tk.Tk()
            widget = StrategyCriteriaWidget(root)
            self.assertIsNotNone(widget)
            self.assertIsNotNone(widget.analyzer)
            root.destroy()

        except ImportError:
            self.skipTest("Tkinter not available")

    def test_enhanced_desktop_app_imports(self):
        """Test that enhanced desktop app imports work"""
        try:
            from desktop_app import TradingSystemGUI
            from strategy_criteria_display import StrategyCriteriaWidget, StrategyCriteriaAnalyzer
            from intelligent_strategy_engine import IntelligentStrategyEngine
            from market_analysis_engine import MarketAnalysisEngine

            # If we get here, imports are successful
            self.assertTrue(True)

        except ImportError as e:
            self.fail(f"Enhanced desktop app imports failed: {e}")

def run_comprehensive_test():
    """Run comprehensive test of enhanced features"""
    print("=" * 60)
    print("ENHANCED DESKTOP FEATURES TEST - August 18, 2025")
    print("=" * 60)

    # Test 1: Module imports
    print("Testing module imports...")
    try:
        from strategy_criteria_display import StrategyCriteriaAnalyzer, StrategyCriteriaWidget
        from desktop_app import TradingSystemGUI
        print("✓ All modules imported successfully")
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

    # Test 2: Criteria analysis
    print("\nTesting criteria analysis...")
    try:
        suite = unittest.TestLoader().loadTestsFromTestCase(TestStrategyCriteriaDisplay)
        runner = unittest.TextTestRunner(verbosity=0)
        result = runner.run(suite)
        if result.wasSuccessful():
            print("✓ Criteria analysis tests passed")
        else:
            print(f"✗ Criteria analysis tests failed: {len(result.failures)} failures, {len(result.errors)} errors")
    except Exception as e:
        print(f"✗ Criteria analysis test error: {e}")

    # Test 3: Desktop app integration
    print("\nTesting desktop app integration...")
    try:
        suite = unittest.TestLoader().loadTestsFromTestCase(TestDesktopAppIntegration)
        runner = unittest.TextTestRunner(verbosity=0)
        result = runner.run(suite)
        if result.wasSuccessful():
            print("✓ Desktop app integration tests passed")
        else:
            print(f"✗ Desktop app integration tests failed: {len(result.failures)} failures, {len(result.errors)} errors")
    except Exception as e:
        print(f"✗ Desktop app integration test error: {e}")

    print("\n" + "=" * 60)
    print("Enhanced features testing completed!")
    print("Ready for production use on August 18, 2025")
    print("=" * 60)

    return True

if __name__ == "__main__":
    # Run comprehensive test
    run_comprehensive_test()

    # Ask if user wants to run unit tests
    try:
        response = input("\nRun detailed unit tests? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            unittest.main(verbosity=2)
    except KeyboardInterrupt:
        print("\nTesting interrupted. Goodbye!")
