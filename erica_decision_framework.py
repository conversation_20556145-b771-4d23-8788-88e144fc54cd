"""
<PERSON>'s Complete Decision Framework Implementation
Based on @AbundantlyErica YouTube content and Millionaire Mentorship Program

This module implements <PERSON>'s exact decision-making process for strategy selection,
market timing, and risk management as taught in her educational content.

Key Components:
- Strategy selection criteria for each strategy type
- Market timing principles and volatility-based switching
- Risk management rules for different market environments
- Position sizing methodology based on market conditions
- Profit-taking and loss management rules
- Systematic approach to options trading

All rules are based on <PERSON>'s publicly available educational content.
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import math

from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime
from daily_outline import StrategyType

class EricaMarketEnvironment(Enum):
    HIGH_VOLATILITY = "high_volatility"  # IV rank > 70%
    NORMAL_VOLATILITY = "normal_volatility"  # IV rank 30-70%
    LOW_VOLATILITY = "low_volatility"  # IV rank < 30%
    EARNINGS_SEASON = "earnings_season"  # High earnings activity
    MARKET_STRESS = "market_stress"  # VIX > 30, extreme conditions

@dataclass
class EricaStrategyRules:
    """Erica's specific rules for each strategy"""
    
    # Covered Calls
    cc_min_iv_rank: float = 0.5  # Minimum 50th percentile IV
    cc_preferred_dte: Tuple[int, int] = (30, 45)  # 30-45 days to expiration
    cc_target_delta: float = 0.30  # Target 30 delta
    cc_min_premium: float = 0.30  # Minimum $30 premium per contract
    cc_profit_target: float = 0.50  # Take profits at 50% of max gain
    cc_management_dte: int = 21  # Manage at 21 DTE if not profitable
    
    # Credit Spreads (Put Credit Spreads)
    cs_min_iv_rank: float = 0.6  # Minimum 60th percentile IV
    cs_preferred_dte: Tuple[int, int] = (30, 45)  # 30-45 days to expiration
    cs_target_delta: float = 0.20  # Target 15-20 delta on short put
    cs_min_credit: float = 0.25  # Minimum $25 credit per spread
    cs_max_risk_reward: float = 3.0  # Maximum 1:3 risk/reward ratio
    cs_profit_target: float = 0.50  # Take profits at 50% of max gain
    cs_bullish_bias_required: bool = True  # Require bullish market bias
    
    # LEAPS (Long-term Equity Anticipation Securities)
    leaps_min_timeframe: int = 365  # Minimum 12 months to expiration
    leaps_target_delta: float = 0.75  # Target 70-80 delta (deep ITM)
    leaps_max_iv_rank: float = 0.6  # Prefer lower IV for buying options
    leaps_strong_trend_required: bool = True  # Require strong bullish trend
    leaps_profit_target: float = 1.0  # Take profits at 100% gain
    leaps_stop_loss: float = 0.5  # Stop loss at 50% of premium paid
    
    # Premium Selling (Systematic)
    ps_min_iv_rank: float = 0.7  # Minimum 70th percentile IV
    ps_systematic_approach: bool = True  # Use systematic, not emotional approach
    ps_diversification_required: bool = True  # Diversify across strategies
    ps_profit_target: float = 0.50  # Take profits at 50% of max gain
    ps_high_vol_preferred: bool = True  # Prefer high volatility periods

@dataclass
class EricaMarketTimingRules:
    """Erica's market timing and environment assessment rules"""
    
    # Volatility-based strategy switching
    high_vol_favored_strategies: List[StrategyType] = None
    low_vol_favored_strategies: List[StrategyType] = None
    
    # Market regime preferences
    bull_market_strategies: List[StrategyType] = None
    bear_market_strategies: List[StrategyType] = None
    sideways_market_strategies: List[StrategyType] = None
    
    # Earnings season adjustments
    earnings_season_approach: str = "selective"  # Be selective during earnings
    earnings_iv_crush_opportunity: bool = True  # Use IV crush post-earnings
    
    # Fed meeting and macro event timing
    fed_meeting_caution_days: int = 7  # Be cautious 7 days before Fed meetings
    macro_event_position_sizing: float = 0.75  # Reduce size during macro events
    
    def __post_init__(self):
        if self.high_vol_favored_strategies is None:
            self.high_vol_favored_strategies = [
                StrategyType.COVERED_CALL,
                StrategyType.CREDIT_SPREAD,
                StrategyType.PREMIUM_SELLING
            ]
        
        if self.low_vol_favored_strategies is None:
            self.low_vol_favored_strategies = [
                StrategyType.LEAPS
            ]
        
        if self.bull_market_strategies is None:
            self.bull_market_strategies = [
                StrategyType.LEAPS,
                StrategyType.CREDIT_SPREAD,
                StrategyType.COVERED_CALL
            ]
        
        if self.bear_market_strategies is None:
            self.bear_market_strategies = [
                StrategyType.COVERED_CALL,
                StrategyType.PREMIUM_SELLING
            ]
        
        if self.sideways_market_strategies is None:
            self.sideways_market_strategies = [
                StrategyType.COVERED_CALL,
                StrategyType.PREMIUM_SELLING
            ]

@dataclass
class EricaRiskManagementRules:
    """Erica's risk management and position sizing rules"""
    
    # Account risk limits
    max_risk_per_trade: float = 0.02  # Never risk more than 2% per trade
    max_portfolio_risk: float = 0.20  # Maximum 20% of portfolio at risk
    cash_reserve_minimum: float = 0.20  # Keep minimum 20% cash
    
    # Position sizing methodology
    base_position_size: float = 0.05  # Base 5% of account per position
    volatility_adjustment: bool = True  # Adjust size based on volatility
    confidence_scaling: bool = True  # Scale size based on setup confidence
    
    # Profit taking rules
    systematic_profit_taking: bool = True  # Always take profits systematically
    profit_target_percentage: float = 0.50  # 50% of maximum profit
    never_let_winner_become_loser: bool = True  # Erica's key rule
    
    # Loss management
    stop_loss_methodology: str = "premium_based"  # Based on premium collected/paid
    max_loss_multiplier: float = 2.0  # Max loss = 2x premium collected
    early_exit_signals: List[str] = None  # Signals to exit early
    
    # Market condition adjustments
    reduce_size_high_uncertainty: bool = True  # Reduce size in uncertain markets
    increase_size_high_confidence: bool = True  # Increase size in high confidence setups
    
    def __post_init__(self):
        if self.early_exit_signals is None:
            self.early_exit_signals = [
                "thesis_invalidated",
                "market_regime_change",
                "volatility_spike",
                "news_driven_move"
            ]

class EricaDecisionFramework:
    """Complete implementation of Erica's decision-making framework"""
    
    def __init__(self):
        self.strategy_rules = EricaStrategyRules()
        self.timing_rules = EricaMarketTimingRules()
        self.risk_rules = EricaRiskManagementRules()
        
    def evaluate_strategy_suitability(self, strategy: StrategyType, 
                                    market_factors: MarketFactors,
                                    stock_factors: StockSpecificFactors,
                                    symbol: str) -> Tuple[float, List[str]]:
        """
        Evaluate strategy suitability using Erica's exact criteria
        
        Returns:
            Tuple of (suitability_score, reasoning_list)
        """
        
        score = 0.0
        reasoning = []
        
        if strategy == StrategyType.COVERED_CALL:
            score, reasoning = self._evaluate_covered_call_suitability(
                market_factors, stock_factors, symbol
            )
        elif strategy == StrategyType.CREDIT_SPREAD:
            score, reasoning = self._evaluate_credit_spread_suitability(
                market_factors, stock_factors, symbol
            )
        elif strategy == StrategyType.LEAPS:
            score, reasoning = self._evaluate_leaps_suitability(
                market_factors, stock_factors, symbol
            )
        elif strategy == StrategyType.PREMIUM_SELLING:
            score, reasoning = self._evaluate_premium_selling_suitability(
                market_factors, stock_factors, symbol
            )
        
        return score, reasoning
    
    def _evaluate_covered_call_suitability(self, market_factors: MarketFactors,
                                         stock_factors: StockSpecificFactors,
                                         symbol: str) -> Tuple[float, List[str]]:
        """Evaluate covered call suitability using Erica's criteria"""
        
        score = 0.0
        reasoning = []
        
        # Rule 1: IV Rank requirement (25% weight)
        if stock_factors.iv_rank >= self.strategy_rules.cc_min_iv_rank:
            iv_score = min(1.0, stock_factors.iv_rank / 0.7)  # Scale to 70% max
            score += iv_score * 0.25
            reasoning.append(f"✓ IV rank {stock_factors.iv_rank:.0%} meets minimum {self.strategy_rules.cc_min_iv_rank:.0%}")
        else:
            reasoning.append(f"✗ IV rank {stock_factors.iv_rank:.0%} below minimum {self.strategy_rules.cc_min_iv_rank:.0%}")
        
        # Rule 2: Market environment (20% weight)
        if market_factors.market_regime in [MarketRegime.SIDEWAYS_MARKET, MarketRegime.BULL_MARKET]:
            if market_factors.trend_strength < 0.7:  # Not too strong bullish
                score += 0.20
                reasoning.append("✓ Market environment suitable (sideways to moderate bull)")
            else:
                score += 0.10
                reasoning.append("⚠ Strong bull market may limit CC effectiveness")
        else:
            score += 0.05
            reasoning.append("⚠ Bear market not ideal for covered calls")
        
        # Rule 3: Volatility environment (20% weight)
        if market_factors.volatility_factors > 0.6:
            vol_score = min(1.0, market_factors.volatility_factors)
            score += vol_score * 0.20
            reasoning.append("✓ High volatility environment favors premium collection")
        else:
            score += market_factors.volatility_factors * 0.20
            reasoning.append("⚠ Lower volatility reduces premium collection opportunity")
        
        # Rule 4: Stock-specific factors (20% weight)
        if stock_factors.technical_confluence_score > 0.6:
            score += 0.15
            reasoning.append("✓ Strong technical setup supports covered call strategy")
        elif stock_factors.technical_confluence_score < 0.4:
            score += 0.05
            reasoning.append("⚠ Weak technicals may indicate trending move")
        else:
            score += 0.10
        
        # Rule 5: Earnings proximity (15% weight)
        if stock_factors.earnings_days_away:
            if stock_factors.earnings_days_away > 21:
                score += 0.15
                reasoning.append("✓ Sufficient time before earnings")
            elif stock_factors.earnings_days_away > 7:
                score += 0.10
                reasoning.append("⚠ Earnings approaching - monitor IV crush opportunity")
            else:
                score += 0.05
                reasoning.append("⚠ Very close to earnings - high risk")
        else:
            score += 0.15  # No earnings in sight
        
        return min(score, 1.0), reasoning
    
    def _evaluate_credit_spread_suitability(self, market_factors: MarketFactors,
                                          stock_factors: StockSpecificFactors,
                                          symbol: str) -> Tuple[float, List[str]]:
        """Evaluate credit spread suitability using Erica's criteria"""
        
        score = 0.0
        reasoning = []
        
        # Rule 1: Bullish bias requirement (30% weight)
        if market_factors.bullish_factors > 0.6:
            bullish_score = min(1.0, market_factors.bullish_factors)
            score += bullish_score * 0.30
            reasoning.append("✓ Strong bullish market bias supports credit spreads")
        else:
            reasoning.append("✗ Insufficient bullish bias for credit spreads")
        
        # Rule 2: IV Rank requirement (25% weight)
        if stock_factors.iv_rank >= self.strategy_rules.cs_min_iv_rank:
            iv_score = min(1.0, stock_factors.iv_rank / 0.8)
            score += iv_score * 0.25
            reasoning.append(f"✓ IV rank {stock_factors.iv_rank:.0%} meets minimum {self.strategy_rules.cs_min_iv_rank:.0%}")
        else:
            reasoning.append(f"✗ IV rank {stock_factors.iv_rank:.0%} below minimum {self.strategy_rules.cs_min_iv_rank:.0%}")
        
        # Rule 3: Technical setup (20% weight)
        if stock_factors.technical_confluence_score > 0.6:
            score += 0.20
            reasoning.append("✓ Strong technical setup supports bullish bias")
        elif stock_factors.technical_confluence_score > 0.4:
            score += 0.10
            reasoning.append("⚠ Mixed technical signals")
        else:
            reasoning.append("✗ Weak technical setup contradicts bullish bias")
        
        # Rule 4: Support level clarity (15% weight)
        if stock_factors.support_resistance_clarity > 0.7:
            score += 0.15
            reasoning.append("✓ Clear support levels for strike selection")
        else:
            score += stock_factors.support_resistance_clarity * 0.15
            reasoning.append("⚠ Support levels not clearly defined")
        
        # Rule 5: Market volatility (10% weight)
        if market_factors.volatility_factors > 0.6:
            score += 0.10
            reasoning.append("✓ High volatility supports premium collection")
        else:
            score += market_factors.volatility_factors * 0.10
        
        return min(score, 1.0), reasoning
    
    def _evaluate_leaps_suitability(self, market_factors: MarketFactors,
                                  stock_factors: StockSpecificFactors,
                                  symbol: str) -> Tuple[float, List[str]]:
        """Evaluate LEAPS suitability using Erica's criteria"""
        
        score = 0.0
        reasoning = []
        
        # Rule 1: Strong bullish trend requirement (35% weight)
        if (market_factors.market_regime == MarketRegime.BULL_MARKET and 
            market_factors.trend_strength > 0.6):
            trend_score = min(1.0, market_factors.trend_strength)
            score += trend_score * 0.35
            reasoning.append("✓ Strong bullish trend supports long-term bullish strategy")
        else:
            reasoning.append("✗ Insufficient bullish trend for LEAPS strategy")
        
        # Rule 2: Low IV environment preferred (25% weight)
        if stock_factors.iv_rank < self.strategy_rules.leaps_max_iv_rank:
            iv_score = 1.0 - (stock_factors.iv_rank / self.strategy_rules.leaps_max_iv_rank)
            score += iv_score * 0.25
            reasoning.append(f"✓ Low IV rank {stock_factors.iv_rank:.0%} favorable for option buying")
        else:
            reasoning.append(f"✗ High IV rank {stock_factors.iv_rank:.0%} makes LEAPS expensive")
        
        # Rule 3: Relative strength (20% weight)
        if stock_factors.relative_strength_vs_spy > 0.2:
            rs_score = min(1.0, stock_factors.relative_strength_vs_spy / 0.5)
            score += rs_score * 0.20
            reasoning.append("✓ Strong relative strength vs market")
        else:
            reasoning.append("⚠ Weak relative strength vs market")
        
        # Rule 4: Technical confluence (15% weight)
        if stock_factors.technical_confluence_score > 0.7:
            score += 0.15
            reasoning.append("✓ Strong technical setup supports long-term bullish view")
        else:
            score += stock_factors.technical_confluence_score * 0.15
        
        # Rule 5: Low uncertainty environment (5% weight)
        if market_factors.uncertainty_factors < 0.4:
            score += 0.05
            reasoning.append("✓ Low uncertainty environment supports long-term strategies")
        else:
            reasoning.append("⚠ High uncertainty may affect long-term outlook")
        
        return min(score, 1.0), reasoning
    
    def _evaluate_premium_selling_suitability(self, market_factors: MarketFactors,
                                            stock_factors: StockSpecificFactors,
                                            symbol: str) -> Tuple[float, List[str]]:
        """Evaluate premium selling suitability using Erica's criteria"""
        
        score = 0.0
        reasoning = []
        
        # Rule 1: High IV rank requirement (40% weight)
        if stock_factors.iv_rank >= self.strategy_rules.ps_min_iv_rank:
            iv_score = min(1.0, stock_factors.iv_rank / 0.9)
            score += iv_score * 0.40
            reasoning.append(f"✓ High IV rank {stock_factors.iv_rank:.0%} excellent for premium selling")
        else:
            reasoning.append(f"✗ IV rank {stock_factors.iv_rank:.0%} below minimum {self.strategy_rules.ps_min_iv_rank:.0%}")
        
        # Rule 2: High volatility environment (30% weight)
        if market_factors.volatility_factors > 0.7:
            vol_score = min(1.0, market_factors.volatility_factors)
            score += vol_score * 0.30
            reasoning.append("✓ High volatility environment ideal for systematic premium selling")
        else:
            score += market_factors.volatility_factors * 0.30
            reasoning.append("⚠ Lower volatility reduces premium selling opportunity")
        
        # Rule 3: Market regime compatibility (20% weight)
        if market_factors.market_regime in [MarketRegime.SIDEWAYS_MARKET, MarketRegime.BEAR_MARKET]:
            score += 0.20
            reasoning.append("✓ Market regime favorable for premium selling")
        elif market_factors.market_regime == MarketRegime.BULL_MARKET and market_factors.trend_strength < 0.7:
            score += 0.15
            reasoning.append("✓ Moderate bull market acceptable for premium selling")
        else:
            score += 0.05
            reasoning.append("⚠ Strong bull market may limit premium selling effectiveness")
        
        # Rule 4: Systematic opportunity (10% weight)
        if hasattr(stock_factors, 'wheel_suitability_score'):
            wheel_score = getattr(stock_factors, 'wheel_suitability_score', 0.5)
            score += wheel_score * 0.10
            reasoning.append("✓ Good candidate for systematic wheel strategy")
        else:
            score += 0.05
        
        return min(score, 1.0), reasoning
    
    def assess_market_environment(self, market_factors: MarketFactors) -> EricaMarketEnvironment:
        """Assess current market environment using Erica's framework"""
        
        # High volatility environment
        if market_factors.volatility_factors > 0.7 or market_factors.vix_level > 25:
            return EricaMarketEnvironment.HIGH_VOLATILITY
        
        # Market stress conditions
        if market_factors.vix_level > 30 or market_factors.uncertainty_factors > 0.8:
            return EricaMarketEnvironment.MARKET_STRESS
        
        # Earnings season
        if market_factors.earnings_season_intensity > 0.7:
            return EricaMarketEnvironment.EARNINGS_SEASON
        
        # Low volatility
        if market_factors.volatility_factors < 0.3 or market_factors.vix_level < 15:
            return EricaMarketEnvironment.LOW_VOLATILITY
        
        # Normal volatility
        return EricaMarketEnvironment.NORMAL_VOLATILITY
    
    def calculate_position_size(self, strategy: StrategyType, account_size: float,
                              confidence_score: float, market_factors: MarketFactors,
                              stock_factors: StockSpecificFactors) -> Tuple[float, str]:
        """Calculate position size using Erica's methodology"""
        
        # Start with base position size
        base_size = account_size * self.risk_rules.base_position_size
        
        # Confidence adjustment
        if self.risk_rules.confidence_scaling:
            confidence_multiplier = 0.5 + (confidence_score * 1.0)  # 0.5x to 1.5x
            base_size *= confidence_multiplier
        
        # Volatility adjustment
        if self.risk_rules.volatility_adjustment:
            if stock_factors.iv_rank > 0.8:
                base_size *= 0.8  # Reduce size for very high volatility
            elif stock_factors.iv_rank < 0.3:
                base_size *= 1.1  # Increase size for low volatility
        
        # Market uncertainty adjustment
        if self.risk_rules.reduce_size_high_uncertainty:
            if market_factors.uncertainty_factors > 0.7:
                base_size *= 0.7
        
        # Strategy-specific adjustments
        if strategy == StrategyType.LEAPS:
            base_size *= 0.8  # Smaller size for leveraged strategies
        elif strategy == StrategyType.PREMIUM_SELLING:
            base_size *= 1.1  # Slightly larger for income strategies
        
        # Ensure we don't exceed risk limits
        max_position = account_size * 0.10  # Never more than 10% per position
        final_size = min(base_size, max_position)
        
        # Generate reasoning
        reasoning = f"Base: {self.risk_rules.base_position_size:.1%} of account"
        if confidence_score > 0.7:
            reasoning += f", increased for high confidence ({confidence_score:.0%})"
        elif confidence_score < 0.5:
            reasoning += f", reduced for low confidence ({confidence_score:.0%})"
        
        if market_factors.uncertainty_factors > 0.7:
            reasoning += ", reduced for market uncertainty"
        
        return final_size, reasoning
    
    def get_profit_taking_rules(self, strategy: StrategyType) -> Dict[str, any]:
        """Get Erica's profit taking rules for each strategy"""
        
        base_rules = {
            'systematic_approach': self.risk_rules.systematic_profit_taking,
            'profit_target': self.risk_rules.profit_target_percentage,
            'never_let_winner_become_loser': self.risk_rules.never_let_winner_become_loser
        }
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            base_rules.update({
                'profit_target': 0.50,  # 50% of max profit
                'management_dte': 21,   # Manage at 21 DTE
                'early_exit_conditions': [
                    'reached_profit_target',
                    'approaching_management_dte',
                    'thesis_invalidated'
                ]
            })
        elif strategy == StrategyType.LEAPS:
            base_rules.update({
                'profit_target': 1.00,  # 100% gain
                'stop_loss': 0.50,      # 50% of premium paid
                'hold_period': '6-12 months',
                'early_exit_conditions': [
                    'reached_profit_target',
                    'trend_reversal',
                    'fundamental_change'
                ]
            })
        
        return base_rules
    
    def get_strategy_priority_ranking(self, market_factors: MarketFactors) -> List[StrategyType]:
        """Get Erica's strategy priority ranking for current market environment"""
        
        environment = self.assess_market_environment(market_factors)
        
        if environment == EricaMarketEnvironment.HIGH_VOLATILITY:
            return [
                StrategyType.PREMIUM_SELLING,
                StrategyType.COVERED_CALL,
                StrategyType.CREDIT_SPREAD,
                StrategyType.LEAPS
            ]
        elif environment == EricaMarketEnvironment.LOW_VOLATILITY:
            return [
                StrategyType.LEAPS,
                StrategyType.CREDIT_SPREAD,
                StrategyType.COVERED_CALL,
                StrategyType.PREMIUM_SELLING
            ]
        elif environment == EricaMarketEnvironment.MARKET_STRESS:
            return [
                StrategyType.PREMIUM_SELLING,
                StrategyType.COVERED_CALL,
                StrategyType.CREDIT_SPREAD,
                StrategyType.LEAPS
            ]
        else:  # Normal volatility or earnings season
            return [
                StrategyType.COVERED_CALL,
                StrategyType.CREDIT_SPREAD,
                StrategyType.PREMIUM_SELLING,
                StrategyType.LEAPS
            ]
