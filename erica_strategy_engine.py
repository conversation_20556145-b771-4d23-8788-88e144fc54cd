"""
<PERSON>'s Strategy Engine - EXACT Implementation
Following AbundantlyErica's (YouTube) precise strategy rules and parameters

This module implements <PERSON>'s exact methodology including:
1. Precise delta targets, DTE ranges, and profit targets
2. Ticker-specific overrides for AMD, NVDA, GOOGL, AMZN, AAPL
3. Earnings proximity rules and IV rank thresholds
4. Expected move calculations and strike selection
5. Roll management and risk controls
6. All strategy variants (CC, CSP, Spreads, LEAPS, Wheel)

Date: August 18, 2025
"""

from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import logging
import math

class EricaStrategy(Enum):
    BASELINE_CC = "baseline_cc"
    FAST_MONEY_CC = "fast_money_cc"
    EARNINGS_CC = "earnings_cc"
    NEAR_EARNINGS_CC = "near_earnings_cc"
    BEAR_CALL_SPREAD = "bear_call_spread"
    PUT_CREDIT_SPREAD = "put_credit_spread"
    BULLISH_CALL_SPREAD = "bullish_call_spread"
    LEAPS = "leaps"
    DIAGONAL_CALENDAR = "diagonal_calendar"
    CASH_SECURED_PUT = "cash_secured_put"
    THE_WHEEL = "the_wheel"

class MarketBias(Enum):
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    MILDLY_BULLISH = "mildly_bullish"
    BULLISH = "bullish"
    LONG_TERM_BULLISH = "long_term_bullish"

@dataclass
class EricaSignals:
    """Core market signals per Erica's methodology"""
    spot_price: float
    earnings_days_away: int
    iv_rank: float  # 0-100
    iv_percentile: float  # 0-100
    expected_move: Optional[float]
    atr: float
    support_level: Optional[float]
    resistance_level: Optional[float]
    
    # Derived signals
    @property
    def is_near_earnings(self) -> bool:
        return self.earnings_days_away <= 10
    
    @property
    def is_post_earnings(self) -> bool:
        return -3 <= self.earnings_days_away <= -1
    
    @property
    def is_iv_elevated(self) -> bool:
        return self.iv_rank >= 50

@dataclass
class EricaParameters:
    """Erica's exact strategy parameters"""
    
    # Global defaults from Erica's JSON
    SIGNALS = {
        "nearEarningsDays": 10,
        "ivRankHigh": 50,
        "atrLookback": 14
    }
    
    COVERED_CALLS = {
        "baseline": {"targetDelta": [0.20, 0.30], "dte": [7, 21]},
        "fastMoney": {"targetDelta": [0.25, 0.30], "dte": [0, 3]},
        "earnings": {"targetDelta": [0.10, 0.20], "dte": [5, 12], "useEMGuardrail": True, "bufferPct": 0.0},
        "nearEarningsConservative": {"targetDeltaMax": 0.10, "dte": [7, 14], "useEMGuardrail": True, "bufferPct": 0.10}
    }
    
    ROLLS = {
        "deltaBreach": 0.35,
        "timeFloorDTE": 5,
        "priceProximityATR": 1.0
    }
    
    SPREADS = {
        "bcs": {"shortDelta": [0.20, 0.30], "dte": [14, 35], "takeProfitPct": [0.5, 0.7]},
        "pcs": {"shortDelta": [0.20, 0.30], "dte": [14, 35], "takeProfitPct": [0.5, 0.7]},
        "bullCall": {"dte": [30, 60]}
    }
    
    LEAPS = {
        "delta": [0.60, 0.75],
        "tenorDays": [270, 540],
        "sellCalls": {"dte": [14, 30], "targetDelta": [0.20, 0.30]}
    }
    
    CSP_WHEEL = {
        "cspDelta": [0.20, 0.30],
        "dte": [7, 21],
        "wheel": True
    }
    
    # Ticker-specific overrides (Erica's exact rules)
    TICKER_OVERRIDES = {
        "AMD": {"earningsDeltaMax": 0.18, "spreadWidth": 10},
        "NVDA": {"earningsDeltaMax": 0.15, "spreadWidth": 20, "takeProfitPct": 0.5},
        "GOOGL": {"nearEarningsBufferPct": 0.12, "ccDeltaMax": 0.20},
        "AMZN": {"earningsDeltaMax": 0.18, "wheelPreferred": True},
        "AAPL": {"fastMoneyPreferred": True, "ccDelta": [0.22, 0.30]}
    }
    
    RISK = {
        "maxPerTradeLossPct": 0.02,
        "maxTickerExposurePct": 0.25
    }

@dataclass
class EricaTradeSetup:
    """Complete trade setup following Erica's methodology"""
    strategy: EricaStrategy
    symbol: str
    
    # Entry parameters
    short_strike: Optional[float]
    long_strike: Optional[float]
    target_delta: float
    dte: int
    
    # Risk/Reward
    max_profit: float
    max_loss: float
    breakeven: float
    credit_received: Optional[float]
    debit_paid: Optional[float]
    
    # Management rules
    profit_target_pct: float  # 50-70% for credits
    time_exit_dte: int  # Exit by 5 DTE if unclear
    roll_trigger_delta: float  # 0.35-0.40
    
    # Erica's specific rules
    reasoning: str
    risk_factors: List[str]
    management_notes: str
    ticker_specific_adjustments: Dict[str, Any]

class EricaStrategyEngine:
    """Exact implementation of Erica's trading strategies"""
    
    def __init__(self):
        self.params = EricaParameters()
        self.logger = logging.getLogger(__name__)
    
    def analyze_setup(self, symbol: str, signals: EricaSignals, 
                     market_bias: MarketBias, account_size: float = 100000) -> List[EricaTradeSetup]:
        """
        Analyze and recommend strategies following Erica's exact methodology
        
        Args:
            symbol: Stock symbol (AMD, NVDA, GOOGL, AMZN, AAPL)
            signals: Current market signals
            market_bias: Current market bias assessment
            account_size: Account size for position sizing
            
        Returns:
            List of recommended trade setups ranked by Erica's preferences
        """
        
        setups = []
        
        # Get ticker-specific overrides
        ticker_overrides = self.params.TICKER_OVERRIDES.get(symbol, {})
        
        # Covered Call Analysis
        cc_setups = self._analyze_covered_calls(symbol, signals, market_bias, ticker_overrides)
        setups.extend(cc_setups)
        
        # Spread Analysis
        spread_setups = self._analyze_spreads(symbol, signals, market_bias, ticker_overrides)
        setups.extend(spread_setups)
        
        # LEAPS Analysis
        if market_bias in [MarketBias.BULLISH, MarketBias.LONG_TERM_BULLISH]:
            leaps_setup = self._analyze_leaps(symbol, signals, ticker_overrides)
            if leaps_setup:
                setups.append(leaps_setup)
        
        # CSP/Wheel Analysis
        csp_setup = self._analyze_csp_wheel(symbol, signals, market_bias, ticker_overrides)
        if csp_setup:
            setups.append(csp_setup)
        
        # Apply Erica's ranking preferences
        ranked_setups = self._rank_setups_erica_style(setups, symbol, signals, market_bias)
        
        return ranked_setups
    
    def _analyze_covered_calls(self, symbol: str, signals: EricaSignals, 
                              market_bias: MarketBias, ticker_overrides: Dict) -> List[EricaTradeSetup]:
        """Analyze covered call opportunities per Erica's rules"""
        
        setups = []
        
        # Baseline Covered Call
        if market_bias in [MarketBias.NEUTRAL, MarketBias.MILDLY_BULLISH] and not signals.is_near_earnings:
            setup = self._create_baseline_cc(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)
        
        # Fast Money CC (Erica's favorite for AAPL)
        if (ticker_overrides.get("fastMoneyPreferred", False) or 
            not signals.is_near_earnings and signals.iv_rank < 30):
            setup = self._create_fast_money_cc(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)
        
        # Earnings CC (IV capture)
        if 5 <= signals.earnings_days_away <= 12 and signals.is_iv_elevated:
            setup = self._create_earnings_cc(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)
        
        # Near-Earnings Conservative CC
        if signals.is_near_earnings and market_bias != MarketBias.BEARISH:
            setup = self._create_near_earnings_cc(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)
        
        return setups
    
    def _create_baseline_cc(self, symbol: str, signals: EricaSignals, 
                           ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create baseline covered call per Erica's rules"""
        
        # Get parameters with ticker overrides
        cc_params = self.params.COVERED_CALLS["baseline"].copy()
        
        # Apply AAPL override
        if symbol == "AAPL" and "ccDelta" in ticker_overrides:
            cc_params["targetDelta"] = ticker_overrides["ccDelta"]
        
        # Calculate strike
        target_delta = (cc_params["targetDelta"][0] + cc_params["targetDelta"][1]) / 2
        dte = cc_params["dte"][1]  # Use longer DTE for baseline
        
        # Strike selection: 3-10% OTM or delta target
        otm_pct = 0.05  # 5% OTM as middle ground
        strike_by_pct = signals.spot_price * (1 + otm_pct)
        
        # Use the more conservative (higher) strike
        short_strike = max(strike_by_pct, self._delta_to_strike(signals.spot_price, target_delta, dte, True))
        
        # Calculate metrics
        credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, True)
        max_profit = credit * 100
        max_loss = float('inf')  # Unlimited downside on shares
        breakeven = signals.spot_price - credit
        
        return EricaTradeSetup(
            strategy=EricaStrategy.BASELINE_CC,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=None,
            target_delta=target_delta,
            dte=dte,
            max_profit=max_profit,
            max_loss=max_loss,
            breakeven=breakeven,
            credit_received=credit,
            debit_paid=None,
            profit_target_pct=0.6,  # 60% profit target
            time_exit_dte=5,
            roll_trigger_delta=0.35,
            reasoning=f"Baseline CC for {symbol}: Neutral to mildly bullish, no imminent catalyst",
            risk_factors=["Upside limitation", "Assignment risk above strike"],
            management_notes="Roll up/out for credit if threatened ITM and want to keep shares",
            ticker_specific_adjustments=ticker_overrides
        )

    def _create_fast_money_cc(self, symbol: str, signals: EricaSignals,
                             ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create fast money CC (0-3 DTE) per Erica's rules"""

        cc_params = self.params.COVERED_CALLS["fastMoney"]
        target_delta = (cc_params["targetDelta"][0] + cc_params["targetDelta"][1]) / 2
        dte = 1  # Typically Thu->Fri

        short_strike = self._delta_to_strike(signals.spot_price, target_delta, dte, True)
        credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, True)

        return EricaTradeSetup(
            strategy=EricaStrategy.FAST_MONEY_CC,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=None,
            target_delta=target_delta,
            dte=dte,
            max_profit=credit * 100,
            max_loss=float('inf'),
            breakeven=signals.spot_price - credit,
            credit_received=credit,
            debit_paid=None,
            profit_target_pct=0.7,  # 70% or same-day exit
            time_exit_dte=0,  # Exit same day/EOD
            roll_trigger_delta=0.40,  # Higher threshold for short DTE
            reasoning=f"Fast money CC for {symbol}: Quick cash flow, no catalyst",
            risk_factors=["Rapid gamma risk", "Assignment risk"],
            management_notes="Exit same-day or by EOD, 50-70% capture",
            ticker_specific_adjustments=ticker_overrides
        )

    def _create_earnings_cc(self, symbol: str, signals: EricaSignals,
                           ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create earnings CC (IV capture) per Erica's rules"""

        cc_params = self.params.COVERED_CALLS["earnings"]

        # Apply ticker-specific delta overrides
        if symbol in ["AMD", "NVDA", "AMZN"] and "earningsDeltaMax" in ticker_overrides:
            max_delta = ticker_overrides["earningsDeltaMax"]
        else:
            max_delta = cc_params["targetDelta"][1]  # 0.20

        target_delta = min(max_delta, 0.15)  # Conservative for earnings
        dte = signals.earnings_days_away

        # Strike selection: Beyond expected move or delta target
        if signals.expected_move and cc_params["useEMGuardrail"]:
            em_strike = signals.spot_price + signals.expected_move
            delta_strike = self._delta_to_strike(signals.spot_price, target_delta, dte, True)
            short_strike = max(em_strike, delta_strike)
        else:
            short_strike = self._delta_to_strike(signals.spot_price, target_delta, dte, True)

        credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, True)

        return EricaTradeSetup(
            strategy=EricaStrategy.EARNINGS_CC,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=None,
            target_delta=target_delta,
            dte=dte,
            max_profit=credit * 100,
            max_loss=float('inf'),
            breakeven=signals.spot_price - credit,
            credit_received=credit,
            debit_paid=None,
            profit_target_pct=0.8,  # Higher target for IV crush
            time_exit_dte=1,  # Exit morning after earnings
            roll_trigger_delta=0.30,  # Lower threshold near earnings
            reasoning=f"Earnings CC for {symbol}: Capture IV crush, expire after earnings",
            risk_factors=["Earnings gap risk", "IV crush timing", "Assignment if ITM"],
            management_notes="Close/roll morning after earnings to realize IV crush",
            ticker_specific_adjustments=ticker_overrides
        )

    def _create_near_earnings_cc(self, symbol: str, signals: EricaSignals,
                                ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create near-earnings conservative CC per Erica's rules"""

        cc_params = self.params.COVERED_CALLS["nearEarningsConservative"]

        # Apply GOOGL override
        if symbol == "GOOGL" and "nearEarningsBufferPct" in ticker_overrides:
            buffer_pct = ticker_overrides["nearEarningsBufferPct"]
        else:
            buffer_pct = cc_params["bufferPct"]  # 0.10

        max_delta = cc_params["targetDeltaMax"]  # 0.10
        dte = min(signals.earnings_days_away + 7, 14)  # Extend past earnings

        # Strike selection: EM + buffer or very low delta
        if signals.expected_move:
            em_strike = signals.spot_price + signals.expected_move * (1 + buffer_pct)
            delta_strike = self._delta_to_strike(signals.spot_price, max_delta, dte, True)
            short_strike = max(em_strike, delta_strike)
        else:
            short_strike = self._delta_to_strike(signals.spot_price, max_delta, dte, True)

        credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, True)

        return EricaTradeSetup(
            strategy=EricaStrategy.NEAR_EARNINGS_CC,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=None,
            target_delta=max_delta,
            dte=dte,
            max_profit=credit * 100,
            max_loss=float('inf'),
            breakeven=signals.spot_price - credit,
            credit_received=credit,
            debit_paid=None,
            profit_target_pct=0.5,  # Conservative target
            time_exit_dte=5,
            roll_trigger_delta=0.25,  # Very conservative
            reasoning=f"Near-earnings CC for {symbol}: Priority on keeping shares through report",
            risk_factors=["Earnings volatility", "Limited upside capture"],
            management_notes="Let expire if OTM, roll on strength if needed",
            ticker_specific_adjustments=ticker_overrides
        )

    def _analyze_spreads(self, symbol: str, signals: EricaSignals,
                        market_bias: MarketBias, ticker_overrides: Dict) -> List[EricaTradeSetup]:
        """Analyze spread opportunities per Erica's rules"""

        setups = []

        # Bear Call Spread
        if (market_bias in [MarketBias.NEUTRAL, MarketBias.BEARISH] and
            signals.resistance_level and signals.is_iv_elevated):
            setup = self._create_bear_call_spread(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)

        # Put Credit Spread
        if (market_bias in [MarketBias.BULLISH, MarketBias.MILDLY_BULLISH] and
            signals.support_level and signals.is_iv_elevated):
            setup = self._create_put_credit_spread(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)

        # Bullish Call Spread (debit)
        if market_bias == MarketBias.BULLISH and not signals.is_iv_elevated:
            setup = self._create_bullish_call_spread(symbol, signals, ticker_overrides)
            if setup:
                setups.append(setup)

        return setups

    def _create_bear_call_spread(self, symbol: str, signals: EricaSignals,
                                ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create bear call spread per Erica's rules"""

        spread_params = self.params.SPREADS["bcs"]
        target_delta = (spread_params["shortDelta"][0] + spread_params["shortDelta"][1]) / 2
        dte = 21  # Middle of 14-35 range

        # Short call just above resistance
        if signals.resistance_level:
            short_strike = max(signals.resistance_level,
                             self._delta_to_strike(signals.spot_price, target_delta, dte, True))
        else:
            short_strike = self._delta_to_strike(signals.spot_price, target_delta, dte, True)

        # Width based on ticker overrides
        width = ticker_overrides.get("spreadWidth", 10)
        long_strike = short_strike + width

        # Calculate spread metrics
        short_credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, True)
        long_debit = self._estimate_option_premium(signals.spot_price, long_strike, dte, signals.iv_rank, True)
        net_credit = short_credit - long_debit

        max_profit = net_credit * 100
        max_loss = (width - net_credit) * 100
        breakeven = short_strike + net_credit

        # Apply NVDA take profit override
        profit_target = ticker_overrides.get("takeProfitPct", 0.6)

        return EricaTradeSetup(
            strategy=EricaStrategy.BEAR_CALL_SPREAD,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=long_strike,
            target_delta=target_delta,
            dte=dte,
            max_profit=max_profit,
            max_loss=max_loss,
            breakeven=breakeven,
            credit_received=net_credit,
            debit_paid=None,
            profit_target_pct=profit_target,
            time_exit_dte=5,
            roll_trigger_delta=0.35,
            reasoning=f"Bear call spread for {symbol}: Neutral to bearish near resistance",
            risk_factors=["Upside breakout risk", "Assignment risk on short leg"],
            management_notes="Roll out/up if thesis intact, otherwise cut at planned loss",
            ticker_specific_adjustments=ticker_overrides
        )

    def _create_put_credit_spread(self, symbol: str, signals: EricaSignals,
                                 ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Create put credit spread per Erica's rules"""

        spread_params = self.params.SPREADS["pcs"]
        target_delta = (spread_params["shortDelta"][0] + spread_params["shortDelta"][1]) / 2
        dte = 21

        # Short put above/at support
        if signals.support_level:
            short_strike = min(signals.support_level,
                             self._delta_to_strike(signals.spot_price, target_delta, dte, False))
        else:
            short_strike = self._delta_to_strike(signals.spot_price, target_delta, dte, False)

        width = ticker_overrides.get("spreadWidth", 10)
        long_strike = short_strike - width

        short_credit = self._estimate_option_premium(signals.spot_price, short_strike, dte, signals.iv_rank, False)
        long_debit = self._estimate_option_premium(signals.spot_price, long_strike, dte, signals.iv_rank, False)
        net_credit = short_credit - long_debit

        max_profit = net_credit * 100
        max_loss = (width - net_credit) * 100
        breakeven = short_strike - net_credit

        profit_target = ticker_overrides.get("takeProfitPct", 0.6)

        return EricaTradeSetup(
            strategy=EricaStrategy.PUT_CREDIT_SPREAD,
            symbol=symbol,
            short_strike=short_strike,
            long_strike=long_strike,
            target_delta=target_delta,
            dte=dte,
            max_profit=max_profit,
            max_loss=max_loss,
            breakeven=breakeven,
            credit_received=net_credit,
            debit_paid=None,
            profit_target_pct=profit_target,
            time_exit_dte=5,
            roll_trigger_delta=0.35,
            reasoning=f"Put credit spread for {symbol}: Bullish with defined risk near support",
            risk_factors=["Downside break risk", "Assignment risk on short leg"],
            management_notes="Roll down/out for credit if support holds, otherwise exit",
            ticker_specific_adjustments=ticker_overrides
        )

    def _analyze_leaps(self, symbol: str, signals: EricaSignals,
                      ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Analyze LEAPS per Erica's rules"""

        leaps_params = self.params.LEAPS
        target_delta = (leaps_params["delta"][0] + leaps_params["delta"][1]) / 2  # 0.675
        dte = leaps_params["tenorDays"][0]  # 270 days (9 months)

        # ITM strike for stability (delta 0.60-0.75)
        strike = self._delta_to_strike(signals.spot_price, target_delta, dte, True)

        # Estimate LEAPS premium
        premium = self._estimate_option_premium(signals.spot_price, strike, dte, signals.iv_rank, True)

        return EricaTradeSetup(
            strategy=EricaStrategy.LEAPS,
            symbol=symbol,
            short_strike=None,
            long_strike=strike,  # Long position
            target_delta=target_delta,
            dte=dte,
            max_profit=float('inf'),  # Unlimited upside
            max_loss=premium * 100,  # Limited to premium paid
            breakeven=strike + premium,
            credit_received=None,
            debit_paid=premium,
            profit_target_pct=0.4,  # 40% profit target
            time_exit_dte=180,  # Roll before 6 months remain
            roll_trigger_delta=0.0,  # N/A for long positions
            reasoning=f"LEAPS for {symbol}: Long-term bullish convexity over shares",
            risk_factors=["Time decay", "Volatility risk", "Trend reversal"],
            management_notes="Sell short calls against for income when IV fair (diagonal)",
            ticker_specific_adjustments=ticker_overrides
        )

    def _analyze_csp_wheel(self, symbol: str, signals: EricaSignals,
                          market_bias: MarketBias, ticker_overrides: Dict) -> Optional[EricaTradeSetup]:
        """Analyze CSP/Wheel per Erica's rules"""

        # AMZN wheel preference
        if symbol == "AMZN" and not ticker_overrides.get("wheelPreferred", False):
            return None

        # Only if willing to own lower
        if market_bias == MarketBias.BEARISH:
            return None

        csp_params = self.params.CSP_WHEEL
        target_delta = (csp_params["cspDelta"][0] + csp_params["cspDelta"][1]) / 2
        dte = csp_params["dte"][1]  # 21 days

        # Strike at/near support
        if signals.support_level:
            strike = min(signals.support_level,
                        self._delta_to_strike(signals.spot_price, target_delta, dte, False))
        else:
            strike = self._delta_to_strike(signals.spot_price, target_delta, dte, False)

        premium = self._estimate_option_premium(signals.spot_price, strike, dte, signals.iv_rank, False)

        return EricaTradeSetup(
            strategy=EricaStrategy.CASH_SECURED_PUT,
            symbol=symbol,
            short_strike=strike,
            long_strike=None,
            target_delta=target_delta,
            dte=dte,
            max_profit=premium * 100,
            max_loss=(strike - premium) * 100,  # If assigned
            breakeven=strike - premium,
            credit_received=premium,
            debit_paid=None,
            profit_target_pct=0.6,
            time_exit_dte=5,
            roll_trigger_delta=0.35,
            reasoning=f"CSP for {symbol}: Willing to own lower, part of wheel strategy",
            risk_factors=["Assignment risk", "Downside exposure if assigned"],
            management_notes="If assigned, transition to covered calls (wheel)",
            ticker_specific_adjustments=ticker_overrides
        )

    def _rank_setups_erica_style(self, setups: List[EricaTradeSetup], symbol: str,
                                signals: EricaSignals, market_bias: MarketBias) -> List[EricaTradeSetup]:
        """Rank setups according to Erica's preferences"""

        def erica_score(setup: EricaTradeSetup) -> float:
            score = 0.0

            # Erica's strategy preferences by situation
            if signals.is_near_earnings:
                if setup.strategy == EricaStrategy.EARNINGS_CC:
                    score += 10.0
                elif setup.strategy == EricaStrategy.NEAR_EARNINGS_CC:
                    score += 8.0

            # Fast money preference for AAPL
            if symbol == "AAPL" and setup.strategy == EricaStrategy.FAST_MONEY_CC:
                score += 9.0

            # Wheel preference for AMZN
            if symbol == "AMZN" and setup.strategy == EricaStrategy.CASH_SECURED_PUT:
                score += 8.0

            # IV rank considerations
            if signals.is_iv_elevated:
                if setup.strategy in [EricaStrategy.EARNINGS_CC, EricaStrategy.BEAR_CALL_SPREAD,
                                    EricaStrategy.PUT_CREDIT_SPREAD]:
                    score += 5.0

            # Risk-adjusted return
            if setup.credit_received and setup.max_loss < float('inf'):
                risk_reward = setup.max_profit / max(setup.max_loss, 1)
                score += risk_reward * 2

            # Time efficiency (Erica likes shorter DTEs for income)
            if setup.dte <= 21:
                score += 3.0

            return score

        # Sort by Erica's scoring
        ranked = sorted(setups, key=erica_score, reverse=True)
        return ranked

    # Helper methods for calculations

    def _delta_to_strike(self, spot: float, delta: float, dte: int, is_call: bool) -> float:
        """Convert delta to approximate strike price"""
        # Simplified Black-Scholes approximation
        # In production, would use proper options pricing

        if is_call:
            # Call delta is positive, higher strikes have lower delta
            otm_factor = (1 - delta) * 0.2  # Rough approximation
            return spot * (1 + otm_factor)
        else:
            # Put delta is negative, lower strikes have higher absolute delta
            otm_factor = delta * 0.2
            return spot * (1 - otm_factor)

    def _estimate_option_premium(self, spot: float, strike: float, dte: int,
                                iv_rank: float, is_call: bool) -> float:
        """Estimate option premium (simplified)"""
        # Simplified premium estimation
        # In production, would use proper Black-Scholes or market data

        time_value = (dte / 365) ** 0.5 * 0.1  # Time component
        vol_component = (iv_rank / 100) * 0.05  # Volatility component

        if is_call:
            intrinsic = max(0, spot - strike)
            moneyness_factor = max(0.01, 1 - abs(spot - strike) / spot)
        else:
            intrinsic = max(0, strike - spot)
            moneyness_factor = max(0.01, 1 - abs(spot - strike) / spot)

        premium = intrinsic + (time_value + vol_component) * spot * moneyness_factor
        return max(0.05, premium)  # Minimum premium

    def check_roll_conditions(self, setup: EricaTradeSetup, current_signals: EricaSignals) -> Dict[str, Any]:
        """Check if position should be rolled per Erica's rules"""

        roll_conditions = {
            "should_roll": False,
            "roll_type": None,
            "reason": None,
            "new_strike": None,
            "new_dte": None
        }

        # Delta breach check
        current_delta = self._calculate_current_delta(setup, current_signals)
        if current_delta > setup.roll_trigger_delta:
            roll_conditions.update({
                "should_roll": True,
                "roll_type": "roll_up_and_out",
                "reason": f"Delta breach: {current_delta:.2f} > {setup.roll_trigger_delta:.2f}"
            })

        # Time decay check
        if setup.dte <= self.params.ROLLS["timeFloorDTE"]:
            if setup.credit_received and setup.credit_received > 0.10:  # Still significant value
                roll_conditions.update({
                    "should_roll": True,
                    "roll_type": "roll_out",
                    "reason": f"Time floor reached: {setup.dte} DTE"
                })

        # Price proximity check
        if setup.short_strike:
            price_distance = abs(current_signals.spot_price - setup.short_strike)
            atr_threshold = current_signals.atr * self.params.ROLLS["priceProximityATR"]

            if price_distance <= atr_threshold:
                roll_conditions.update({
                    "should_roll": True,
                    "roll_type": "roll_out",
                    "reason": f"Price proximity: {price_distance:.2f} <= {atr_threshold:.2f} ATR"
                })

        return roll_conditions

    def _calculate_current_delta(self, setup: EricaTradeSetup, current_signals: EricaSignals) -> float:
        """Calculate current delta of position"""
        # Simplified delta calculation
        if not setup.short_strike:
            return 0.0

        # Rough delta approximation based on moneyness
        if setup.strategy.value.endswith("_cc"):  # Call options
            if current_signals.spot_price > setup.short_strike:
                return min(0.99, 0.5 + (current_signals.spot_price - setup.short_strike) / setup.short_strike)
            else:
                return max(0.01, 0.5 - (setup.short_strike - current_signals.spot_price) / setup.short_strike)
        else:  # Put options
            if current_signals.spot_price < setup.short_strike:
                return min(-0.01, -0.5 - (setup.short_strike - current_signals.spot_price) / setup.short_strike)
            else:
                return max(-0.99, -0.5 + (current_signals.spot_price - setup.short_strike) / setup.short_strike)


def create_erica_engine() -> EricaStrategyEngine:
    """Factory function to create Erica's strategy engine"""
    return EricaStrategyEngine()
