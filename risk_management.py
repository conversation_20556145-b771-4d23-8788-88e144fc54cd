"""
Risk Management System for Erica's Trading Strategies

Implements comprehensive risk management based on <PERSON>'s principles:
1. Position sizing based on account size and risk tolerance
2. Portfolio-level risk assessment and limits
3. Strategy-specific risk parameters
4. Dynamic position sizing based on volatility
5. Stop-loss and profit-taking rules
6. Correlation analysis for portfolio diversification

Key Principles from Erica's System:
- Never risk more than 2% of account on a single trade
- Diversify across strategies and timeframes
- Use systematic profit-taking rules (50% of max profit)
- Adjust position sizes based on volatility
- Maintain cash reserves for opportunities
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import math
import logging

from daily_outline import TradingSignal, StrategyType
from strategy_engine import PortfolioPosition, DailyRecommendation

@dataclass
class RiskParameters:
    """Risk management parameters for the system"""
    max_account_risk: float = 0.02  # Max 2% of account per trade
    max_portfolio_risk: float = 0.20  # Max 20% of account at risk
    max_position_size: float = 0.10  # Max 10% of account per position
    max_correlation: float = 0.70  # Max correlation between positions
    cash_reserve: float = 0.20  # Keep 20% cash reserve
    volatility_adjustment: bool = True  # Adjust size based on volatility
    
    # Strategy-specific parameters
    covered_call_max_size: float = 0.15  # Max 15% for covered calls
    credit_spread_max_size: float = 0.08  # Max 8% for credit spreads
    leaps_max_size: float = 0.05  # Max 5% for LEAPS
    premium_selling_max_size: float = 0.10  # Max 10% for premium selling

@dataclass
class PositionRisk:
    """Risk assessment for a single position"""
    symbol: str
    strategy: StrategyType
    position_size: float
    max_loss: float
    max_gain: float
    risk_reward_ratio: float
    probability_of_profit: float
    days_to_expiration: Optional[int]
    volatility_risk: str  # LOW, MODERATE, HIGH
    correlation_risk: str  # LOW, MODERATE, HIGH

@dataclass
class PortfolioRisk:
    """Overall portfolio risk assessment"""
    total_risk: float  # Total amount at risk
    risk_percentage: float  # Percentage of account at risk
    diversification_score: float  # 0-1 scale
    correlation_risk: str  # LOW, MODERATE, HIGH
    volatility_exposure: str  # LOW, MODERATE, HIGH
    strategy_concentration: Dict[StrategyType, float]
    recommendations: List[str]

class RiskManager:
    """Comprehensive risk management system"""
    
    def __init__(self, account_size: float, risk_params: RiskParameters = None):
        self.account_size = account_size
        self.risk_params = risk_params or RiskParameters()
        self.logger = logging.getLogger(__name__)
        
        # Historical volatility data for risk adjustment
        self.volatility_benchmarks = {
            'low': 0.15,
            'moderate': 0.25,
            'high': 0.40
        }
    
    def calculate_position_size(self, signal: TradingSignal, 
                               current_volatility: float,
                               existing_positions: List[PortfolioPosition] = None) -> Tuple[float, str]:
        """
        Calculate optimal position size based on risk parameters
        
        Args:
            signal: Trading signal to size
            current_volatility: Current volatility of the underlying
            existing_positions: Current portfolio positions
            
        Returns:
            Tuple of (position_size, reasoning)
        """
        
        if not signal.entry_price:
            return 0.0, "No entry price available"
        
        # Base position size calculation
        base_size = self._calculate_base_position_size(signal)
        
        # Volatility adjustment
        volatility_adjusted_size = self._adjust_for_volatility(base_size, current_volatility)
        
        # Strategy-specific limits
        strategy_limited_size = self._apply_strategy_limits(volatility_adjusted_size, signal.strategy)
        
        # Portfolio-level constraints
        portfolio_adjusted_size = self._apply_portfolio_constraints(
            strategy_limited_size, signal, existing_positions or []
        )
        
        # Final size with account limits
        final_size = min(portfolio_adjusted_size, self.account_size * self.risk_params.max_position_size)
        
        # Generate reasoning
        reasoning = self._generate_sizing_reasoning(
            signal, base_size, volatility_adjusted_size, 
            strategy_limited_size, portfolio_adjusted_size, final_size
        )
        
        return final_size, reasoning
    
    def _calculate_base_position_size(self, signal: TradingSignal) -> float:
        """Calculate base position size using Kelly Criterion approach"""
        
        if not signal.risk_reward_ratio or signal.risk_reward_ratio <= 0:
            # Default to 2% risk if no risk/reward data
            return self.account_size * self.risk_params.max_account_risk
        
        # Estimate win probability based on confidence and strategy
        win_probability = self._estimate_win_probability(signal)
        
        # Kelly Criterion: f = (bp - q) / b
        # where b = odds received (risk/reward ratio), p = win probability, q = loss probability
        b = signal.risk_reward_ratio
        p = win_probability
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # Apply Kelly fraction with safety factor (use 25% of Kelly)
        kelly_size = max(0, kelly_fraction * 0.25 * self.account_size)
        
        # Cap at maximum account risk
        max_risk_size = self.account_size * self.risk_params.max_account_risk
        
        return min(kelly_size, max_risk_size)
    
    def _estimate_win_probability(self, signal: TradingSignal) -> float:
        """Estimate win probability based on strategy and confidence"""
        
        # Base probabilities by strategy (from Erica's historical data)
        strategy_base_probs = {
            StrategyType.COVERED_CALL: 0.75,
            StrategyType.CREDIT_SPREAD: 0.70,
            StrategyType.PREMIUM_SELLING: 0.72,
            StrategyType.LEAPS: 0.60
        }
        
        base_prob = strategy_base_probs.get(signal.strategy, 0.65)
        
        # Adjust based on confidence score
        confidence_adjustment = (signal.confidence - 0.5) * 0.2  # +/- 10% max
        
        adjusted_prob = base_prob + confidence_adjustment
        
        return max(0.4, min(0.9, adjusted_prob))  # Keep between 40-90%
    
    def _adjust_for_volatility(self, base_size: float, volatility: float) -> float:
        """Adjust position size based on current volatility"""
        
        if not self.risk_params.volatility_adjustment:
            return base_size
        
        # Volatility adjustment factor
        if volatility > self.volatility_benchmarks['high']:
            # High volatility - reduce size by 30%
            adjustment_factor = 0.70
        elif volatility > self.volatility_benchmarks['moderate']:
            # Moderate volatility - reduce size by 15%
            adjustment_factor = 0.85
        elif volatility < self.volatility_benchmarks['low']:
            # Low volatility - can increase size by 20%
            adjustment_factor = 1.20
        else:
            # Normal volatility
            adjustment_factor = 1.0
        
        return base_size * adjustment_factor
    
    def _apply_strategy_limits(self, size: float, strategy: StrategyType) -> float:
        """Apply strategy-specific position size limits"""
        
        strategy_limits = {
            StrategyType.COVERED_CALL: self.risk_params.covered_call_max_size,
            StrategyType.CREDIT_SPREAD: self.risk_params.credit_spread_max_size,
            StrategyType.LEAPS: self.risk_params.leaps_max_size,
            StrategyType.PREMIUM_SELLING: self.risk_params.premium_selling_max_size
        }
        
        max_strategy_size = self.account_size * strategy_limits.get(strategy, 0.05)
        
        return min(size, max_strategy_size)
    
    def _apply_portfolio_constraints(self, size: float, signal: TradingSignal,
                                   existing_positions: List[PortfolioPosition]) -> float:
        """Apply portfolio-level constraints"""
        
        # Calculate current portfolio risk
        current_risk = sum(pos.unrealized_pnl for pos in existing_positions if pos.unrealized_pnl < 0)
        current_risk_pct = abs(current_risk) / self.account_size
        
        # Check if adding this position would exceed portfolio risk limit
        estimated_risk = size * 0.5  # Assume 50% potential loss
        new_risk_pct = (abs(current_risk) + estimated_risk) / self.account_size
        
        if new_risk_pct > self.risk_params.max_portfolio_risk:
            # Reduce size to stay within portfolio risk limit
            available_risk = self.account_size * self.risk_params.max_portfolio_risk - abs(current_risk)
            max_new_position = available_risk / 0.5  # Reverse the 50% assumption
            size = min(size, max(0, max_new_position))
        
        # Check for concentration in same symbol
        symbol_exposure = sum(pos.current_value for pos in existing_positions 
                            if pos.symbol == signal.symbol)
        symbol_exposure_pct = symbol_exposure / self.account_size
        
        if symbol_exposure_pct > 0.15:  # Max 15% per symbol
            # Reduce size to limit symbol concentration
            available_symbol_exposure = self.account_size * 0.15 - symbol_exposure
            size = min(size, max(0, available_symbol_exposure))
        
        return size
    
    def _generate_sizing_reasoning(self, signal: TradingSignal, base_size: float,
                                 volatility_size: float, strategy_size: float,
                                 portfolio_size: float, final_size: float) -> str:
        """Generate explanation for position sizing decision"""
        
        reasoning_parts = []
        
        # Base calculation
        base_pct = (base_size / self.account_size) * 100
        reasoning_parts.append(f"Base size: {base_pct:.1f}% of account")
        
        # Volatility adjustment
        if abs(volatility_size - base_size) > base_size * 0.05:
            vol_change = ((volatility_size / base_size) - 1) * 100
            reasoning_parts.append(f"Volatility adjustment: {vol_change:+.0f}%")
        
        # Strategy limits
        if strategy_size < volatility_size:
            reasoning_parts.append(f"Strategy limit applied")
        
        # Portfolio constraints
        if portfolio_size < strategy_size:
            reasoning_parts.append(f"Portfolio risk limit applied")
        
        # Final size
        final_pct = (final_size / self.account_size) * 100
        reasoning_parts.append(f"Final size: {final_pct:.1f}% of account")
        
        return " | ".join(reasoning_parts)
    
    def assess_position_risk(self, signal: TradingSignal, position_size: float,
                           volatility: float) -> PositionRisk:
        """Assess risk for a specific position"""
        
        # Calculate maximum loss and gain
        if signal.strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            # Premium selling strategies
            max_gain = signal.entry_price * position_size if signal.entry_price else 0
            max_loss = position_size * 2  # Estimate 2x premium as max loss
        elif signal.strategy == StrategyType.CREDIT_SPREAD:
            # Credit spreads have defined risk
            max_gain = signal.entry_price * position_size if signal.entry_price else 0
            max_loss = max_gain * 3  # Typical 1:3 risk/reward for credit spreads
        else:  # LEAPS
            max_gain = position_size * 2  # 100% gain potential
            max_loss = position_size  # Can lose entire premium
        
        # Risk/reward ratio
        risk_reward = max_gain / max_loss if max_loss > 0 else 0
        
        # Probability of profit (estimated)
        prob_profit = self._estimate_win_probability(signal)
        
        # Volatility risk assessment
        if volatility > self.volatility_benchmarks['high']:
            vol_risk = "HIGH"
        elif volatility > self.volatility_benchmarks['moderate']:
            vol_risk = "MODERATE"
        else:
            vol_risk = "LOW"
        
        return PositionRisk(
            symbol=signal.symbol,
            strategy=signal.strategy,
            position_size=position_size,
            max_loss=max_loss,
            max_gain=max_gain,
            risk_reward_ratio=risk_reward,
            probability_of_profit=prob_profit,
            days_to_expiration=None,  # Would need options data
            volatility_risk=vol_risk,
            correlation_risk="LOW"  # Would need correlation analysis
        )
    
    def assess_portfolio_risk(self, positions: List[PortfolioPosition],
                            recommendations: List[DailyRecommendation]) -> PortfolioRisk:
        """Assess overall portfolio risk"""
        
        # Calculate total risk
        total_at_risk = sum(abs(pos.unrealized_pnl) for pos in positions if pos.unrealized_pnl < 0)
        risk_percentage = total_at_risk / self.account_size
        
        # Strategy concentration
        strategy_concentration = {}
        total_value = sum(pos.current_value for pos in positions)
        
        if total_value > 0:
            for strategy in StrategyType:
                strategy_value = sum(pos.current_value for pos in positions if pos.strategy == strategy)
                strategy_concentration[strategy] = strategy_value / total_value
        
        # Diversification score (simplified)
        diversification_score = 1.0 - max(strategy_concentration.values()) if strategy_concentration else 1.0
        
        # Risk level assessment
        if risk_percentage > 0.15:
            correlation_risk = "HIGH"
        elif risk_percentage > 0.10:
            correlation_risk = "MODERATE"
        else:
            correlation_risk = "LOW"
        
        # Volatility exposure (would need more data for accurate assessment)
        volatility_exposure = "MODERATE"
        
        # Generate recommendations
        risk_recommendations = []
        
        if risk_percentage > self.risk_params.max_portfolio_risk:
            risk_recommendations.append("Portfolio risk exceeds limit - consider reducing positions")
        
        if diversification_score < 0.6:
            risk_recommendations.append("Low diversification - consider spreading across strategies")
        
        max_strategy_concentration = max(strategy_concentration.values()) if strategy_concentration else 0
        if max_strategy_concentration > 0.6:
            risk_recommendations.append("High strategy concentration - diversify across strategies")
        
        return PortfolioRisk(
            total_risk=total_at_risk,
            risk_percentage=risk_percentage,
            diversification_score=diversification_score,
            correlation_risk=correlation_risk,
            volatility_exposure=volatility_exposure,
            strategy_concentration=strategy_concentration,
            recommendations=risk_recommendations
        )
    
    def generate_risk_report(self, portfolio_risk: PortfolioRisk,
                           position_risks: List[PositionRisk]) -> str:
        """Generate comprehensive risk report"""
        
        report_lines = []
        report_lines.append("=== RISK MANAGEMENT REPORT ===")
        report_lines.append("")
        
        # Portfolio overview
        report_lines.append(f"Portfolio Risk: {portfolio_risk.risk_percentage:.1%} of account")
        report_lines.append(f"Total at Risk: ${portfolio_risk.total_risk:,.2f}")
        report_lines.append(f"Diversification Score: {portfolio_risk.diversification_score:.2f}")
        report_lines.append(f"Correlation Risk: {portfolio_risk.correlation_risk}")
        report_lines.append("")
        
        # Strategy breakdown
        if portfolio_risk.strategy_concentration:
            report_lines.append("Strategy Allocation:")
            for strategy, concentration in portfolio_risk.strategy_concentration.items():
                if concentration > 0:
                    report_lines.append(f"  {strategy.value}: {concentration:.1%}")
            report_lines.append("")
        
        # Position risks
        if position_risks:
            report_lines.append("Position Risk Summary:")
            for pos_risk in position_risks:
                report_lines.append(
                    f"  {pos_risk.symbol} ({pos_risk.strategy.value}): "
                    f"${pos_risk.max_loss:,.0f} max loss, "
                    f"{pos_risk.probability_of_profit:.0%} win rate"
                )
            report_lines.append("")
        
        # Recommendations
        if portfolio_risk.recommendations:
            report_lines.append("Risk Management Recommendations:")
            for rec in portfolio_risk.recommendations:
                report_lines.append(f"  • {rec}")
        
        return "\n".join(report_lines)
