"""
Daily Trading Dashboard System
Comprehensive dashboard for time-sequenced action plans and mobile-friendly trading execution

This module provides a complete trading dashboard that includes:
1. Time-sequenced action plans with priority rankings
2. Mobile-friendly formatting for on-the-go trading
3. Scannable, prioritized information display
4. Morning briefing, intraday alerts, and end-of-day review
5. Quick-reference summaries for immediate execution
6. Real-time updates and adaptive content

Date: August 18, 2025
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, time, timedelta
from enum import Enum
import threading
import json

from trading_itinerary_generator import TradingItineraryGenerator, DailyTradingPlan, TradingItinerary, TradingAction, ExecutionTiming
from intelligent_strategy_engine import IntelligentStrategyEngine, StrategyOfTheDay
from market_analysis_engine import MarketAnalysisEngine
from realtime_monitoring_system import RealTimeMonitoringSystem, Alert
from ai_analysis_engine import AIAnalysisEngine

class DashboardMode(Enum):
    DESKTOP = "desktop"
    MOBILE = "mobile"
    COMPACT = "compact"

class DashboardSection(Enum):
    MORNING_BRIEFING = "morning_briefing"
    ACTIVE_TRADES = "active_trades"
    PENDING_ACTIONS = "pending_actions"
    MARKET_OVERVIEW = "market_overview"
    ALERTS = "alerts"
    PERFORMANCE = "performance"
    END_OF_DAY = "end_of_day"

@dataclass
class DashboardConfig:
    """Configuration for dashboard display and behavior"""
    mode: DashboardMode = DashboardMode.DESKTOP
    auto_refresh_interval: int = 30  # seconds
    show_sections: List[DashboardSection] = field(default_factory=lambda: list(DashboardSection))
    priority_filter: List[str] = field(default_factory=lambda: ["HIGH", "MEDIUM", "LOW"])
    time_format: str = "12h"  # 12h or 24h
    compact_view: bool = False
    mobile_optimized: bool = False

class DailyTradingDashboard:
    """Comprehensive daily trading dashboard with mobile-friendly interface"""
    
    def __init__(self, api_key: str, config: DashboardConfig = None):
        self.api_key = api_key
        self.config = config or DashboardConfig()
        
        # Core components
        self.strategy_engine = IntelligentStrategyEngine(api_key)
        self.market_analyzer = MarketAnalysisEngine(api_key)
        self.itinerary_generator = TradingItineraryGenerator()
        self.monitoring_system = RealTimeMonitoringSystem(api_key=api_key)
        self.ai_analyzer = AIAnalysisEngine(api_key)
        
        # Dashboard state
        self.current_plan: Optional[DailyTradingPlan] = None
        self.active_alerts: List[Alert] = []
        self.last_update: Optional[datetime] = None
        self.is_market_open: bool = False
        
        # GUI components
        self.root: Optional[tk.Tk] = None
        self.dashboard_frame: Optional[ttk.Frame] = None
        self.sections: Dict[DashboardSection, ttk.Frame] = {}
        
        # Auto-refresh
        self.auto_refresh_active = False
        self.refresh_thread: Optional[threading.Thread] = None
    
    def create_dashboard_gui(self, symbols: List[str] = None):
        """Create the main dashboard GUI"""
        if symbols is None:
            symbols = ["AAPL", "NVDA", "AMD", "GOOGL", "AMZN"]
        
        self.symbols = symbols
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("Daily Trading Dashboard - August 18, 2025")
        self.root.geometry("1600x1000" if self.config.mode == DashboardMode.DESKTOP else "800x600")
        
        # Configure styles
        self._setup_styles()
        
        # Create main layout
        self._create_main_layout()
        
        # Generate initial plan
        self._generate_daily_plan()
        
        # Start auto-refresh if enabled
        if self.config.auto_refresh_interval > 0:
            self._start_auto_refresh()
        
        return self.root
    
    def _setup_styles(self):
        """Setup custom styles for the dashboard"""
        style = ttk.Style()
        
        # Dashboard-specific styles
        style.configure('Dashboard.TFrame', background='#f0f0f0')
        style.configure('Section.TLabelframe', font=('Arial', 11, 'bold'))
        style.configure('Priority.High.TLabel', foreground='red', font=('Arial', 9, 'bold'))
        style.configure('Priority.Medium.TLabel', foreground='orange', font=('Arial', 9))
        style.configure('Priority.Low.TLabel', foreground='green', font=('Arial', 9))
        style.configure('Time.TLabel', font=('Courier', 9, 'bold'))
        style.configure('Status.Active.TLabel', foreground='blue', font=('Arial', 9, 'bold'))
        style.configure('Status.Pending.TLabel', foreground='gray', font=('Arial', 9))
        style.configure('Status.Complete.TLabel', foreground='green', font=('Arial', 9))
    
    def _create_main_layout(self):
        """Create the main dashboard layout"""
        
        # Main container
        main_container = ttk.Frame(self.root, style='Dashboard.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Header with refresh controls
        self._create_header(main_container)
        
        # Main content area
        if self.config.mode == DashboardMode.MOBILE:
            self._create_mobile_layout(main_container)
        else:
            self._create_desktop_layout(main_container)
    
    def _create_header(self, parent):
        """Create dashboard header with controls"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Title and time
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Label(title_frame, text="Daily Trading Dashboard", 
                 font=('Arial', 16, 'bold')).pack(side=tk.LEFT)
        
        self.time_label = ttk.Label(title_frame, text="", style='Time.TLabel')
        self.time_label.pack(side=tk.RIGHT)
        
        # Controls
        controls_frame = ttk.Frame(header_frame)
        controls_frame.pack(side=tk.RIGHT)
        
        ttk.Button(controls_frame, text="Refresh", 
                  command=self._manual_refresh).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(controls_frame, text="Export", 
                  command=self._export_dashboard).pack(side=tk.LEFT, padx=2)
        
        # Market status indicator
        self.market_status_label = ttk.Label(controls_frame, text="Market: CLOSED", 
                                           style='Status.Pending.TLabel')
        self.market_status_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_desktop_layout(self, parent):
        """Create desktop-optimized layout"""
        
        # Create notebook for sections
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Morning Briefing Tab
        briefing_frame = ttk.Frame(notebook)
        notebook.add(briefing_frame, text="Morning Briefing")
        self.sections[DashboardSection.MORNING_BRIEFING] = briefing_frame
        self._create_morning_briefing_section(briefing_frame)
        
        # Active Trades Tab
        trades_frame = ttk.Frame(notebook)
        notebook.add(trades_frame, text="Active Trades")
        self.sections[DashboardSection.ACTIVE_TRADES] = trades_frame
        self._create_active_trades_section(trades_frame)
        
        # Pending Actions Tab
        actions_frame = ttk.Frame(notebook)
        notebook.add(actions_frame, text="Pending Actions")
        self.sections[DashboardSection.PENDING_ACTIONS] = actions_frame
        self._create_pending_actions_section(actions_frame)
        
        # Market Overview Tab
        market_frame = ttk.Frame(notebook)
        notebook.add(market_frame, text="Market Overview")
        self.sections[DashboardSection.MARKET_OVERVIEW] = market_frame
        self._create_market_overview_section(market_frame)
        
        # Alerts Tab
        alerts_frame = ttk.Frame(notebook)
        notebook.add(alerts_frame, text="Alerts")
        self.sections[DashboardSection.ALERTS] = alerts_frame
        self._create_alerts_section(alerts_frame)
    
    def _create_mobile_layout(self, parent):
        """Create mobile-optimized layout"""
        
        # Single scrollable frame for mobile
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Add sections vertically
        self._create_compact_briefing(scrollable_frame)
        self._create_compact_actions(scrollable_frame)
        self._create_compact_alerts(scrollable_frame)
    
    def _create_morning_briefing_section(self, parent):
        """Create morning briefing section"""
        
        # Briefing text area
        briefing_frame = ttk.LabelFrame(parent, text="Morning Briefing", style='Section.TLabelframe')
        briefing_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.briefing_text = scrolledtext.ScrolledText(briefing_frame, height=15, 
                                                      font=('Arial', 10), wrap=tk.WORD)
        self.briefing_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Key metrics frame
        metrics_frame = ttk.LabelFrame(parent, text="Key Metrics", style='Section.TLabelframe')
        metrics_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create metrics grid
        self.metrics_labels = {}
        metrics = ["Total Trades", "Risk Exposure", "Market Regime", "VIX Level", "Active Alerts"]
        
        for i, metric in enumerate(metrics):
            row = i // 3
            col = i % 3
            
            ttk.Label(metrics_frame, text=f"{metric}:").grid(row=row*2, column=col, sticky='w', padx=5, pady=2)
            self.metrics_labels[metric] = ttk.Label(metrics_frame, text="--", font=('Arial', 9, 'bold'))
            self.metrics_labels[metric].grid(row=row*2+1, column=col, sticky='w', padx=5, pady=2)
    
    def _create_active_trades_section(self, parent):
        """Create active trades section"""
        
        trades_frame = ttk.LabelFrame(parent, text="Active Trades", style='Section.TLabelframe')
        trades_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Trades treeview
        columns = ('Symbol', 'Strategy', 'Entry Time', 'Status', 'P&L', 'Next Action')
        self.trades_tree = ttk.Treeview(trades_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.trades_tree.heading(col, text=col)
            self.trades_tree.column(col, width=120)
        
        # Scrollbar for trades
        trades_scrollbar = ttk.Scrollbar(trades_frame, orient=tk.VERTICAL, command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=trades_scrollbar.set)
        
        self.trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        trades_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_pending_actions_section(self, parent):
        """Create pending actions section"""
        
        actions_frame = ttk.LabelFrame(parent, text="Pending Actions", style='Section.TLabelframe')
        actions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Actions treeview
        columns = ('Time', 'Symbol', 'Action', 'Priority', 'Details', 'Status')
        self.actions_tree = ttk.Treeview(actions_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.actions_tree.heading(col, text=col)
            if col == 'Details':
                self.actions_tree.column(col, width=200)
            else:
                self.actions_tree.column(col, width=100)
        
        # Scrollbar for actions
        actions_scrollbar = ttk.Scrollbar(actions_frame, orient=tk.VERTICAL, command=self.actions_tree.yview)
        self.actions_tree.configure(yscrollcommand=actions_scrollbar.set)
        
        self.actions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        actions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def _create_market_overview_section(self, parent):
        """Create market overview section"""
        
        overview_frame = ttk.LabelFrame(parent, text="Market Overview", style='Section.TLabelframe')
        overview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.market_text = scrolledtext.ScrolledText(overview_frame, height=20, 
                                                    font=('Arial', 10), wrap=tk.WORD)
        self.market_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def _create_alerts_section(self, parent):
        """Create alerts section"""
        
        alerts_frame = ttk.LabelFrame(parent, text="Active Alerts", style='Section.TLabelframe')
        alerts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Alerts treeview
        columns = ('Time', 'Type', 'Symbol', 'Message', 'Severity', 'Action')
        self.alerts_tree = ttk.Treeview(alerts_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.alerts_tree.heading(col, text=col)
            if col == 'Message':
                self.alerts_tree.column(col, width=250)
            else:
                self.alerts_tree.column(col, width=100)
        
        # Scrollbar for alerts
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)
        
        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
