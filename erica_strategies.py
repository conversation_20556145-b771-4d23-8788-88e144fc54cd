"""
Erica's Trading Strategies Implementation
Based on @AbundantlyErica YouTube channel methodologies

Core Strategies:
1. Covered Calls - Generate income from stock holdings
2. Credit Spreads - Profit from time decay and limited movement
3. LEAPS - Long-term equity anticipation securities
4. Premium Selling - Systematic premium collection strategies

Key Principles from Erica's System:
- Focus on high-probability trades
- Systematic approach to options trading
- Risk management through position sizing
- Compound growth through reinvestment
- Market timing based on volatility
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import math
from datetime import datetime, timedelta

from daily_outline import TradingSignal, StrategyType, MarketCondition, MarketAnalysis

class OptionType(Enum):
    CALL = "call"
    PUT = "put"

@dataclass
class OptionData:
    symbol: str
    strike: float
    expiration: str
    option_type: OptionType
    bid: float
    ask: float
    volume: int
    open_interest: int
    implied_volatility: float
    delta: Optional[float] = None
    theta: Optional[float] = None
    gamma: Optional[float] = None

@dataclass
class StrategyParameters:
    """Configuration parameters for each strategy"""
    min_premium: float = 0.30  # Minimum premium to collect
    max_dte: int = 45  # Maximum days to expiration
    min_dte: int = 7   # Minimum days to expiration
    target_delta: float = 0.30  # Target delta for options
    profit_target: float = 0.50  # Take profit at 50% of max profit
    max_loss: float = 2.0  # Maximum loss as multiple of premium collected

class EricaStrategies:
    """Implementation of Erica's core trading strategies"""
    
    def __init__(self, params: StrategyParameters = None):
        self.params = params or StrategyParameters()
    
    def analyze_covered_call_opportunity(self, 
                                       market_data: MarketAnalysis,
                                       options_chain: List[OptionData],
                                       account_size: float = 100000) -> Optional[TradingSignal]:
        """
        Analyze covered call opportunities based on Erica's methodology
        
        Erica's Covered Call Rules:
        1. Own 100 shares of the underlying
        2. Sell calls 30-45 DTE
        3. Target 0.30 delta (30% probability of assignment)
        4. Collect minimum $30 premium per contract
        5. Close at 50% profit or 21 DTE
        """
        if not options_chain:
            return None
            
        current_price = market_data.current_price
        
        # Filter call options based on Erica's criteria
        suitable_calls = []
        for option in options_chain:
            if (option.option_type == OptionType.CALL and
                option.strike > current_price and  # Out of the money
                self.params.min_dte <= self._days_to_expiration(option.expiration) <= self.params.max_dte and
                option.bid >= self.params.min_premium and
                option.delta and abs(option.delta) <= self.params.target_delta):
                suitable_calls.append(option)
        
        if not suitable_calls:
            return None
        
        # Select best option based on premium/risk ratio
        best_option = max(suitable_calls, key=lambda x: x.bid / (x.strike - current_price))
        
        # Calculate position sizing (conservative approach)
        max_contracts = min(10, int(account_size * 0.1 / (current_price * 100)))  # Max 10% of account
        
        confidence = self._calculate_covered_call_confidence(market_data, best_option)
        
        return TradingSignal(
            symbol=market_data.symbol,
            strategy=StrategyType.COVERED_CALL,
            action="SELL" if confidence > 0.6 else "HOLD",
            confidence=confidence,
            reasoning=f"Covered call on {best_option.strike} strike, {best_option.expiration} exp. "
                     f"Premium: ${best_option.bid:.2f}, Delta: {best_option.delta:.2f}",
            entry_price=best_option.bid,
            target_price=best_option.bid * 0.5,  # 50% profit target
            position_size=max_contracts,
            risk_reward_ratio=2.0  # Risk premium to make 2x return
        )
    
    def analyze_credit_spread_opportunity(self,
                                        market_data: MarketAnalysis,
                                        options_chain: List[OptionData],
                                        account_size: float = 100000) -> Optional[TradingSignal]:
        """
        Analyze credit spread opportunities (Put Credit Spreads for bullish bias)
        
        Erica's Credit Spread Rules:
        1. Use put credit spreads in bullish/neutral markets
        2. Sell put at 0.15-0.20 delta (15-20% probability of assignment)
        3. Buy put 5-10 strikes lower for protection
        4. Target 30-45 DTE
        5. Close at 50% profit or 21 DTE
        """
        if not options_chain or market_data.market_condition == MarketCondition.BEARISH:
            return None
        
        current_price = market_data.current_price
        
        # Find suitable put options for credit spread
        puts = [opt for opt in options_chain if opt.option_type == OptionType.PUT]
        
        suitable_short_puts = []
        for put in puts:
            dte = self._days_to_expiration(put.expiration)
            if (put.strike < current_price * 0.95 and  # Below current price
                self.params.min_dte <= dte <= self.params.max_dte and
                put.delta and abs(put.delta) >= 0.15 and abs(put.delta) <= 0.20):
                suitable_short_puts.append(put)
        
        if not suitable_short_puts:
            return None
        
        best_short_put = max(suitable_short_puts, key=lambda x: x.bid)
        
        # Find protective put (5-10 strikes lower)
        protective_puts = [
            put for put in puts 
            if (put.expiration == best_short_put.expiration and
                put.strike >= best_short_put.strike - 10 and
                put.strike < best_short_put.strike)
        ]
        
        if not protective_puts:
            return None
        
        best_long_put = min(protective_puts, key=lambda x: x.ask)
        
        # Calculate spread metrics
        max_profit = best_short_put.bid - best_long_put.ask
        max_loss = (best_short_put.strike - best_long_put.strike) - max_profit
        
        if max_profit < 0.25 or max_loss / max_profit > 3:  # Risk/reward check
            return None
        
        confidence = self._calculate_credit_spread_confidence(market_data, best_short_put)
        
        return TradingSignal(
            symbol=market_data.symbol,
            strategy=StrategyType.CREDIT_SPREAD,
            action="SELL" if confidence > 0.65 else "HOLD",
            confidence=confidence,
            reasoning=f"Put credit spread: Sell {best_short_put.strike}P, Buy {best_long_put.strike}P. "
                     f"Max profit: ${max_profit:.2f}, Max loss: ${max_loss:.2f}",
            entry_price=max_profit,
            target_price=max_profit * 0.5,
            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0
        )
    
    def analyze_leaps_opportunity(self,
                                market_data: MarketAnalysis,
                                options_chain: List[OptionData],
                                account_size: float = 100000) -> Optional[TradingSignal]:
        """
        Analyze LEAPS (Long-term Equity Anticipation Securities) opportunities
        
        Erica's LEAPS Rules:
        1. Buy calls with 12+ months to expiration
        2. Target 0.70-0.80 delta (deep in the money)
        3. Use as stock replacement with leverage
        4. Focus on strong growth stocks
        5. Hold for 6-12 months typically
        """
        if market_data.market_condition == MarketCondition.BEARISH:
            return None
        
        current_price = market_data.current_price
        
        # Filter for LEAPS calls (12+ months)
        leaps_calls = []
        for option in options_chain:
            dte = self._days_to_expiration(option.expiration)
            if (option.option_type == OptionType.CALL and
                dte >= 365 and  # At least 12 months
                option.strike <= current_price * 1.05 and  # At or near the money
                option.delta and option.delta >= 0.70):  # High delta
                leaps_calls.append(option)
        
        if not leaps_calls:
            return None
        
        # Select LEAPS with best delta/cost ratio
        best_leaps = min(leaps_calls, key=lambda x: x.ask / x.delta if x.delta else float('inf'))
        
        # Position sizing for LEAPS (more conservative)
        max_contracts = min(5, int(account_size * 0.05 / (best_leaps.ask * 100)))
        
        confidence = self._calculate_leaps_confidence(market_data, best_leaps)
        
        return TradingSignal(
            symbol=market_data.symbol,
            strategy=StrategyType.LEAPS,
            action="BUY" if confidence > 0.7 else "HOLD",
            confidence=confidence,
            reasoning=f"LEAPS call {best_leaps.strike} strike, {best_leaps.expiration} exp. "
                     f"Cost: ${best_leaps.ask:.2f}, Delta: {best_leaps.delta:.2f}",
            entry_price=best_leaps.ask,
            target_price=best_leaps.ask * 2.0,  # 100% profit target
            position_size=max_contracts,
            risk_reward_ratio=1.0  # Risk premium to make 100% return
        )
    
    def analyze_premium_selling_opportunity(self,
                                          market_data: MarketAnalysis,
                                          options_chain: List[OptionData]) -> Optional[TradingSignal]:
        """
        Systematic premium selling based on high probability strategies
        
        Erica's Premium Selling Rules:
        1. Focus on high IV rank periods
        2. Sell options with 15-30 delta
        3. Target 30-45 DTE
        4. Manage at 50% profit or 21 DTE
        5. Use cash-secured puts or covered calls
        """
        if not options_chain:
            return None
        
        # Determine best premium selling strategy based on market condition
        if market_data.market_condition in [MarketCondition.BULLISH, MarketCondition.NEUTRAL]:
            # Prefer cash-secured puts in bullish/neutral markets
            return self._analyze_cash_secured_puts(market_data, options_chain)
        else:
            # Consider covered calls in bearish markets (if holding stock)
            return self._analyze_premium_calls(market_data, options_chain)

    def analyze_pmcc_opportunity(self, market_data: MarketAnalysis, options_chain: List[OptionData], account_size: float = 100000) -> Optional[TradingSignal]:
        """Analyze Poor Man's Covered Call (PMCC): LEAPS long call + short nearer-dated call."""
        if not options_chain:
            return None
        current_price = market_data.current_price
        # LEAPS selection: >=365 DTE, 0.50–0.70 delta on calls
        leaps_candidates: List[OptionData] = []
        for o in options_chain:
            if o.option_type == OptionType.CALL and o.delta is not None:
                dte = self._days_to_expiration(o.expiration)
                if dte >= 365 and 0.50 <= abs(o.delta) <= 0.70:
                    leaps_candidates.append(o)
        if not leaps_candidates:
            return None
        leaps_candidates.sort(key=lambda o: (abs(abs(o.delta or 0.0) - 0.60), -(o.open_interest or 0), -(o.volume or 0), (o.ask or 0.0)))
        long_call = leaps_candidates[0]
        # Short call 21–45 DTE, <=0.30Δ, OTM
        short_candidates: List[OptionData] = []
        for o in options_chain:
            if o.option_type == OptionType.CALL and o.delta is not None:
                dte = self._days_to_expiration(o.expiration)
                if 21 <= dte <= 45 and abs(o.delta) <= 0.30 and o.strike > current_price:
                    buffer = max(1.0, current_price * 0.01)
                    if o.strike >= current_price + buffer:
                        short_candidates.append(o)
        if not short_candidates:
            return None
        short_candidates.sort(key=lambda o: (-float(o.bid or 0.0), abs(abs(o.delta or 0.0) - 0.25), -(o.open_interest or 0), -(o.volume or 0)))
        short_call = short_candidates[0]
        # Confidence
        confidence = 0.55
        if market_data.market_condition in (MarketCondition.BULLISH, MarketCondition.NEUTRAL):
            confidence += 0.2
        if market_data.volatility is not None:
            if market_data.volatility <= 0.35:
                confidence += 0.1
            elif market_data.volatility >= 0.5:
                confidence -= 0.05
        if long_call.delta is not None:
            confidence += max(0.0, 0.05 - abs(abs(long_call.delta) - 0.60))
        if (short_call.bid or 0) >= 0.5:
            confidence += 0.05
        confidence = min(1.0, max(0.0, confidence))
        debit = (long_call.ask or 0.0) - (short_call.bid or 0.0)
        reasoning = (f"PMCC: Buy LEAPS {long_call.expiration} {long_call.strike} call (Δ={long_call.delta:.2f}) and "
                     f"sell {short_call.expiration} {short_call.strike} call (Δ={short_call.delta:.2f})")
        return TradingSignal(
            symbol=market_data.symbol,
            strategy=StrategyType.LEAPS,
            action="BUY" if confidence >= 0.6 else "HOLD",
            confidence=confidence,
            reasoning=reasoning + " — PMCC (LEAPS + short call)",
            entry_price=debit if debit > 0 else None,
            target_price=None
        )

    
    def _analyze_cash_secured_puts(self, market_data: MarketAnalysis, options_chain: List[OptionData]) -> Optional[TradingSignal]:
        """Analyze cash-secured put opportunities"""
        current_price = market_data.current_price
        
        suitable_puts = []
        for option in options_chain:
            if (option.option_type == OptionType.PUT and
                option.strike < current_price * 0.95 and  # Below current price
                self.params.min_dte <= self._days_to_expiration(option.expiration) <= self.params.max_dte and
                option.delta and abs(option.delta) >= 0.15 and abs(option.delta) <= 0.30):
                suitable_puts.append(option)
        
        if not suitable_puts:
            return None
        
        best_put = max(suitable_puts, key=lambda x: x.bid)
        confidence = self._calculate_premium_selling_confidence(market_data, best_put)
        
        return TradingSignal(
            symbol=market_data.symbol,
            strategy=StrategyType.PREMIUM_SELLING,
            action="SELL" if confidence > 0.65 else "HOLD",
            confidence=confidence,
            reasoning=f"Cash-secured put {best_put.strike} strike, premium: ${best_put.bid:.2f}",
            entry_price=best_put.bid,
            target_price=best_put.bid * 0.5
        )
    
    def _analyze_premium_calls(self, market_data: MarketAnalysis, options_chain: List[OptionData]) -> Optional[TradingSignal]:
        """Analyze premium call selling opportunities"""
        # Implementation for call premium selling
        return None
    
    def _days_to_expiration(self, expiration_str: str) -> int:
        """Calculate days to expiration"""
        try:
            exp_date = datetime.strptime(expiration_str, "%Y-%m-%d")
            return (exp_date - datetime.now()).days
        except:
            return 0
    
    def _calculate_covered_call_confidence(self, market_data: MarketAnalysis, option: OptionData) -> float:
        """Calculate confidence score for covered call strategy"""
        confidence = 0.5  # Base confidence
        
        # Adjust based on market conditions
        if market_data.market_condition == MarketCondition.NEUTRAL:
            confidence += 0.2
        elif market_data.market_condition == MarketCondition.BULLISH:
            confidence += 0.1
        
        # Adjust based on volatility
        if market_data.volatility > 0.25:  # High volatility favors premium selling
            confidence += 0.15
        
        # Adjust based on technical indicators
        if market_data.rsi and market_data.rsi > 60:  # Overbought favors covered calls
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _calculate_credit_spread_confidence(self, market_data: MarketAnalysis, option: OptionData) -> float:
        """Calculate confidence score for credit spread strategy"""
        confidence = 0.6  # Base confidence for credit spreads
        
        if market_data.market_condition == MarketCondition.BULLISH:
            confidence += 0.2
        elif market_data.market_condition == MarketCondition.NEUTRAL:
            confidence += 0.15
        
        # High volatility favors credit spreads
        if market_data.volatility > 0.20:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _calculate_leaps_confidence(self, market_data: MarketAnalysis, option: OptionData) -> float:
        """Calculate confidence score for LEAPS strategy"""
        confidence = 0.5
        
        if market_data.market_condition == MarketCondition.BULLISH:
            confidence += 0.3
        
        # Strong uptrend favors LEAPS
        if market_data.trend == "STRONG_UPTREND":
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _calculate_premium_selling_confidence(self, market_data: MarketAnalysis, option: OptionData) -> float:
        """Calculate confidence score for premium selling strategy"""
        confidence = 0.6
        
        # High volatility is key for premium selling
        if market_data.volatility > 0.25:
            confidence += 0.2
        elif market_data.volatility > 0.20:
            confidence += 0.1
        
        return min(confidence, 1.0)
