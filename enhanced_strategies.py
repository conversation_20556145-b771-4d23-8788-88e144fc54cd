"""
Enhanced Strategy Implementation with Stock-Specific Rules
Based on <PERSON>'s Millionaire Mentorship Program Strategy Grid

This module implements the detailed, stock-specific strategy rules for:
- AMD: High volatility, earnings-driven moves
- NVDA: Extreme volatility, gap risk
- GOOGL: Lower volatility, range-bound tendencies  
- AMZN: Seasonal patterns, earnings sensitivity
- AAPL: Steady appreciation, product cycles

Each strategy is customized per stock's unique characteristics.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import math

from daily_outline import TradingSignal, StrategyType, MarketCondition, MarketAnalysis
from erica_strategies import OptionData, OptionType, StrategyParameters

@dataclass
class StockSpecificRules:
    """Stock-specific strategy rules and parameters"""
    symbol: str
    
    # Covered Call Rules
    cc_preferred_dte: Tuple[int, int]  # (min, max) days to expiration
    cc_delta_range: Tuple[float, float]  # (min, max) delta targets
    cc_earnings_adjustment: str  # How to handle earnings
    cc_frequency: str  # How often to deploy
    
    # Put Credit Spread Rules
    pcs_width_points: Tuple[int, int]  # Spread width range
    pcs_support_buffer: float  # Distance from support level
    pcs_iv_threshold: float  # Minimum IV rank to deploy
    
    # LEAPS Rules
    leaps_preferred_timeframe: Tuple[int, int]  # (min, max) months
    leaps_delta_target: float  # Target delta for LEAPS
    leaps_funding_strategy: str  # How to fund with short calls
    
    # Wheel Strategy Rules
    wheel_frequency: str  # How often to use wheel
    wheel_support_level: str  # Where to enter CSPs
    wheel_cc_strikes: str  # How to select CC strikes after assignment
    
    # Volatility Rules
    high_iv_threshold: float  # IV rank threshold for premium selling
    earnings_move_estimate: float  # Typical earnings move %
    seasonal_patterns: List[str]  # Known seasonal tendencies

# Stock-specific rule definitions based on Erica's strategy grid
STOCK_RULES = {
    "AMD": StockSpecificRules(
        symbol="AMD",
        cc_preferred_dte=(2, 5),  # Short-DTE between reports
        cc_delta_range=(0.15, 0.25),
        cc_earnings_adjustment="EM_based_strikes",  # Use earnings move estimates
        cc_frequency="frequent_between_earnings",
        pcs_width_points=(5, 10),
        pcs_support_buffer=0.05,  # 5% buffer from support
        pcs_iv_threshold=0.6,  # 60th percentile IV
        leaps_preferred_timeframe=(12, 24),
        leaps_delta_target=0.75,
        leaps_funding_strategy="monthly_short_calls",
        wheel_frequency="frequent",
        wheel_support_level="strong_technical_support",
        wheel_cc_strikes="moderate_otm",
        high_iv_threshold=0.7,
        earnings_move_estimate=0.12,  # 12% typical EM
        seasonal_patterns=["earnings_volatility", "tech_sector_rotation"]
    ),
    
    "NVDA": StockSpecificRules(
        symbol="NVDA",
        cc_preferred_dte=(7, 21),  # Longer DTE due to gap risk
        cc_delta_range=(0.15, 0.20),  # Conservative delta
        cc_earnings_adjustment="extra_conservative",
        cc_frequency="calmer_periods_only",
        pcs_width_points=(10, 20),  # Wider spreads for volatility
        pcs_support_buffer=0.08,  # 8% buffer - more volatile
        pcs_iv_threshold=0.7,
        leaps_preferred_timeframe=(12, 18),  # Shorter due to volatility
        leaps_delta_target=0.70,
        leaps_funding_strategy="frequent_short_call_rolls",
        wheel_frequency="limited",
        wheel_support_level="deep_support_only",
        wheel_cc_strikes="conservative_otm",
        high_iv_threshold=0.8,
        earnings_move_estimate=0.15,  # 15% typical EM
        seasonal_patterns=["ai_news_cycles", "earnings_volatility", "tech_leadership"]
    ),
    
    "GOOGL": StockSpecificRules(
        symbol="GOOGL",
        cc_preferred_dte=(10, 30),  # Standard DTE
        cc_delta_range=(0.20, 0.30),  # Higher delta due to lower volatility
        cc_earnings_adjustment="em_plus_cushion",  # EM + 10-15%
        cc_frequency="near_earnings_focused",
        pcs_width_points=(5, 15),
        pcs_support_buffer=0.03,  # 3% buffer - less volatile
        pcs_iv_threshold=0.5,  # Lower threshold due to lower IV
        leaps_preferred_timeframe=(18, 24),
        leaps_delta_target=0.80,
        leaps_funding_strategy="monthly_otm_calls",
        wheel_frequency="range_bound_periods",
        wheel_support_level="trend_support",
        wheel_cc_strikes="standard_otm",
        high_iv_threshold=0.6,
        earnings_move_estimate=0.08,  # 8% typical EM
        seasonal_patterns=["ad_revenue_cycles", "regulatory_news"]
    ),
    
    "AMZN": StockSpecificRules(
        symbol="AMZN",
        cc_preferred_dte=(7, 21),
        cc_delta_range=(0.20, 0.30),
        cc_earnings_adjustment="em_aware_strikes",
        cc_frequency="range_bound_periods",
        pcs_width_points=(5, 15),
        pcs_support_buffer=0.04,  # 4% buffer
        pcs_iv_threshold=0.6,
        leaps_preferred_timeframe=(12, 24),
        leaps_delta_target=0.75,
        leaps_funding_strategy="covered_call_financing",
        wheel_frequency="effective_in_uptrend",
        wheel_support_level="consolidation_support",
        wheel_cc_strikes="seasonal_aware",
        high_iv_threshold=0.7,
        earnings_move_estimate=0.10,  # 10% typical EM
        seasonal_patterns=["q4_strength", "prime_day", "holiday_shopping"]
    ),
    
    "AAPL": StockSpecificRules(
        symbol="AAPL",
        cc_preferred_dte=(2, 30),  # Flexible: weekly and monthly
        cc_delta_range=(0.20, 0.30),
        cc_earnings_adjustment="product_cycle_aware",
        cc_frequency="frequent_cash_flow",
        pcs_width_points=(2, 10),  # Tighter spreads
        pcs_support_buffer=0.02,  # 2% buffer - most stable
        pcs_iv_threshold=0.5,
        leaps_preferred_timeframe=(12, 24),
        leaps_delta_target=0.80,
        leaps_funding_strategy="monthly_cc_around_highs",
        wheel_frequency="reliable",
        wheel_support_level="mild_dip_support",
        wheel_cc_strikes="rhythm_based",  # Match stock's natural rhythm
        high_iv_threshold=0.6,
        earnings_move_estimate=0.06,  # 6% typical EM
        seasonal_patterns=["product_launches", "september_strength", "december_strength"]
    )
}

class EnhancedStrategyAnalyzer:
    """Enhanced strategy analyzer with stock-specific rules"""
    
    def __init__(self):
        self.stock_rules = STOCK_RULES
    
    def get_stock_specific_recommendation(self, 
                                        symbol: str,
                                        market_analysis: MarketAnalysis,
                                        options_chain: List[OptionData],
                                        account_size: float = 100000) -> List[TradingSignal]:
        """
        Generate stock-specific strategy recommendations based on Erica's detailed rules
        """
        if symbol not in self.stock_rules:
            return []
        
        rules = self.stock_rules[symbol]
        signals = []
        
        # Analyze each strategy with stock-specific rules
        
        # 1. Covered Calls with stock-specific parameters
        cc_signal = self._analyze_stock_specific_covered_calls(
            symbol, market_analysis, options_chain, rules, account_size
        )
        if cc_signal:
            signals.append(cc_signal)
        
        # 2. Put Credit Spreads with stock-specific rules
        pcs_signal = self._analyze_stock_specific_put_credit_spreads(
            symbol, market_analysis, options_chain, rules, account_size
        )
        if pcs_signal:
            signals.append(pcs_signal)
        
        # 3. LEAPS with stock-specific approach
        leaps_signal = self._analyze_stock_specific_leaps(
            symbol, market_analysis, options_chain, rules, account_size
        )
        if leaps_signal:
            signals.append(leaps_signal)
        
        # 4. Wheel strategy assessment
        wheel_signal = self._analyze_stock_specific_wheel(
            symbol, market_analysis, options_chain, rules, account_size
        )
        if wheel_signal:
            signals.append(wheel_signal)
        
        return signals
    
    def _analyze_stock_specific_covered_calls(self, symbol: str, market_analysis: MarketAnalysis,
                                            options_chain: List[OptionData], rules: StockSpecificRules,
                                            account_size: float) -> Optional[TradingSignal]:
        """Analyze covered calls with stock-specific rules"""
        
        current_price = market_analysis.current_price
        
        # Filter options based on stock-specific DTE preferences
        min_dte, max_dte = rules.cc_preferred_dte
        min_delta, max_delta = rules.cc_delta_range
        
        suitable_calls = []
        for option in options_chain:
            if (option.option_type == OptionType.CALL and
                option.strike > current_price and
                min_dte <= self._days_to_expiration(option.expiration) <= max_dte and
                option.delta and min_delta <= abs(option.delta) <= max_delta):
                suitable_calls.append(option)
        
        if not suitable_calls:
            return None
        
        # Apply stock-specific selection logic
        if symbol == "AMD":
            # AMD: Focus on short-DTE between earnings for cash flow
            best_option = min(suitable_calls, key=lambda x: self._days_to_expiration(x.expiration))
            reasoning = f"AMD short-DTE CC ({self._days_to_expiration(best_option.expiration)}d) for cash flow between earnings"
            
        elif symbol == "NVDA":
            # NVDA: Conservative delta to avoid gap risk
            conservative_calls = [opt for opt in suitable_calls if opt.delta and abs(opt.delta) <= 0.20]
            if not conservative_calls:
                return None
            best_option = max(conservative_calls, key=lambda x: x.bid)
            reasoning = f"NVDA conservative CC (Δ={best_option.delta:.2f}) to avoid gap risk"
            
        elif symbol == "GOOGL":
            # GOOGL: Near earnings with extra cushion
            best_option = max(suitable_calls, key=lambda x: x.strike - current_price)  # Higher strikes
            reasoning = f"GOOGL CC with earnings cushion, strike {best_option.strike}"
            
        elif symbol == "AMZN":
            # AMZN: EM-aware strikes, seasonal consideration
            best_option = max(suitable_calls, key=lambda x: x.bid / (x.strike - current_price))
            reasoning = f"AMZN CC with EM-aware strike selection"
            
        elif symbol == "AAPL":
            # AAPL: Flexible DTE for cash flow
            dte = self._days_to_expiration(suitable_calls[0].expiration)
            if dte <= 7:
                best_option = max(suitable_calls, key=lambda x: x.bid)
                reasoning = f"AAPL short-term CC for immediate cash flow"
            else:
                best_option = max(suitable_calls, key=lambda x: x.bid / (x.strike - current_price))
                reasoning = f"AAPL monthly CC for steady income"
        else:
            best_option = suitable_calls[0]
            reasoning = "Standard covered call setup"
        
        # Calculate confidence based on stock-specific factors
        confidence = self._calculate_stock_specific_confidence(symbol, market_analysis, best_option, rules)
        
        return TradingSignal(
            symbol=symbol,
            strategy=StrategyType.COVERED_CALL,
            action="SELL" if confidence > 0.6 else "HOLD",
            confidence=confidence,
            reasoning=reasoning,
            entry_price=best_option.bid,
            target_price=best_option.bid * 0.5,  # 50% profit target
            position_size=min(10, int(account_size * 0.1 / (current_price * 100)))
        )
    
    def _analyze_stock_specific_put_credit_spreads(self, symbol: str, market_analysis: MarketAnalysis,
                                                 options_chain: List[OptionData], rules: StockSpecificRules,
                                                 account_size: float) -> Optional[TradingSignal]:
        """Analyze put credit spreads with stock-specific rules"""
        
        current_price = market_analysis.current_price
        support_level = market_analysis.support_level
        
        if not support_level:
            return None
        
        # Stock-specific support buffer
        target_strike = support_level * (1 + rules.pcs_support_buffer)
        
        if target_strike >= current_price * 0.95:  # Too close to current price
            return None
        
        # Find suitable puts for the spread
        puts = [opt for opt in options_chain if opt.option_type == OptionType.PUT]
        
        # Filter by stock-specific criteria
        min_width, max_width = rules.pcs_width_points
        
        suitable_short_puts = [
            put for put in puts
            if (put.strike <= target_strike and
                put.strike >= target_strike - max_width and
                7 <= self._days_to_expiration(put.expiration) <= 45)
        ]
        
        if not suitable_short_puts:
            return None
        
        best_short_put = max(suitable_short_puts, key=lambda x: x.bid)
        
        # Find protective put
        protective_puts = [
            put for put in puts
            if (put.expiration == best_short_put.expiration and
                put.strike >= best_short_put.strike - max_width and
                put.strike < best_short_put.strike)
        ]
        
        if not protective_puts:
            return None
        
        best_long_put = min(protective_puts, key=lambda x: x.ask)
        
        max_profit = best_short_put.bid - best_long_put.ask
        max_loss = (best_short_put.strike - best_long_put.strike) - max_profit
        
        if max_profit < 0.25 or max_loss / max_profit > 3:
            return None
        
        # Stock-specific reasoning
        reasoning_map = {
            "AMD": f"AMD PCS at support +{rules.pcs_support_buffer:.0%} buffer, width {best_short_put.strike - best_long_put.strike:.0f}pts",
            "NVDA": f"NVDA conservative PCS with {rules.pcs_support_buffer:.0%} buffer for volatility",
            "GOOGL": f"GOOGL PCS in range-bound phase, wide breakeven cushion",
            "AMZN": f"AMZN PCS after support hold, seasonal awareness",
            "AAPL": f"AAPL PCS into seasonal strength, tight risk management"
        }
        
        confidence = self._calculate_pcs_confidence(symbol, market_analysis, rules)
        
        return TradingSignal(
            symbol=symbol,
            strategy=StrategyType.CREDIT_SPREAD,
            action="SELL" if confidence > 0.65 else "HOLD",
            confidence=confidence,
            reasoning=reasoning_map.get(symbol, "Put credit spread setup"),
            entry_price=max_profit,
            target_price=max_profit * 0.5,
            risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0
        )
    
    def _analyze_stock_specific_leaps(self, symbol: str, market_analysis: MarketAnalysis,
                                    options_chain: List[OptionData], rules: StockSpecificRules,
                                    account_size: float) -> Optional[TradingSignal]:
        """Analyze LEAPS with stock-specific approach"""
        
        if market_analysis.market_condition == MarketCondition.BEARISH:
            return None
        
        current_price = market_analysis.current_price
        min_months, max_months = rules.leaps_preferred_timeframe
        target_delta = rules.leaps_delta_target
        
        # Filter for LEAPS calls
        leaps_calls = []
        for option in options_chain:
            dte = self._days_to_expiration(option.expiration)
            if (option.option_type == OptionType.CALL and
                min_months * 30 <= dte <= max_months * 30 and
                option.strike <= current_price * 1.05 and
                option.delta and option.delta >= target_delta - 0.05):
                leaps_calls.append(option)
        
        if not leaps_calls:
            return None
        
        # Stock-specific LEAPS selection
        if symbol == "NVDA":
            # NVDA: Smaller size due to volatility
            best_leaps = min(leaps_calls, key=lambda x: x.ask)  # Lower cost due to volatility
            reasoning = f"NVDA LEAPS with reduced size due to volatility, frequent short call rolls planned"
        elif symbol == "GOOGL":
            # GOOGL: Lower IV windows preferred
            best_leaps = max(leaps_calls, key=lambda x: x.delta)  # Higher delta in low IV
            reasoning = f"GOOGL LEAPS in lower IV window, pair with 20-30 DTE short calls"
        elif symbol == "AMZN":
            # AMZN: Use on breakouts after consolidation
            best_leaps = max(leaps_calls, key=lambda x: x.delta)
            reasoning = f"AMZN LEAPS on breakout setup, finance with covered calls"
        elif symbol == "AAPL":
            # AAPL: Ideal for steady appreciation
            best_leaps = min(leaps_calls, key=lambda x: x.ask / x.delta if x.delta else float('inf'))
            reasoning = f"AAPL LEAPS for steady appreciation, sell CCs around seasonal highs"
        else:
            best_leaps = leaps_calls[0]
            reasoning = f"LEAPS setup for {symbol}"
        
        # Adjust position size based on stock volatility
        volatility_multiplier = {
            "NVDA": 0.5,  # Smaller size
            "AMD": 0.7,
            "GOOGL": 1.0,
            "AMZN": 0.8,
            "AAPL": 1.0
        }.get(symbol, 1.0)
        
        max_contracts = min(5, int(account_size * 0.05 * volatility_multiplier / (best_leaps.ask * 100)))
        
        confidence = self._calculate_leaps_confidence(symbol, market_analysis, rules)
        
        return TradingSignal(
            symbol=symbol,
            strategy=StrategyType.LEAPS,
            action="BUY" if confidence > 0.7 else "HOLD",
            confidence=confidence,
            reasoning=reasoning,
            entry_price=best_leaps.ask,
            target_price=best_leaps.ask * 2.0,
            position_size=max_contracts
        )
    
    def _analyze_stock_specific_wheel(self, symbol: str, market_analysis: MarketAnalysis,
                                    options_chain: List[OptionData], rules: StockSpecificRules,
                                    account_size: float) -> Optional[TradingSignal]:
        """Analyze wheel strategy with stock-specific rules"""
        
        wheel_suitability = {
            "AMD": "frequent",
            "NVDA": "limited",
            "GOOGL": "range_bound_periods",
            "AMZN": "effective_in_uptrend",
            "AAPL": "reliable"
        }
        
        if wheel_suitability.get(symbol) == "limited":
            return None  # NVDA not suitable for wheel due to gap risk
        
        current_price = market_analysis.current_price
        support_level = market_analysis.support_level
        
        if not support_level:
            return None
        
        # Stock-specific wheel entry logic
        reasoning_map = {
            "AMD": f"AMD Wheel: CSP at strong support ${support_level:.2f}, frequent rotation",
            "GOOGL": f"GOOGL Wheel: Range-bound rotation, trend support entry",
            "AMZN": f"AMZN Wheel: Uptrend consolidation entry, scale-in approach",
            "AAPL": f"AAPL Wheel: Reliable weekly/monthly rhythm, mild dip entry"
        }
        
        confidence = 0.7 if symbol in ["AAPL", "AMD"] else 0.6
        
        return TradingSignal(
            symbol=symbol,
            strategy=StrategyType.PREMIUM_SELLING,
            action="SELL",
            confidence=confidence,
            reasoning=reasoning_map.get(symbol, f"{symbol} Wheel strategy"),
            entry_price=support_level * 0.98,  # Slightly below support
            target_price=support_level * 0.98 * 0.5
        )
    
    def _days_to_expiration(self, expiration_str: str) -> int:
        """Calculate days to expiration"""
        try:
            exp_date = datetime.strptime(expiration_str, "%Y-%m-%d")
            return (exp_date - datetime.now()).days
        except:
            return 0
    
    def _calculate_stock_specific_confidence(self, symbol: str, market_analysis: MarketAnalysis,
                                           option: OptionData, rules: StockSpecificRules) -> float:
        """Calculate confidence with stock-specific factors"""
        base_confidence = 0.6
        
        # Volatility factor
        if market_analysis.volatility > rules.high_iv_threshold:
            base_confidence += 0.15
        
        # Stock-specific adjustments
        if symbol == "AAPL" and market_analysis.trend == "UPTREND":
            base_confidence += 0.1  # AAPL reliable in uptrends
        elif symbol == "AMD" and market_analysis.volatility > 0.3:
            base_confidence += 0.1  # AMD benefits from high volatility
        elif symbol == "NVDA":
            base_confidence -= 0.05  # NVDA always has gap risk
        
        return min(base_confidence, 1.0)
    
    def _calculate_pcs_confidence(self, symbol: str, market_analysis: MarketAnalysis,
                                rules: StockSpecificRules) -> float:
        """Calculate PCS confidence with stock-specific factors"""
        base_confidence = 0.65
        
        if market_analysis.market_condition == MarketCondition.BULLISH:
            base_confidence += 0.15
        
        # Stock-specific PCS confidence
        if symbol == "AAPL" and market_analysis.trend == "UPTREND":
            base_confidence += 0.1
        elif symbol == "GOOGL" and market_analysis.market_condition == MarketCondition.NEUTRAL:
            base_confidence += 0.05  # GOOGL good for range-bound PCS
        
        return min(base_confidence, 1.0)
    
    def _calculate_leaps_confidence(self, symbol: str, market_analysis: MarketAnalysis,
                                  rules: StockSpecificRules) -> float:
        """Calculate LEAPS confidence with stock-specific factors"""
        base_confidence = 0.6
        
        if market_analysis.market_condition == MarketCondition.BULLISH:
            base_confidence += 0.2
        
        # Stock-specific LEAPS confidence
        if symbol == "AAPL" and market_analysis.trend == "UPTREND":
            base_confidence += 0.15  # AAPL excellent for LEAPS
        elif symbol == "NVDA":
            base_confidence -= 0.1  # Higher risk due to volatility
        
        return min(base_confidence, 1.0)
