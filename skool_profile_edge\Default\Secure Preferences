{"edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.102\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.102\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.102\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kieiaeokhmlbffedbdfgbdcnhgpcckkn": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.102\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}}}, "protection": {"macs": {"browser": {"show_home_button": "9468D0E1A5FEF3B3D549FF489DC52899FFBDEBBB9B3F7BF908E8C3A716A04133"}, "default_search_provider_data": {"template_url_data": "058C88FCDE8E96ED88F2F907995A9B3A0041EA1571203F54A56D200C77A2B489"}, "edge": {"services": {"account_id": "4C2B5A91093292A6043D61382ACF34689F57B891E29AC65F99855C2253CC79E8", "last_username": "97FC49510991E4CC0469BB0B68DC2D096AB0F0249A225E9FF6BEF44B01F92733"}}, "enterprise_signin": {"policy_recovery_token": "F486D835BBC72BE7E4C5CD7DE0D29EEBC4EEC8A6F1072100816F9FB1BB245F52"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "D1CDEC56A5C802F31E65664502E3E7620EFE8EB30395B068B15B3F35B0AED560", "cjneempfhkonkkbcmnfdibgobmhbagaj": "2686ED769647614D513C7622758635EAC7467217F5FE63934FECDD8CDA804AE9", "dcaajljecejllikfgbhjdgeognacjkkp": "6B03EBF0852432648C2B360D4365F25AB0B8BA0F091DA59AF23EE60B75641D72", "dgiklkfkllikcanfonkcabmbdfmgleag": "20CF85A8DEEE23B23619A7008F022F88EFAF4A96494BB2B15FCD74C7604AB97A", "ehlmnljdoejdahfjdfobmpfancoibmig": "62C895C34CF24C6BBB99C037B2C30CEDB0C7C0CBAA98B5AB7D87075FE89573D4", "fjngpfnaikknjdhkckmncgicobbkcnle": "F0B8668590C66CA43F484D556DD31F821148A0EF61FCEA1AFBA40A711E522892", "gbihlnbpmfkodghomcinpblknjhneknc": "EED39DAE5F659B022B874C0D1FE66BABF3835BCF0A1473BB873C39E3DDE66A4C", "gbmoeijgfngecijpcnbooedokgafmmji": "A7F513CE5DBA465228B5AF3FD12E0ED79BCD453AC76D2A7BB4B936605717A7C6", "gcinnojdebelpnodghnoicmcdmamjoch": "E9A20411A02C12E866D9F85FC9C2630A4214C06785B42003858F4C3E4E9B8225", "gecfnmoodchdkebjjffmdcmeghkflpib": "9D62C49241FEB0557B198AD02DB89EC3BBCFC3E28202A5E0B9418DA2FDD9C7D2", "hfmgbegjielnmfghmoohgmplnpeehike": "5727DC5C3328D19357DB4CB2529B41F2EC177A8A44656FE079E957C61CD361B3", "iglcjdemknebjbklcgkfaebgojjphkec": "48FA955052D81DC0F12E2D35821DA9654C3E4BEF1EE4F67981E11AA45284B6C3", "jbleckejnaboogigodiafflhkajdmpcl": "2ACB1E6A37CC328B69E31F1CAA942B20CCA4CE293C0E1F63C3647901BAD77526", "kfihiegbjaloebkmglnjnljoljgkkchm": "E7D7A15DAA973785912208039530977021CD42C6BB0CBE938BEDC3694D4CF8E9", "kieiaeokhmlbffedbdfgbdcnhgpcckkn": "541D1DB824C11512A9D458B375AF8E0EA02F25E662724A11D13E4416D12793ED", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "53767CB97AD818DD83E1C6AF9CB47A1FE7243D28AAE85C38DDAE9DF6B2AD4B25", "nkbndigcebkoaejohleckhekfmcecfja": "F94A702F58D83DC34A5F1DAE6C01B7F83C2874505048194E095FC3571E599F84", "ofefcgjbeghpigppfmkologfjadafddi": "7C828837F1717CF77F4CE78B1D196D189FCBED0040CCE94EBC5312564392F074"}, "ui": {"developer_mode": "6F44E80AA9669BF67FE7AE7541DF7D1741685DFD94536F22C17103EB25B1E2D6"}}, "google": {"services": {"last_signed_in_username": "262E3F54383A763C0F3F3AFA5EA05B0254219F67BC23BDC478ADD5E4199FEB98"}}, "homepage": "10618D5AC742FF9516D3F6EF41B3FAAC638455A4712B9290BC10907266267FD7", "homepage_is_newtabpage": "295CF4973F66252EC1F86A7385F31DB176455B929C001925CCEF288C1198080F", "media": {"cdm": {"origin_data": "3F0AECE36261D36E683A8E8C732E30E7FE8F3462E464282786BB4787734D1C80"}, "storage_id_salt": "C82BD57997AEF3F08CA0761B0AF07546636D68F01D354627DC43E6F0839C16FE"}, "pinned_tabs": "4B5AE17977E1F604D461334B4B9D2F0C967CA8FAAB13DA1F69CAE8A7419DDDB4", "prefs": {"preference_reset_time": "B1BEF40E9F53E6E0A9F57993917FF67390D22CAB21260202D1586EE6AA66E038"}, "safebrowsing": {"incidents_sent": "B382A52025515798426D082EEF0F67638B5203A6BD95FED3675BE9924C79CEC9"}, "schedule_to_flush_to_disk": "E3FA6EBE14F978C173EF97776BCB3C76CD37079D82FECE6E7641216E5B0AA444", "search_provider_overrides": "20B632117B78F850DE1CDF3DDA5D15DD65CDC43BB65A542CF027BD0E63EC8221", "session": {"restore_on_startup": "1ABB241554D0DE635F3AEEEEC6CFFCFD42D3FA13BED4A10D0DBFC40DE0752DAB", "startup_urls": "D1F6C65183F7915D90F88D3B3B6CD73AE40F2A34E9F73EB6D54FC0ADBA6BB638"}}, "super_mac": "BC922FCCF1DEBDDEC3F6EA12897309319251601F6DD92EA7B61B88D68E931D64"}}