"""
Strategy Criteria Display System
Visual representation of <PERSON>'s decision framework criteria

This module provides visual components for displaying strategy criteria,
showing which criteria are met/unmet for each recommendation with:
- Color-coded checkmarks and warnings
- Numerical scores and percentiles
- Real-time updates as market conditions change
- Educational tooltips explaining each criterion

Integration with the desktop application for transparent decision-making.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from market_analysis_engine import MarketFactors, StockSpecificFactors
from intelligent_strategy_engine import StrategyOfTheDay
from erica_decision_framework import EricaDecisionFramework, EricaStrategyRules
from strategy_decision_tree import StrategyRecommendation
from daily_outline import StrategyType

class CriteriaStatus(Enum):
    MET = "met"
    NOT_MET = "not_met"
    BORDERLINE = "borderline"
    NOT_APPLICABLE = "na"

@dataclass
class CriteriaItem:
    """Individual criteria item with status and details"""
    name: str
    description: str
    status: CriteriaStatus
    current_value: Any
    required_value: Any
    score: float  # 0-1 scale
    tooltip: str
    category: str  # market, stock, erica, risk
    # Optional list of risk factors for this criterion; default empty to avoid AttributeError
    risk_factors: List[str] = field(default_factory=list)

@dataclass
class StrategyCriteriaAnalysis:
    """Complete criteria analysis for a strategy recommendation"""
    symbol: str
    strategy: StrategyType
    overall_score: float
    criteria_items: List[CriteriaItem]
    market_criteria: List[CriteriaItem]
    stock_criteria: List[CriteriaItem]
    erica_criteria: List[CriteriaItem]
    risk_criteria: List[CriteriaItem]
    summary: str
    recommendation_strength: str

class StrategyCriteriaAnalyzer:
    """Analyzes and evaluates strategy criteria"""
    
    def __init__(self):
        self.erica_framework = EricaDecisionFramework()
        
    def analyze_strategy_criteria(self, strategy_rec: StrategyRecommendation,
                                market_factors: MarketFactors,
                                stock_factors: StockSpecificFactors) -> StrategyCriteriaAnalysis:
        """
        Analyze all criteria for a strategy recommendation
        
        Args:
            strategy_rec: Strategy recommendation from decision tree
            market_factors: Current market factors
            stock_factors: Stock-specific factors
            
        Returns:
            Complete criteria analysis with visual indicators
        """
        
        # Analyze each category of criteria
        market_criteria = self._analyze_market_criteria(strategy_rec.primary_strategy, market_factors)
        stock_criteria = self._analyze_stock_criteria(strategy_rec.primary_strategy, stock_factors)
        erica_criteria = self._analyze_erica_criteria(strategy_rec.primary_strategy, strategy_rec.symbol, market_factors, stock_factors)
        risk_criteria = self._analyze_risk_criteria(strategy_rec.primary_strategy, market_factors, stock_factors)
        
        # Combine all criteria
        all_criteria = market_criteria + stock_criteria + erica_criteria + risk_criteria
        
        # Calculate overall score
        overall_score = sum(item.score for item in all_criteria) / len(all_criteria) if all_criteria else 0
        
        # Generate summary
        summary = self._generate_criteria_summary(strategy_rec.primary_strategy, all_criteria)
        
        # Determine recommendation strength
        recommendation_strength = self._determine_recommendation_strength(overall_score, all_criteria)
        
        return StrategyCriteriaAnalysis(
            symbol=strategy_rec.symbol,
            strategy=strategy_rec.primary_strategy,
            overall_score=overall_score,
            criteria_items=all_criteria,
            market_criteria=market_criteria,
            stock_criteria=stock_criteria,
            erica_criteria=erica_criteria,
            risk_criteria=risk_criteria,
            summary=summary,
            recommendation_strength=recommendation_strength
        )
    
    def _analyze_market_criteria(self, strategy: StrategyType, market_factors: MarketFactors) -> List[CriteriaItem]:
        """Analyze market environment criteria"""
        criteria = []
        
        # VIX Level Analysis
        vix_status, vix_score = self._evaluate_vix_for_strategy(strategy, market_factors.vix_level)
        criteria.append(CriteriaItem(
            name="VIX Level",
            description=f"Current VIX: {market_factors.vix_level:.1f}",
            status=vix_status,
            current_value=market_factors.vix_level,
            required_value=self._get_vix_requirement(strategy),
            score=vix_score,
            tooltip=f"VIX measures market volatility. {self._get_vix_tooltip(strategy)}",
            category="market"
        ))
        
        # Market Regime
        regime_status, regime_score = self._evaluate_market_regime(strategy, market_factors.market_regime)
        criteria.append(CriteriaItem(
            name="Market Regime",
            description=f"Current: {market_factors.market_regime.value}",
            status=regime_status,
            current_value=market_factors.market_regime.value,
            required_value=self._get_regime_requirement(strategy),
            score=regime_score,
            tooltip=f"Market trend direction. {self._get_regime_tooltip(strategy)}",
            category="market"
        ))
        
        # Volatility Regime
        vol_regime_status, vol_regime_score = self._evaluate_volatility_regime(strategy, market_factors.volatility_regime)
        criteria.append(CriteriaItem(
            name="Volatility Regime",
            description=f"Current: {market_factors.volatility_regime.value}",
            status=vol_regime_status,
            current_value=market_factors.volatility_regime.value,
            required_value=self._get_vol_regime_requirement(strategy),
            score=vol_regime_score,
            tooltip=f"Volatility environment. {self._get_vol_regime_tooltip(strategy)}",
            category="market"
        ))
        
        # Bullish/Bearish Factors
        sentiment_status, sentiment_score = self._evaluate_market_sentiment(strategy, market_factors)
        criteria.append(CriteriaItem(
            name="Market Sentiment",
            description=f"Bullish: {market_factors.bullish_factors:.0%}, Bearish: {market_factors.bearish_factors:.0%}",
            status=sentiment_status,
            current_value=market_factors.bullish_factors - market_factors.bearish_factors,
            required_value=self._get_sentiment_requirement(strategy),
            score=sentiment_score,
            tooltip=f"Overall market sentiment balance. {self._get_sentiment_tooltip(strategy)}",
            category="market"
        ))
        
        return criteria
    
    def _analyze_stock_criteria(self, strategy: StrategyType, stock_factors: StockSpecificFactors) -> List[CriteriaItem]:
        """Analyze stock-specific criteria"""
        criteria = []
        
        # IV Rank (normalize if provided as 0-100)
        iv_value = stock_factors.iv_rank
        if iv_value > 1.0:
            iv_value = iv_value / 100.0
        iv_status, iv_score = self._evaluate_iv_rank(strategy, iv_value)
        criteria.append(CriteriaItem(
            name="IV Rank",
            description=f"Current: {iv_value:.0%}",
            status=iv_status,
            current_value=iv_value,
            required_value=self._get_iv_requirement(strategy),
            score=iv_score,
            tooltip=f"Implied volatility percentile. {self._get_iv_tooltip(strategy)}",
            category="stock"
        ))
        
        # Technical Confluence
        tech_status, tech_score = self._evaluate_technical_confluence(strategy, stock_factors.technical_confluence_score)
        criteria.append(CriteriaItem(
            name="Technical Confluence",
            description=f"Score: {stock_factors.technical_confluence_score:.0%}",
            status=tech_status,
            current_value=stock_factors.technical_confluence_score,
            required_value=self._get_technical_requirement(strategy),
            score=tech_score,
            tooltip=f"Technical indicator alignment. {self._get_technical_tooltip(strategy)}",
            category="stock"
        ))
        
        # Earnings Proximity
        earnings_status, earnings_score = self._evaluate_earnings_proximity(strategy, stock_factors.earnings_days_away)
        criteria.append(CriteriaItem(
            name="Earnings Proximity",
            description=f"Days to earnings: {stock_factors.earnings_days_away}",
            status=earnings_status,
            current_value=stock_factors.earnings_days_away,
            required_value=self._get_earnings_requirement(strategy),
            score=earnings_score,
            tooltip=f"Distance from earnings announcement. {self._get_earnings_tooltip(strategy)}",
            category="stock"
        ))
        
        # Liquidity (using placeholder values since fields don't exist)
        options_volume = 50000  # Placeholder
        bid_ask_spread = 0.05   # Placeholder
        liquidity_status, liquidity_score = self._evaluate_liquidity(strategy, options_volume, bid_ask_spread)
        criteria.append(CriteriaItem(
            name="Options Liquidity",
            description=f"Volume: {options_volume:,}, Spread: {bid_ask_spread:.3f}",
            status=liquidity_status,
            current_value=options_volume,
            required_value=self._get_liquidity_requirement(strategy),
            score=liquidity_score,
            tooltip=f"Options market liquidity. {self._get_liquidity_tooltip(strategy)}",
            category="stock"
        ))
        
        return criteria
    
    def _analyze_erica_criteria(self, strategy: StrategyType, symbol: str,
                              market_factors: MarketFactors, stock_factors: StockSpecificFactors) -> List[CriteriaItem]:
        """Analyze Erica's specific strategy criteria"""
        criteria = []
        
        # Get Erica's suitability score
        suitability_score, reasoning = self.erica_framework.evaluate_strategy_suitability(
            strategy, market_factors, stock_factors, symbol
        )
        
        # Strategy-specific criteria based on Erica's rules
        if strategy == StrategyType.COVERED_CALL:
            criteria.extend(self._analyze_covered_call_criteria(stock_factors, suitability_score))
        elif strategy == StrategyType.CREDIT_SPREAD:
            criteria.extend(self._analyze_credit_spread_criteria(market_factors, stock_factors, suitability_score))
        elif strategy == StrategyType.LEAPS:
            criteria.extend(self._analyze_leaps_criteria(market_factors, stock_factors, suitability_score))
        elif strategy == StrategyType.PREMIUM_SELLING:
            criteria.extend(self._analyze_premium_selling_criteria(market_factors, stock_factors, suitability_score))
        
        return criteria
    
    def _analyze_risk_criteria(self, strategy: StrategyType, market_factors: MarketFactors,
                             stock_factors: StockSpecificFactors) -> List[CriteriaItem]:
        """Analyze risk management criteria"""
        criteria = []
        
        # Uncertainty Score
        uncertainty_status, uncertainty_score = self._evaluate_uncertainty(strategy, market_factors.uncertainty_factors)
        criteria.append(CriteriaItem(
            name="Market Uncertainty",
            description=f"Uncertainty: {market_factors.uncertainty_factors:.0%}",
            status=uncertainty_status,
            current_value=market_factors.uncertainty_factors,
            required_value=self._get_uncertainty_requirement(strategy),
            score=uncertainty_score,
            tooltip=f"Market uncertainty level. {self._get_uncertainty_tooltip(strategy)}",
            category="risk"
        ))
        
        # Position Sizing Appropriateness
        sizing_status, sizing_score = self._evaluate_position_sizing(strategy, market_factors, stock_factors)
        criteria.append(CriteriaItem(
            name="Position Sizing",
            description="Based on volatility and risk",
            status=sizing_status,
            current_value=sizing_score,
            required_value=0.7,
            score=sizing_score,
            tooltip=f"Appropriate position size for current conditions. {self._get_sizing_tooltip(strategy)}",
            category="risk"
        ))
        
        return criteria

    # Helper methods for criteria evaluation

    def _evaluate_vix_for_strategy(self, strategy: StrategyType, vix_level: float) -> Tuple[CriteriaStatus, float]:
        """Evaluate VIX level for strategy suitability"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            # Premium selling strategies prefer higher VIX
            if vix_level >= 25:
                return CriteriaStatus.MET, 1.0
            elif vix_level >= 20:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.3
        elif strategy == StrategyType.LEAPS:
            # LEAPS prefer lower VIX
            if vix_level <= 15:
                return CriteriaStatus.MET, 1.0
            elif vix_level <= 20:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.3
        else:
            return CriteriaStatus.NOT_APPLICABLE, 0.5

    def _evaluate_market_regime(self, strategy: StrategyType, regime) -> Tuple[CriteriaStatus, float]:
        """Evaluate market regime for strategy"""
        # Prefer enum-based logic if MarketRegime is passed; fallback to string matching
        try:
            from market_analysis_engine import MarketRegime as MR  # local import to avoid cycles
        except Exception:
            MR = None

        if MR is not None and isinstance(regime, MR):
            if strategy == StrategyType.LEAPS:
                if regime == MR.BULL_MARKET:
                    return CriteriaStatus.MET, 1.0
                elif regime in (MR.SIDEWAYS_MARKET, MR.TRANSITION):
                    return CriteriaStatus.BORDERLINE, 0.6
                else:
                    return CriteriaStatus.NOT_MET, 0.2
            elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
                if regime in (MR.SIDEWAYS_MARKET, MR.TRANSITION):
                    return CriteriaStatus.MET, 1.0
                else:
                    return CriteriaStatus.BORDERLINE, 0.7
            elif strategy == StrategyType.CREDIT_SPREAD:
                if regime in (MR.BULL_MARKET, MR.SIDEWAYS_MARKET, MR.TRANSITION):
                    return CriteriaStatus.MET, 1.0
                else:
                    return CriteriaStatus.NOT_MET, 0.3
            else:
                return CriteriaStatus.NOT_APPLICABLE, 0.5

        # Fallback: string-based, case-insensitive
        regime_str = (regime.value if hasattr(regime, 'value') else str(regime)).upper()

        if strategy == StrategyType.LEAPS:
            if 'BULL' in regime_str:
                return CriteriaStatus.MET, 1.0
            elif 'NEUTRAL' in regime_str or 'SIDEWAYS' in regime_str or 'TRANSITION' in regime_str:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.2
        elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            if 'NEUTRAL' in regime_str or 'SIDEWAYS' in regime_str or 'TRANSITION' in regime_str:
                return CriteriaStatus.MET, 1.0
            else:
                return CriteriaStatus.BORDERLINE, 0.7
        elif strategy == StrategyType.CREDIT_SPREAD:
            if 'BULL' in regime_str or 'NEUTRAL' in regime_str or 'SIDEWAYS' in regime_str or 'TRANSITION' in regime_str:
                return CriteriaStatus.MET, 1.0
            else:
                return CriteriaStatus.NOT_MET, 0.3
        else:
            return CriteriaStatus.NOT_APPLICABLE, 0.5

    def _evaluate_volatility_regime(self, strategy: StrategyType, vol_regime) -> Tuple[CriteriaStatus, float]:
        """Evaluate volatility regime for strategy"""
        vol_str = vol_regime.value if hasattr(vol_regime, 'value') else str(vol_regime)

        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            if 'HIGH' in vol_str:
                return CriteriaStatus.MET, 1.0
            elif 'NORMAL' in vol_str:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.4
        elif strategy == StrategyType.LEAPS:
            if 'LOW' in vol_str:
                return CriteriaStatus.MET, 1.0
            elif 'NORMAL' in vol_str:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.3
        else:
            return CriteriaStatus.NOT_APPLICABLE, 0.5

    def _evaluate_market_sentiment(self, strategy: StrategyType, market_factors: MarketFactors) -> Tuple[CriteriaStatus, float]:
        """Evaluate market sentiment for strategy"""
        sentiment_balance = market_factors.bullish_factors - market_factors.bearish_factors

        if strategy == StrategyType.LEAPS:
            if sentiment_balance > 0.2:
                return CriteriaStatus.MET, 1.0
            elif sentiment_balance > 0:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.3
        elif strategy == StrategyType.CREDIT_SPREAD:
            if sentiment_balance > 0:
                return CriteriaStatus.MET, 1.0
            elif sentiment_balance > -0.1:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.3
        else:
            # Neutral strategies are less sensitive to sentiment
            return CriteriaStatus.MET, 0.8

    def _evaluate_iv_rank(self, strategy: StrategyType, iv_rank: float) -> Tuple[CriteriaStatus, float]:
        """Evaluate IV rank for strategy"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            if iv_rank >= 0.7:
                return CriteriaStatus.MET, 1.0
            elif iv_rank >= 0.5:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.3
        elif strategy == StrategyType.LEAPS:
            if iv_rank <= 0.4:
                return CriteriaStatus.MET, 1.0
            elif iv_rank <= 0.6:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.2
        else:
            return CriteriaStatus.NOT_APPLICABLE, 0.5

    def _evaluate_technical_confluence(self, strategy: StrategyType, tech_score: float) -> Tuple[CriteriaStatus, float]:
        """Evaluate technical confluence score"""
        if strategy == StrategyType.LEAPS:
            # LEAPS need strong technical confluence
            if tech_score >= 0.8:
                return CriteriaStatus.MET, 1.0
            elif tech_score >= 0.6:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.3
        else:
            # Other strategies are less dependent on technicals
            if tech_score >= 0.6:
                return CriteriaStatus.MET, 1.0
            elif tech_score >= 0.4:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.4

    def _evaluate_earnings_proximity(self, strategy: StrategyType, days_to_earnings: int) -> Tuple[CriteriaStatus, float]:
        """Evaluate earnings proximity"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD]:
            # These strategies should avoid earnings
            if days_to_earnings > 45 or days_to_earnings < 0:
                return CriteriaStatus.MET, 1.0
            elif days_to_earnings > 30:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.2
        elif strategy == StrategyType.LEAPS:
            # LEAPS can handle earnings better
            if days_to_earnings > 14 or days_to_earnings < 0:
                return CriteriaStatus.MET, 1.0
            else:
                return CriteriaStatus.BORDERLINE, 0.6
        else:
            return CriteriaStatus.NOT_APPLICABLE, 0.8

    def _evaluate_liquidity(self, strategy: StrategyType, options_volume: int, bid_ask_spread: float) -> Tuple[CriteriaStatus, float]:
        """Evaluate options liquidity"""
        # All strategies need good liquidity
        if options_volume > 1000 and bid_ask_spread < 0.05:
            return CriteriaStatus.MET, 1.0
        elif options_volume > 500 and bid_ask_spread < 0.10:
            return CriteriaStatus.BORDERLINE, 0.7
        else:
            return CriteriaStatus.NOT_MET, 0.3

    def _evaluate_uncertainty(self, strategy: StrategyType, uncertainty_score: float) -> Tuple[CriteriaStatus, float]:
        """Evaluate market uncertainty"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            # These strategies can handle uncertainty better
            if uncertainty_score <= 0.6:
                return CriteriaStatus.MET, 1.0
            elif uncertainty_score <= 0.8:
                return CriteriaStatus.BORDERLINE, 0.7
            else:
                return CriteriaStatus.NOT_MET, 0.4
        else:
            # Directional strategies prefer lower uncertainty
            if uncertainty_score <= 0.4:
                return CriteriaStatus.MET, 1.0
            elif uncertainty_score <= 0.6:
                return CriteriaStatus.BORDERLINE, 0.6
            else:
                return CriteriaStatus.NOT_MET, 0.2

    def _evaluate_position_sizing(self, strategy: StrategyType, market_factors: MarketFactors,
                                stock_factors: StockSpecificFactors) -> Tuple[CriteriaStatus, float]:
        """Evaluate position sizing appropriateness"""
        # Consider volatility and uncertainty for position sizing
        vol_factor = 1.0 - (market_factors.vix_level - 15) / 30  # Normalize VIX
        uncertainty_factor = 1.0 - market_factors.uncertainty_factors

        sizing_score = (vol_factor + uncertainty_factor) / 2
        sizing_score = max(0.1, min(1.0, sizing_score))

        if sizing_score >= 0.7:
            return CriteriaStatus.MET, sizing_score
        elif sizing_score >= 0.5:
            return CriteriaStatus.BORDERLINE, sizing_score
        else:
            return CriteriaStatus.NOT_MET, sizing_score

    # Strategy-specific criteria analysis methods

    def _analyze_covered_call_criteria(self, stock_factors: StockSpecificFactors, suitability_score: float) -> List[CriteriaItem]:
        """Analyze covered call specific criteria"""
        criteria = []

        # Premium Collection Opportunity
        premium_score = min(1.0, stock_factors.iv_rank * 2)  # Higher IV = better premium
        premium_status = CriteriaStatus.MET if premium_score >= 0.7 else CriteriaStatus.BORDERLINE if premium_score >= 0.5 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Premium Collection",
            description=f"Expected premium quality: {premium_score:.0%}",
            status=premium_status,
            current_value=premium_score,
            required_value=0.7,
            score=premium_score,
            tooltip="Higher IV rank provides better premium collection opportunities for covered calls",
            category="erica"
        ))

        # Assignment Comfort (based on technical strength)
        assignment_score = stock_factors.technical_confluence_score
        assignment_status = CriteriaStatus.MET if assignment_score >= 0.6 else CriteriaStatus.BORDERLINE if assignment_score >= 0.4 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Assignment Comfort",
            description=f"Technical strength: {assignment_score:.0%}",
            status=assignment_status,
            current_value=assignment_score,
            required_value=0.6,
            score=assignment_score,
            tooltip="Strong technical position indicates comfort with potential assignment",
            category="erica"
        ))

        return criteria

    def _analyze_credit_spread_criteria(self, market_factors: MarketFactors, stock_factors: StockSpecificFactors, suitability_score: float) -> List[CriteriaItem]:
        """Analyze credit spread specific criteria"""
        criteria = []

        # Bullish Bias Requirement
        bullish_bias = getattr(market_factors, 'bullish_factors', 0.5) > getattr(market_factors, 'bearish_factors', 0.5)
        bias_status = CriteriaStatus.MET if bullish_bias else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Bullish Bias",
            description=f"Market bias: {'Bullish' if bullish_bias else 'Bearish'}",
            status=bias_status,
            current_value=bullish_bias,
            required_value=True,
            score=1.0 if bullish_bias else 0.2,
            tooltip="Credit spreads require bullish or neutral market bias for optimal performance",
            category="erica"
        ))

        # High Probability Setup
        iv_val = stock_factors.iv_rank if stock_factors.iv_rank <= 1 else stock_factors.iv_rank / 100.0
        uncertainty_factor = getattr(market_factors, 'uncertainty_factors', 0.5)
        prob_score = min(1.0, (iv_val + (1.0 - uncertainty_factor)) / 2)
        prob_status = CriteriaStatus.MET if prob_score >= 0.7 else CriteriaStatus.BORDERLINE if prob_score >= 0.5 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="High Probability Setup",
            description=f"Probability score: {prob_score:.0%}",
            status=prob_status,
            current_value=prob_score,
            required_value=0.7,
            score=prob_score,
            tooltip="Combination of high IV and low uncertainty creates high-probability setups",
            category="erica"
        ))

        return criteria

    def _analyze_leaps_criteria(self, market_factors: MarketFactors, stock_factors: StockSpecificFactors, suitability_score: float) -> List[CriteriaItem]:
        """Analyze LEAPS specific criteria"""
        criteria = []

        # Strong Trend Requirement
        trend_strength = stock_factors.technical_confluence_score
        trend_status = CriteriaStatus.MET if trend_strength >= 0.8 else CriteriaStatus.BORDERLINE if trend_strength >= 0.6 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Strong Trend",
            description=f"Trend strength: {trend_strength:.0%}",
            status=trend_status,
            current_value=trend_strength,
            required_value=0.8,
            score=trend_strength,
            tooltip="LEAPS require strong, sustained trends for optimal performance",
            category="erica"
        ))

        # Low Volatility Environment
        low_vol_score = 1.0 - min(1.0, market_factors.vix_level / 30)
        vol_status = CriteriaStatus.MET if low_vol_score >= 0.7 else CriteriaStatus.BORDERLINE if low_vol_score >= 0.5 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Low Volatility Entry",
            description=f"Vol environment score: {low_vol_score:.0%}",
            status=vol_status,
            current_value=low_vol_score,
            required_value=0.7,
            score=low_vol_score,
            tooltip="LEAPS are best purchased in low volatility environments",
            category="erica"
        ))

        return criteria

    def _analyze_premium_selling_criteria(self, market_factors: MarketFactors, stock_factors: StockSpecificFactors, suitability_score: float) -> List[CriteriaItem]:
        """Analyze premium selling specific criteria"""
        criteria = []

        # High Volatility Requirement
        high_vol_score = min(1.0, market_factors.vix_level / 25)
        vol_status = CriteriaStatus.MET if high_vol_score >= 0.8 else CriteriaStatus.BORDERLINE if high_vol_score >= 0.6 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="High Volatility",
            description=f"Volatility score: {high_vol_score:.0%}",
            status=vol_status,
            current_value=high_vol_score,
            required_value=0.8,
            score=high_vol_score,
            tooltip="Premium selling strategies thrive in high volatility environments",
            category="erica"
        ))

        # Systematic Approach Suitability
        iv_val = stock_factors.iv_rank if stock_factors.iv_rank <= 1 else stock_factors.iv_rank / 100.0
        options_vol = getattr(stock_factors, 'options_volume', 1000)
        systematic_score = min(1.0, (iv_val + options_vol / 2000) / 2)
        systematic_status = CriteriaStatus.MET if systematic_score >= 0.7 else CriteriaStatus.BORDERLINE if systematic_score >= 0.5 else CriteriaStatus.NOT_MET

        criteria.append(CriteriaItem(
            name="Systematic Suitability",
            description=f"Systematic score: {systematic_score:.0%}",
            status=systematic_status,
            current_value=systematic_score,
            required_value=0.7,
            score=systematic_score,
            tooltip="Good liquidity and consistent IV patterns support systematic premium selling",
            category="erica"
        ))

        return criteria

    # Requirement and tooltip helper methods

    def _get_vix_requirement(self, strategy: StrategyType) -> str:
        """Get VIX requirement description for strategy"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return ">20 (prefer >25)"
        elif strategy == StrategyType.LEAPS:
            return "<20 (prefer <15)"
        else:
            return "N/A"

    def _get_vix_tooltip(self, strategy: StrategyType) -> str:
        """Get VIX tooltip for strategy"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return "Higher VIX provides better premium collection opportunities."
        elif strategy == StrategyType.LEAPS:
            return "Lower VIX reduces option costs for long-term positions."
        else:
            return "VIX impact varies by strategy."

    def _get_regime_requirement(self, strategy: StrategyType) -> str:
        """Get market regime requirement for strategy"""
        if strategy == StrategyType.LEAPS:
            return "Bull Market"
        elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            return "Neutral/Sideways"
        elif strategy == StrategyType.CREDIT_SPREAD:
            return "Bull/Neutral"
        else:
            return "Any"

    def _get_regime_tooltip(self, strategy: StrategyType) -> str:
        """Get regime tooltip for strategy"""
        if strategy == StrategyType.LEAPS:
            return "LEAPS perform best in sustained bull markets."
        elif strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            return "Income strategies work well in range-bound markets."
        elif strategy == StrategyType.CREDIT_SPREAD:
            return "Credit spreads need bullish or neutral bias."
        else:
            return "Market regime impact varies."

    def _get_vol_regime_requirement(self, strategy: StrategyType) -> str:
        """Get volatility regime requirement"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return "High Volatility"
        elif strategy == StrategyType.LEAPS:
            return "Low Volatility"
        else:
            return "Any"

    def _get_vol_regime_tooltip(self, strategy: StrategyType) -> str:
        """Get volatility regime tooltip"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return "High volatility increases premium collection opportunities."
        elif strategy == StrategyType.LEAPS:
            return "Low volatility reduces entry costs for long positions."
        else:
            return "Volatility impact varies by strategy."

    def _get_sentiment_requirement(self, strategy: StrategyType) -> str:
        """Get sentiment requirement"""
        if strategy == StrategyType.LEAPS:
            return "Bullish"
        elif strategy == StrategyType.CREDIT_SPREAD:
            return "Bullish/Neutral"
        else:
            return "Any"

    def _get_sentiment_tooltip(self, strategy: StrategyType) -> str:
        """Get sentiment tooltip"""
        if strategy == StrategyType.LEAPS:
            return "LEAPS require bullish sentiment for directional moves."
        elif strategy == StrategyType.CREDIT_SPREAD:
            return "Credit spreads need positive or neutral sentiment."
        else:
            return "Sentiment impact varies by strategy."

    def _get_iv_requirement(self, strategy: StrategyType) -> str:
        """Get IV rank requirement"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return ">70%"
        elif strategy == StrategyType.LEAPS:
            return "<40%"
        else:
            return "Any"

    def _get_iv_tooltip(self, strategy: StrategyType) -> str:
        """Get IV tooltip"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return "High IV rank provides better premium selling opportunities."
        elif strategy == StrategyType.LEAPS:
            return "Low IV rank reduces costs for buying long options."
        else:
            return "IV impact varies by strategy."

    def _get_technical_requirement(self, strategy: StrategyType) -> str:
        """Get technical requirement"""
        if strategy == StrategyType.LEAPS:
            return ">80%"
        else:
            return ">60%"

    def _get_technical_tooltip(self, strategy: StrategyType) -> str:
        """Get technical tooltip"""
        if strategy == StrategyType.LEAPS:
            return "LEAPS require strong technical confluence for directional conviction."
        else:
            return "Technical analysis supports strategy timing and execution."

    def _get_earnings_requirement(self, strategy: StrategyType) -> str:
        """Get earnings requirement"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD]:
            return ">45 days or past"
        elif strategy == StrategyType.LEAPS:
            return ">14 days or past"
        else:
            return "Any"

    def _get_earnings_tooltip(self, strategy: StrategyType) -> str:
        """Get earnings tooltip"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD]:
            return "Avoid earnings volatility for premium selling strategies."
        elif strategy == StrategyType.LEAPS:
            return "LEAPS can handle earnings volatility better due to longer timeframe."
        else:
            return "Earnings impact varies by strategy."

    def _get_liquidity_requirement(self, strategy: StrategyType) -> str:
        """Get liquidity requirement"""
        return ">1000 volume, <0.05 spread"

    def _get_liquidity_tooltip(self, strategy: StrategyType) -> str:
        """Get liquidity tooltip"""
        return "Good liquidity ensures efficient entry and exit execution."

    def _get_uncertainty_requirement(self, strategy: StrategyType) -> str:
        """Get uncertainty requirement"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            return "<60%"
        else:
            return "<40%"

    def _get_uncertainty_tooltip(self, strategy: StrategyType) -> str:
        """Get uncertainty tooltip"""
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            return "Income strategies can handle moderate uncertainty."
        else:
            return "Directional strategies prefer low uncertainty environments."

    def _get_sizing_tooltip(self, strategy: StrategyType) -> str:
        """Get position sizing tooltip"""
        return "Position size adjusted for current volatility and market conditions."

    def _generate_criteria_summary(self, strategy: StrategyType, criteria: List[CriteriaItem]) -> str:
        """Generate summary of criteria analysis"""
        met_count = sum(1 for c in criteria if c.status == CriteriaStatus.MET)
        total_count = len([c for c in criteria if c.status != CriteriaStatus.NOT_APPLICABLE])

        if total_count == 0:
            return "No applicable criteria found."

        met_percentage = met_count / total_count

        if met_percentage >= 0.8:
            return f"Excellent setup: {met_count}/{total_count} criteria met. Strong recommendation."
        elif met_percentage >= 0.6:
            return f"Good setup: {met_count}/{total_count} criteria met. Solid recommendation."
        elif met_percentage >= 0.4:
            return f"Moderate setup: {met_count}/{total_count} criteria met. Proceed with caution."
        else:
            return f"Weak setup: {met_count}/{total_count} criteria met. Consider alternatives."

    def _determine_recommendation_strength(self, overall_score: float, criteria: List[CriteriaItem]) -> str:
        """Determine recommendation strength"""
        if overall_score >= 0.8:
            return "STRONG"
        elif overall_score >= 0.6:
            return "MODERATE"
        elif overall_score >= 0.4:
            return "WEAK"
        else:
            return "AVOID"


class StrategyCriteriaWidget(ttk.Frame):
    """Visual widget for displaying strategy criteria with color-coded indicators"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.analyzer = StrategyCriteriaAnalyzer()
        self.current_analysis = None

        self.setup_widget()

    def setup_widget(self):
        """Setup the criteria display widget"""

        # Header
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=tk.X, padx=5, pady=5)

        self.title_label = ttk.Label(header_frame, text="Strategy Criteria Analysis",
                                   font=('Arial', 12, 'bold'))
        self.title_label.pack(side=tk.LEFT)

        self.strength_label = ttk.Label(header_frame, text="",
                                      font=('Arial', 10, 'bold'))
        self.strength_label.pack(side=tk.RIGHT)

        # Overall score
        score_frame = ttk.Frame(self)
        score_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(score_frame, text="Overall Score:").pack(side=tk.LEFT)
        self.score_label = ttk.Label(score_frame, text="--", font=('Arial', 10, 'bold'))
        self.score_label.pack(side=tk.LEFT, padx=(5, 0))

        self.score_bar = ttk.Progressbar(score_frame, length=200, mode='determinate')
        self.score_bar.pack(side=tk.LEFT, padx=(10, 0))

        # Criteria notebook
        self.criteria_notebook = ttk.Notebook(self)
        self.criteria_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs for each criteria category
        self.market_frame = ttk.Frame(self.criteria_notebook)
        self.stock_frame = ttk.Frame(self.criteria_notebook)
        self.erica_frame = ttk.Frame(self.criteria_notebook)
        self.risk_frame = ttk.Frame(self.criteria_notebook)

        self.criteria_notebook.add(self.market_frame, text="Market")
        self.criteria_notebook.add(self.stock_frame, text="Stock")
        self.criteria_notebook.add(self.erica_frame, text="Erica's Rules")
        self.criteria_notebook.add(self.risk_frame, text="Risk")

        # Setup criteria display areas
        self.market_tree = self.create_criteria_tree(self.market_frame)
        self.stock_tree = self.create_criteria_tree(self.stock_frame)
        self.erica_tree = self.create_criteria_tree(self.erica_frame)
        self.risk_tree = self.create_criteria_tree(self.risk_frame)

        # Summary
        summary_frame = ttk.LabelFrame(self, text="Summary")
        summary_frame.pack(fill=tk.X, padx=5, pady=5)

        self.summary_text = tk.Text(summary_frame, height=3, wrap=tk.WORD,
                                  font=('Arial', 9))
        self.summary_text.pack(fill=tk.X, padx=5, pady=5)

    def create_criteria_tree(self, parent):
        """Create a treeview for displaying criteria"""

        # Create treeview with scrollbar
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        tree = ttk.Treeview(tree_frame, columns=('Status', 'Current', 'Required', 'Score'),
                           show='tree headings', height=8)

        # Configure columns
        tree.heading('#0', text='Criteria')
        tree.heading('Status', text='Status')
        tree.heading('Current', text='Current')
        tree.heading('Required', text='Required')
        tree.heading('Score', text='Score')

        tree.column('#0', width=150)
        tree.column('Status', width=80)
        tree.column('Current', width=100)
        tree.column('Required', width=100)
        tree.column('Score', width=60)

        # Scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        return tree

    def update_criteria_display(self, strategy_rec: StrategyRecommendation,
                              market_factors: MarketFactors,
                              stock_factors: StockSpecificFactors):
        """Update the criteria display with new analysis"""

        # Analyze criteria
        self.current_analysis = self.analyzer.analyze_strategy_criteria(
            strategy_rec, market_factors, stock_factors
        )

        # Update header
        strategy_name = strategy_rec.primary_strategy.value.replace('_', ' ').title()
        self.title_label.config(text=f"{strategy_rec.symbol} - {strategy_name} Criteria")

        # Update strength indicator
        strength = self.current_analysis.recommendation_strength
        strength_color = self.get_strength_color(strength)
        self.strength_label.config(text=strength, foreground=strength_color)

        # Update overall score
        score_pct = self.current_analysis.overall_score * 100
        self.score_label.config(text=f"{score_pct:.0f}%")
        self.score_bar['value'] = score_pct

        # Update criteria trees
        self.populate_criteria_tree(self.market_tree, self.current_analysis.market_criteria)
        self.populate_criteria_tree(self.stock_tree, self.current_analysis.stock_criteria)
        self.populate_criteria_tree(self.erica_tree, self.current_analysis.erica_criteria)
        self.populate_criteria_tree(self.risk_tree, self.current_analysis.risk_criteria)

        # Update summary
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(tk.END, self.current_analysis.summary)

    def populate_criteria_tree(self, tree, criteria_items: List[CriteriaItem]):
        """Populate a criteria tree with items"""

        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add criteria items
        for criteria in criteria_items:
            status_symbol = self.get_status_symbol(criteria.status)
            status_color = self.get_status_color(criteria.status)

            current_val = self.format_value(criteria.current_value)
            required_val = self.format_value(criteria.required_value)
            score_val = f"{criteria.score:.0%}"

            item_id = tree.insert('', 'end',
                                text=criteria.name,
                                values=(status_symbol, current_val, required_val, score_val))

            # Set row color based on status
            tree.set(item_id, 'Status', status_symbol)

            # Add tooltip (would need additional implementation for full tooltip support)

    def get_status_symbol(self, status: CriteriaStatus) -> str:
        """Get symbol for criteria status"""
        if status == CriteriaStatus.MET:
            return "✓"
        elif status == CriteriaStatus.NOT_MET:
            return "✗"
        elif status == CriteriaStatus.BORDERLINE:
            return "⚠"
        else:
            return "—"

    def get_status_color(self, status: CriteriaStatus) -> str:
        """Get color for criteria status"""
        if status == CriteriaStatus.MET:
            return "green"
        elif status == CriteriaStatus.NOT_MET:
            return "red"
        elif status == CriteriaStatus.BORDERLINE:
            return "orange"
        else:
            return "gray"

    def get_strength_color(self, strength: str) -> str:
        """Get color for recommendation strength"""
        if strength == "STRONG":
            return "green"
        elif strength == "MODERATE":
            return "blue"
        elif strength == "WEAK":
            return "orange"
        else:
            return "red"

    def format_value(self, value: Any) -> str:
        """Format value for display"""
        if isinstance(value, float):
            if 0 <= value <= 1:
                return f"{value:.0%}"
            else:
                return f"{value:.2f}"
        elif isinstance(value, bool):
            return "Yes" if value else "No"
        elif value is None:
            return "N/A"
        else:
            return str(value)
