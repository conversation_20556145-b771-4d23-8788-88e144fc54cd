"""
Desktop Application for AI-Powered Daily Stock Investment Planning System
Enhanced with Erica's Trading Strategies (@AbundantlyErica)

A comprehensive GUI application built with tkinter that provides:
- Real-time market data dashboard
- Interactive strategy analysis
- Daily investment recommendations
- Risk management tools
- Settings and configuration
- Data export capabilities

Usage: python desktop_app.py
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import webbrowser

# Import our trading system modules
from daily_outline import resolve_fmp_key, SYMBOLS_DEFAULT
from daily_recommendations import DailyRecommendationGenerator
from risk_management import RiskManager, RiskParameters
from erica_strategies import StrategyParameters
from strategy_criteria_display import StrategyCriteriaWidget, StrategyCriteriaAnalyzer
from intelligent_strategy_engine import IntelligentStrategyEngine
from market_analysis_engine import MarketAnalysisEngine
from best_strategy_dashboard import BestStrategyDashboard, create_best_strategy_dashboard
from ai_assistant import AIAssistantGUI, AIConversationManager, AIAssistantConfig, DataAccessLayer
from realtime_data_manager import RealTimeDataManager, DataUpdate

class TradingSystemGUI:
    """Main GUI application for the trading system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI-Powered Daily Stock Investment Planning System - Erica's Strategies")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Application state
        self.api_key = resolve_fmp_key(None)  # Try to get API key from environment/file
        self.account_size = 100000
        self.symbols = SYMBOLS_DEFAULT.copy()
        self.last_update = None
        self.current_data = {}
        self.auto_refresh = False  # Disabled by default for performance
        self.refresh_interval = 300  # 5 minutes
        self._refresh_in_progress = False  # Prevent concurrent refreshes

        # Enhanced analysis components
        self.intelligent_engine = None
        self.market_analyzer = None
        self.criteria_analyzer = StrategyCriteriaAnalyzer()
        self.current_strategy_recommendations = {}

        # AI Assistant components
        self.ai_assistant = None
        self.ai_conversation_manager = None
        self.openai_api_key = None

        # Real-time data manager
        self.data_manager = None
        
        # Initialize components
        self.setup_styles()
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()
        
        # Load saved settings
        self.load_settings()

        # Update API status
        self.update_api_status()

        # Start with welcome screen
        self.show_welcome_screen()
    
    def setup_styles(self):
        """Configure ttk styles for better appearance"""
        style = ttk.Style()
        
        # Configure notebook tabs
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # Configure frames
        style.configure('Card.TFrame', relief='raised', borderwidth=1)
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Subheader.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Warning.TLabel', foreground='orange')
        style.configure('Error.TLabel', foreground='red')
    
    def create_menu(self):
        """Create the application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Analysis", command=self.new_analysis)
        file_menu.add_command(label="Export Report", command=self.export_report)
        file_menu.add_command(label="Save Settings", command=self.save_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Settings", command=self.show_settings)
        tools_menu.add_command(label="Test API Connection", command=self.test_api)
        tools_menu.add_command(label="Run System Tests", command=self.run_tests)
        
        # Analysis menu (make it prominent)
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🚀 ANALYZE", menu=analysis_menu)
        analysis_menu.add_command(label="🚀 Run Full Analysis", command=self.run_analysis, accelerator="F5")
        analysis_menu.add_separator()
        analysis_menu.add_command(label="🔄 Refresh Data", command=self.refresh_data)
        analysis_menu.add_checkbutton(label="⚙️ Auto Refresh", variable=tk.BooleanVar(value=self.auto_refresh),
                                     command=self.toggle_auto_refresh)

        # Bind F5 key to run analysis
        self.root.bind('<F5>', lambda e: self.run_analysis())

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Dashboard", command=lambda: self.notebook.select(0))
        view_menu.add_command(label="Best Strategies", command=lambda: self.notebook.select(1))
        view_menu.add_command(label="Strategy Analysis", command=lambda: self.notebook.select(2))
        view_menu.add_command(label="Daily Recommendations", command=lambda: self.notebook.select(3))
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="Strategy Guide", command=self.show_strategy_guide)
        help_menu.add_command(label="Erica's YouTube", command=lambda: webbrowser.open("https://www.youtube.com/@AbundantlyErica"))
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create the application toolbar"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # Quick action buttons
        ttk.Button(toolbar, text="🔄 Refresh", command=self.refresh_data).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="📊 Analyze", command=self.run_analysis).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="⚙️ Settings", command=self.show_settings).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # Account info
        ttk.Label(toolbar, text="Account:").pack(side=tk.LEFT, padx=2)
        self.account_label = ttk.Label(toolbar, text=f"${self.account_size:,}")
        self.account_label.pack(side=tk.LEFT, padx=2)
        
        # API status
        ttk.Label(toolbar, text="API:").pack(side=tk.LEFT, padx=(20, 2))
        self.api_status = ttk.Label(toolbar, text="Not Connected", style='Error.TLabel')
        self.api_status.pack(side=tk.LEFT, padx=2)
        
        # Last update
        ttk.Label(toolbar, text="Updated:").pack(side=tk.LEFT, padx=(20, 2))
        self.update_label = ttk.Label(toolbar, text="Never")
        self.update_label.pack(side=tk.LEFT, padx=2)
    
    def create_main_layout(self):
        """Create the main application layout with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Dashboard tab
        self.dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="📊 Dashboard")
        
        # Best Strategies tab
        self.best_strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.best_strategy_frame, text="⭐ Best Strategies")

        # Strategy Analysis tab
        self.strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.strategy_frame, text="🎯 Strategy Analysis")

        # Daily Recommendations tab
        self.recommendations_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.recommendations_frame, text="📋 Daily Recommendations")
        
        # Risk Management tab
        self.risk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.risk_frame, text="🛡️ Risk Management")
        
        # AI Assistant tab
        self.ai_assistant_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.ai_assistant_frame, text="🤖 AI Assistant")

        # Settings tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="⚙️ Settings")
        
        # Initialize tab contents
        self.setup_dashboard_tab()
        self.setup_best_strategy_tab()
        self.setup_strategy_tab()
        self.setup_recommendations_tab()
        self.setup_risk_tab()
        self.setup_ai_assistant_tab()
        self.setup_settings_tab()
    
    def create_status_bar(self):
        """Create the status bar at the bottom"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_text = ttk.Label(self.status_bar, text="Ready")
        self.status_text.pack(side=tk.LEFT, padx=5)
        
        # Progress bar for operations
        self.progress = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=5)
    
    def setup_dashboard_tab(self):
        """Setup the market data dashboard tab"""
        # Create main container with scrollbar
        canvas = tk.Canvas(self.dashboard_frame)
        scrollbar = ttk.Scrollbar(self.dashboard_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Market overview section
        overview_frame = ttk.LabelFrame(scrollable_frame, text="Market Overview", style='Card.TFrame')
        overview_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.market_overview = ttk.Label(overview_frame, text="📊 Click '🚀 ANALYZE' in the menu or press F5 to load market data",
                                         font=('Arial', 10), foreground='blue')
        self.market_overview.pack(padx=10, pady=10)
        
        # Individual stock cards
        self.stock_cards = {}
        stocks_frame = ttk.Frame(scrollable_frame)
        stocks_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        for i, symbol in enumerate(self.symbols):
            card = self.create_stock_card(stocks_frame, symbol)
            card.grid(row=i//2, column=i%2, padx=5, pady=5, sticky="ew")
            self.stock_cards[symbol] = card
        
        # Configure grid weights
        stocks_frame.columnconfigure(0, weight=1)
        stocks_frame.columnconfigure(1, weight=1)
    
    def create_stock_card(self, parent, symbol):
        """Create a card widget for displaying stock information"""
        card = ttk.LabelFrame(parent, text=symbol, style='Card.TFrame')
        
        # Price info
        price_frame = ttk.Frame(card)
        price_frame.pack(fill=tk.X, padx=5, pady=2)
        
        price_label = ttk.Label(price_frame, text="$0.00", font=('Arial', 14, 'bold'))
        price_label.pack(side=tk.LEFT)
        
        change_label = ttk.Label(price_frame, text="(0.00%)", font=('Arial', 10))
        change_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Key metrics
        metrics_frame = ttk.Frame(card)
        metrics_frame.pack(fill=tk.X, padx=5, pady=2)
        
        volume_label = ttk.Label(metrics_frame, text="Volume: 0", font=('Arial', 9))
        volume_label.pack(anchor=tk.W)
        
        range_label = ttk.Label(metrics_frame, text="52W Range: $0.00 - $0.00", font=('Arial', 9))
        range_label.pack(anchor=tk.W)
        
        # Strategy signal
        signal_frame = ttk.Frame(card)
        signal_frame.pack(fill=tk.X, padx=5, pady=2)
        
        signal_label = ttk.Label(signal_frame, text="Signal: Analyzing...", font=('Arial', 9, 'bold'))
        signal_label.pack(anchor=tk.W)
        
        confidence_label = ttk.Label(signal_frame, text="Confidence: --", font=('Arial', 9))
        confidence_label.pack(anchor=tk.W)
        
        # Store references for updates
        card.price_label = price_label
        card.change_label = change_label
        card.volume_label = volume_label
        card.range_label = range_label
        card.signal_label = signal_label
        card.confidence_label = confidence_label
        
        return card
    
    def setup_strategy_tab(self):
        """Setup the strategy analysis tab"""

        # Create main paned window for strategy tab
        main_paned = ttk.PanedWindow(self.strategy_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel for strategy selection and summary
        left_panel = ttk.Frame(main_paned)
        main_paned.add(left_panel, weight=1)

        # Strategy selection
        selection_frame = ttk.LabelFrame(left_panel, text="Strategy Selection")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(selection_frame, text="Select symbol to analyze:").pack(anchor=tk.W, padx=5, pady=2)

        self.selected_symbol_var = tk.StringVar(value="AAPL")
        symbol_combo = ttk.Combobox(selection_frame, textvariable=self.selected_symbol_var,
                                   values=self.symbols, state="readonly", width=10)
        symbol_combo.pack(anchor=tk.W, padx=20, pady=2)
        symbol_combo.bind('<<ComboboxSelected>>', self.on_symbol_selected)

        # Strategy recommendations summary
        summary_frame = ttk.LabelFrame(left_panel, text="Strategy Recommendations")
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create treeview for strategy recommendations
        self.strategy_tree = ttk.Treeview(summary_frame, columns=('Strategy', 'Confidence', 'Strength'),
                                        show='headings', height=8)

        self.strategy_tree.heading('Strategy', text='Strategy')
        self.strategy_tree.heading('Confidence', text='Confidence')
        self.strategy_tree.heading('Strength', text='Strength')

        self.strategy_tree.column('Strategy', width=120)
        self.strategy_tree.column('Confidence', width=80)
        self.strategy_tree.column('Strength', width=80)

        self.strategy_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.strategy_tree.bind('<<TreeviewSelect>>', self.on_strategy_selected)

        # Right panel for detailed criteria analysis
        right_panel = ttk.Frame(main_paned)
        main_paned.add(right_panel, weight=2)

        # Criteria display widget
        criteria_frame = ttk.LabelFrame(right_panel, text="Strategy Criteria Analysis")
        criteria_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.criteria_widget = StrategyCriteriaWidget(criteria_frame)
        self.criteria_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Analysis controls
        controls_frame = ttk.Frame(right_panel)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(controls_frame, text="Refresh Analysis",
                  command=self.refresh_strategy_analysis).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="Export Criteria",
                  command=self.export_criteria_analysis).pack(side=tk.LEFT, padx=5)
    
    def setup_recommendations_tab(self):
        """Setup the daily recommendations tab"""
        # Action items section
        actions_frame = ttk.LabelFrame(self.recommendations_frame, text="Priority Action Items")
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Create treeview for action items
        columns = ("Priority", "Symbol", "Action", "Reasoning")
        self.actions_tree = ttk.Treeview(actions_frame, columns=columns, show="headings", height=6)
        
        for col in columns:
            self.actions_tree.heading(col, text=col)
            self.actions_tree.column(col, width=150)
        
        actions_scrollbar = ttk.Scrollbar(actions_frame, orient=tk.VERTICAL, command=self.actions_tree.yview)
        self.actions_tree.configure(yscrollcommand=actions_scrollbar.set)
        
        self.actions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        actions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Full report section
        report_frame = ttk.LabelFrame(self.recommendations_frame, text="Complete Daily Report")
        report_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.daily_report = scrolledtext.ScrolledText(report_frame, font=('Courier', 9))
        self.daily_report.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def setup_risk_tab(self):
        """Setup the risk management tab"""
        # Risk overview
        overview_frame = ttk.LabelFrame(self.risk_frame, text="Portfolio Risk Overview")
        overview_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.risk_overview = ttk.Label(overview_frame, text="No risk data available", font=('Arial', 10))
        self.risk_overview.pack(padx=10, pady=10)
        
        # Risk parameters
        params_frame = ttk.LabelFrame(self.risk_frame, text="Risk Parameters")
        params_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Max risk per trade
        ttk.Label(params_frame, text="Max Risk Per Trade (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_risk_var = tk.DoubleVar(value=2.0)
        ttk.Scale(params_frame, from_=0.5, to=5.0, variable=self.max_risk_var, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        self.max_risk_label = ttk.Label(params_frame, text="2.0%")
        self.max_risk_label.grid(row=0, column=2, padx=5, pady=2)
        
        # Max portfolio risk
        ttk.Label(params_frame, text="Max Portfolio Risk (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.portfolio_risk_var = tk.DoubleVar(value=20.0)
        ttk.Scale(params_frame, from_=5.0, to=50.0, variable=self.portfolio_risk_var, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        self.portfolio_risk_label = ttk.Label(params_frame, text="20.0%")
        self.portfolio_risk_label.grid(row=1, column=2, padx=5, pady=2)
        
        params_frame.columnconfigure(1, weight=1)
        
        # Bind scale updates
        self.max_risk_var.trace('w', self.update_risk_labels)
        self.portfolio_risk_var.trace('w', self.update_risk_labels)

    def setup_ai_assistant_tab(self):
        """Setup the AI Assistant tab"""
        # Create a placeholder initially
        placeholder_frame = ttk.LabelFrame(self.ai_assistant_frame, text="🤖 AI Trading Assistant")
        placeholder_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configuration section
        config_frame = ttk.LabelFrame(placeholder_frame, text="Configuration")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(config_frame, text="OpenAI API Key:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.openai_key_var = tk.StringVar()
        openai_entry = ttk.Entry(config_frame, textvariable=self.openai_key_var, show="*", width=40)
        openai_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)

        ttk.Button(config_frame, text="Initialize AI Assistant",
                  command=self.initialize_ai_assistant).grid(row=0, column=2, padx=5, pady=2)

        config_frame.columnconfigure(1, weight=1)

        # Status section
        self.ai_status_frame = ttk.LabelFrame(placeholder_frame, text="Status")
        self.ai_status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.ai_status_label = ttk.Label(self.ai_status_frame,
                                        text="Enter your OpenAI API key above to activate the AI Assistant")
        self.ai_status_label.pack(padx=10, pady=10)

        # Instructions
        instructions_frame = ttk.LabelFrame(placeholder_frame, text="AI Assistant Features")
        instructions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        instructions_text = scrolledtext.ScrolledText(instructions_frame, height=15, font=('Arial', 10))
        instructions_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        instructions_content = """🎯 AI Trading Assistant - Powered by ChatGPT-4

CAPABILITIES:
• Analyze ALL data displayed in the desktop application interface
• Access real-time Financial Modeling Prep (FMP) API data directly
• Answer natural language questions about current market conditions
• Explain strategy recommendations and why they were chosen
• Provide risk management and position sizing guidance
• Suggest alternative strategies when market conditions change

EXAMPLE QUESTIONS:
• "What's the best strategy for AAPL today and why?"
• "Explain the current market environment and how it affects my trades"
• "What are the key risks I should watch for this week?"
• "How should I adjust my portfolio based on current volatility?"
• "Why was a covered call recommended for NVDA?"
• "What's Erica's methodology for this market condition?"

REAL-TIME DATA ACCESS:
• Current stock prices and market data for all 5 symbols
• Live strategy recommendations with confidence scores
• Market factors (VIX, sentiment, volatility regime)
• Earnings calendar and news sentiment analysis
• Technical indicators and support/resistance levels
• Risk metrics and portfolio analysis

ERICA'S METHODOLOGY INTEGRATION:
• Covered Calls: 30-45 DTE, 0.30 delta, $30+ premium targets
• Credit Spreads: Bullish markets, 0.15-0.20 delta, high probability
• LEAPS: 12+ months, 0.70-0.80 delta, growth stocks
• Premium Selling: High IV rank periods, systematic approach

The AI assistant maintains conversation context and provides specific,
actionable advice based on current market conditions and Erica's proven strategies.

To get started, enter your OpenAI API key above and click "Initialize AI Assistant".
You can get an API key from: https://platform.openai.com/api-keys"""

        instructions_text.insert(tk.END, instructions_content)
        instructions_text.config(state=tk.DISABLED)

    def initialize_ai_assistant(self):
        """Initialize the AI Assistant with the provided API key"""
        openai_key = self.openai_key_var.get().strip()

        if not openai_key:
            messagebox.showerror("AI Assistant", "Please enter your OpenAI API key first")
            return

        try:
            # Update status
            self.ai_status_label.config(text="Initializing AI Assistant...")
            self.root.update()

            # Create AI Assistant configuration
            ai_config = AIAssistantConfig(
                openai_api_key=openai_key,
                model="gpt-4",  # Latest available model
                max_tokens=1500,
                temperature=0.7
            )

            # Create data access layer
            fmp_key = self.api_key or resolve_fmp_key(None)
            if not fmp_key:
                messagebox.showerror("AI Assistant", "FMP API key is required. Please configure it in Settings first.")
                return

            data_access = DataAccessLayer(fmp_key, self)

            # Create conversation manager
            self.ai_conversation_manager = AIConversationManager(ai_config, data_access)

            # Clear the placeholder and create the actual AI interface
            for widget in self.ai_assistant_frame.winfo_children():
                widget.destroy()

            # Create the AI Assistant GUI
            self.ai_assistant = AIAssistantGUI(self.ai_assistant_frame, self.ai_conversation_manager)

            # Store the API key
            self.openai_api_key = openai_key

            messagebox.showinfo("AI Assistant", "AI Assistant initialized successfully! You can now chat with Erica's AI.")

        except Exception as e:
            self.ai_status_label.config(text=f"Error: {str(e)}")
            messagebox.showerror("AI Assistant Error", f"Failed to initialize AI Assistant:\n\n{str(e)}")

    def setup_settings_tab(self):
        """Setup the settings tab"""
        # API Configuration
        api_frame = ttk.LabelFrame(self.settings_frame, text="API Configuration")
        api_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(api_frame, text="FMP API Key:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=40)
        api_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        ttk.Button(api_frame, text="Test", command=self.test_api).grid(row=0, column=2, padx=5, pady=2)

        # OpenAI API Key
        ttk.Label(api_frame, text="OpenAI API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.openai_api_key_var = tk.StringVar()
        openai_entry = ttk.Entry(api_frame, textvariable=self.openai_api_key_var, show="*", width=40)
        openai_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        ttk.Button(api_frame, text="Test AI", command=self.test_openai_api).grid(row=1, column=2, padx=5, pady=2)

        api_frame.columnconfigure(1, weight=1)
        
        # Account Configuration
        account_frame = ttk.LabelFrame(self.settings_frame, text="Account Configuration")
        account_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(account_frame, text="Account Size ($):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.account_size_var = tk.IntVar(value=self.account_size)
        ttk.Entry(account_frame, textvariable=self.account_size_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Symbols Configuration
        symbols_frame = ttk.LabelFrame(self.settings_frame, text="Symbols to Analyze")
        symbols_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(symbols_frame, text="Symbols (comma-separated):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.symbols_var = tk.StringVar(value=",".join(self.symbols))
        ttk.Entry(symbols_frame, textvariable=self.symbols_var, width=40).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        symbols_frame.columnconfigure(1, weight=1)
        
        # Auto-refresh settings
        refresh_frame = ttk.LabelFrame(self.settings_frame, text="Auto-Refresh Settings")
        refresh_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(refresh_frame, text="Enable Auto-Refresh", variable=self.auto_refresh_var).pack(anchor=tk.W, padx=5, pady=2)
        
        ttk.Label(refresh_frame, text="Refresh Interval (minutes):").pack(anchor=tk.W, padx=5, pady=2)
        self.refresh_interval_var = tk.IntVar(value=self.refresh_interval // 60)
        ttk.Scale(refresh_frame, from_=1, to=60, variable=self.refresh_interval_var, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=5, pady=2)
        
        # Save button
        ttk.Button(self.settings_frame, text="Save Settings", command=self.save_settings).pack(pady=10)
    
    def show_welcome_screen(self):
        """Show welcome screen with setup instructions"""
        welcome_text = """
Welcome to the AI-Powered Daily Stock Investment Planning System!

This application implements Erica's proven trading strategies from @AbundantlyErica.

To get started:
1. Go to the Settings tab and enter your FMP API key
2. Configure your account size and risk parameters
3. Click 'Analyze' to generate your daily investment recommendations

Features:
• Real-time market data for AAPL, NVDA, GOOGL, AMZN, AMD
• Erica's trading strategies: Covered Calls, Credit Spreads, LEAPS, Premium Selling
• Comprehensive risk management and position sizing
• Daily actionable recommendations with confidence scores
• Automatic data refresh and monitoring

Need help? Check the Help menu for guides and documentation.
        """
        
        self.daily_report.delete(1.0, tk.END)
        self.daily_report.insert(tk.END, welcome_text)
    
    def setup_best_strategy_tab(self):
        """Setup the best strategy tab with comprehensive analysis"""

        # Create the best strategy dashboard
        api_key = self.api_key or "demo_key"  # Use demo key if none provided

        try:
            self.best_strategy_dashboard = create_best_strategy_dashboard(
                self.best_strategy_frame,
                api_key,
                self.symbols
            )
            self.best_strategy_dashboard.pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            # Fallback to simple message if dashboard creation fails
            error_frame = ttk.LabelFrame(self.best_strategy_frame, text="Best Strategy Analysis")
            error_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            error_text = scrolledtext.ScrolledText(error_frame, height=20, font=('Arial', 10))
            error_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            error_message = f"""Best Strategy Dashboard - August 18, 2025

The comprehensive best strategy dashboard provides detailed analysis for each stock:

🎯 PRIMARY STRATEGY RECOMMENDATIONS
• Highest-confidence strategy for each stock (AAPL, NVDA, AMD, GOOGL, AMZN)
• Confidence percentages and recommendation strength
• Specific execution details (strikes, expirations, position sizing)

📊 STRATEGY JUSTIFICATION
• Top 3-5 reasons for strategy selection
• Visual criteria alignment with Erica's decision framework
• Numerical scores for market environment and stock factors
• Risk factors and mitigation strategies

💰 FINANCIAL ANALYST INTEGRATION
• AI-powered analyst-style reviews using OpenAI API
• Fundamental analysis (P/E, growth rates, financial health)
• Technical outlook with support/resistance levels
• Analyst price targets and consensus ratings
• Confidence intervals for price predictions

📰 NEWS IMPACT ANALYSIS
• AI sentiment analysis of latest 5-10 news articles per stock
• How current news supports or challenges recommended strategies
• News-driven catalysts aligned with strategy timing
• Earnings, product launches, and regulatory change impacts

🚀 SUPPORTING CATALYSTS
• Upcoming events benefiting chosen strategies
• Sector rotation trends and technical breakout levels
• Volatility events and seasonal patterns
• Real-time validation and alternative suggestions

⚡ REAL-TIME UPDATES
• Automatic updates when market conditions change
• Alerts when strategy criteria shift
• Alternative strategy suggestions when primary becomes invalid

Error initializing dashboard: {str(e)}

To use the full dashboard, ensure API key is configured in Settings.
Run analysis to populate strategy recommendations.
"""

            error_text.insert(tk.END, error_message)
            error_text.config(state='disabled')

    def update_risk_labels(self, *args):
        """Update risk parameter labels"""
        self.max_risk_label.config(text=f"{self.max_risk_var.get():.1f}%")
        self.portfolio_risk_label.config(text=f"{self.portfolio_risk_var.get():.1f}%")

    def update_api_status(self):
        """Update API status indicator"""
        if self.api_key:
            self.api_status.config(text="Ready", style='Success.TLabel')
            if hasattr(self, 'api_key_var'):
                self.api_key_var.set(self.api_key)
        else:
            self.api_status.config(text="Not Set", style='Error.TLabel')
    
    # Event handlers and functionality methods

    def new_analysis(self):
        """Start a new analysis"""
        self.run_analysis()

    def export_report(self):
        """Export the current report to a file"""
        if not hasattr(self, 'last_report') or not self.last_report:
            messagebox.showwarning("Export", "No report to export. Run an analysis first.")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Daily Report"
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.last_report)
                messagebox.showinfo("Export", f"Report exported to {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export report: {e}")

    def save_settings(self):
        """Save current settings to file"""
        settings = {
            'api_key': self.api_key_var.get() if hasattr(self, 'api_key_var') else '',
            'openai_api_key': self.openai_api_key_var.get() if hasattr(self, 'openai_api_key_var') else '',
            'account_size': self.account_size_var.get() if hasattr(self, 'account_size_var') else self.account_size,
            'symbols': self.symbols_var.get().split(',') if hasattr(self, 'symbols_var') else self.symbols,
            'auto_refresh': self.auto_refresh_var.get() if hasattr(self, 'auto_refresh_var') else self.auto_refresh,
            'refresh_interval': self.refresh_interval_var.get() * 60 if hasattr(self, 'refresh_interval_var') else self.refresh_interval,
            'max_risk': self.max_risk_var.get() if hasattr(self, 'max_risk_var') else 2.0,
            'portfolio_risk': self.portfolio_risk_var.get() if hasattr(self, 'portfolio_risk_var') else 20.0
        }

        try:
            with open('settings.json', 'w') as f:
                json.dump(settings, f, indent=2)
            self.update_status("Settings saved successfully")
            messagebox.showinfo("Settings", "Settings saved successfully!")
        except Exception as e:
            messagebox.showerror("Settings Error", f"Failed to save settings: {e}")

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r') as f:
                    settings = json.load(f)

                self.api_key = settings.get('api_key', '')
                self.openai_api_key = settings.get('openai_api_key', '')
                self.account_size = settings.get('account_size', 100000)
                self.symbols = settings.get('symbols', SYMBOLS_DEFAULT.copy())
                self.auto_refresh = settings.get('auto_refresh', False)
                self.refresh_interval = settings.get('refresh_interval', 300)

                # Update UI if variables exist
                if hasattr(self, 'api_key_var'):
                    self.api_key_var.set(self.api_key)
                if hasattr(self, 'openai_api_key_var'):
                    self.openai_api_key_var.set(self.openai_api_key)
                if hasattr(self, 'account_size_var'):
                    self.account_size_var.set(self.account_size)
                if hasattr(self, 'symbols_var'):
                    self.symbols_var.set(','.join(self.symbols))

                self.update_status("Settings loaded")
        except Exception as e:
            self.update_status(f"Failed to load settings: {e}")

    def show_settings(self):
        """Show the settings tab"""
        self.notebook.select(self.settings_frame)

    def test_api(self):
        """Test the API connection"""
        api_key = self.api_key_var.get() if hasattr(self, 'api_key_var') else self.api_key

        if not api_key:
            messagebox.showerror("API Test", "Please enter an API key first")
            return

        self.update_status("Testing API connection...")
        self.progress.start()

        def test_thread():
            try:
                from daily_outline import fmp_quote
                result = fmp_quote("AAPL", api_key)

                self.root.after(0, lambda: self.api_test_complete(result is not None))
            except Exception as e:
                self.root.after(0, lambda: self.api_test_complete(False, str(e)))

        threading.Thread(target=test_thread, daemon=True).start()

    def api_test_complete(self, success, error=None):
        """Handle API test completion"""
        self.progress.stop()

        if success:
            self.api_status.config(text="Connected", style='Success.TLabel')
            self.update_status("API connection successful")
            messagebox.showinfo("API Test", "API connection successful!")
        else:
            self.api_status.config(text="Failed", style='Error.TLabel')
            error_msg = f"API connection failed: {error}" if error else "API connection failed"
            self.update_status(error_msg)
            messagebox.showerror("API Test", error_msg)

    def test_openai_api(self):
        """Test the OpenAI API connection"""
        openai_key = self.openai_api_key_var.get() if hasattr(self, 'openai_api_key_var') else ""

        if not openai_key:
            messagebox.showerror("OpenAI API Test", "Please enter an OpenAI API key first")
            return

        self.update_status("Testing OpenAI API connection...")
        self.progress.start()

        def test_openai_thread():
            try:
                from openai import OpenAI
                client = OpenAI(api_key=openai_key)

                # Simple test request
                response = client.chat.completions.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": "Hello, this is a test."}],
                    max_tokens=10
                )

                success = response.choices[0].message.content is not None
                self.root.after(0, lambda: self.openai_test_complete(success))

            except Exception as e:
                self.root.after(0, lambda: self.openai_test_complete(False, str(e)))

        threading.Thread(target=test_openai_thread, daemon=True).start()

    def openai_test_complete(self, success, error=None):
        """Handle OpenAI API test completion"""
        self.progress.stop()

        if success:
            self.update_status("OpenAI API connection successful")
            messagebox.showinfo("OpenAI API Test", "OpenAI API connection successful!")
        else:
            error_msg = f"OpenAI API connection failed: {error}" if error else "OpenAI API connection failed"
            self.update_status(error_msg)
            messagebox.showerror("OpenAI API Test", error_msg)

    def run_tests(self):
        """Run system tests"""
        self.update_status("Running system tests...")
        self.progress.start()

        def test_thread():
            try:
                import subprocess
                result = subprocess.run(['python', 'test_suite.py'],
                                      capture_output=True, text=True, cwd='.')

                self.root.after(0, lambda: self.tests_complete(result.returncode == 0, result.stdout))
            except Exception as e:
                self.root.after(0, lambda: self.tests_complete(False, str(e)))

        threading.Thread(target=test_thread, daemon=True).start()

    def tests_complete(self, success, output):
        """Handle test completion"""
        self.progress.stop()

        if success:
            self.update_status("All tests passed")
            messagebox.showinfo("System Tests", "All tests passed successfully!")
        else:
            self.update_status("Some tests failed")
            messagebox.showwarning("System Tests", f"Some tests failed:\n\n{output}")

    def refresh_data(self):
        """Refresh market data"""
        self.run_analysis()

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        self.auto_refresh = not self.auto_refresh
        if self.auto_refresh:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """Start auto-refresh timer with performance optimization"""
        if not self.auto_refresh:
            return

        # Check if enough time has passed since last refresh (prevent spam)
        import time
        current_time = time.time()
        if hasattr(self, 'last_refresh_time') and (current_time - self.last_refresh_time) < 30:
            # Schedule next check in 10 seconds
            self.root.after(10000, self.start_auto_refresh)
            return

        try:
            # Initialize real-time data manager if not already done
            if not self.data_manager and self.api_key:
                self.initialize_data_manager()

            # Use real-time data manager if available, otherwise fallback to timer
            if self.data_manager:
                self.data_manager.start()
            else:
                # Run refresh in background to prevent GUI freezing
                self.root.after_idle(self._background_refresh)

            self.last_refresh_time = current_time

        except Exception as e:
            print(f"Auto-refresh error: {e}")

        # Schedule next refresh
        if self.auto_refresh:
            self.root.after(max(self.refresh_interval * 1000, 30000), self.start_auto_refresh)

    def _background_refresh(self):
        """Perform refresh in background to prevent GUI freezing"""
        try:
            # Only refresh if not already in progress
            if not hasattr(self, '_refresh_in_progress') or not self._refresh_in_progress:
                self._refresh_in_progress = True
                self.refresh_data()
                self._refresh_in_progress = False
        except Exception as e:
            print(f"Background refresh error: {e}")
            self._refresh_in_progress = False

    def stop_auto_refresh(self):
        """Stop auto-refresh timer"""
        if self.data_manager:
            self.data_manager.stop()
        # Timer will stop on next cycle due to auto_refresh flag

    def initialize_data_manager(self):
        """Initialize the real-time data manager"""
        if not self.api_key:
            return

        try:
            self.data_manager = RealTimeDataManager(self.api_key, self.symbols)

            # Add callbacks for data updates
            self.data_manager.add_callback("stock_quotes", self.on_quotes_update)
            self.data_manager.add_callback("market_indicators", self.on_market_indicators_update)
            self.data_manager.add_callback("strategy_recommendations", self.on_strategy_update)

            self.update_status("Real-time data manager initialized")

        except Exception as e:
            self.logger.error(f"Error initializing data manager: {e}")
            self.update_status(f"Data manager error: {e}")

    def on_quotes_update(self, update: DataUpdate):
        """Handle real-time quote updates"""
        if update.success and update.data:
            # Update stock cards with new prices
            self.root.after(0, lambda: self.update_stock_cards_from_quotes(update.data))

    def on_market_indicators_update(self, update: DataUpdate):
        """Handle market indicators updates"""
        if update.success and update.data:
            # Update market overview
            self.root.after(0, lambda: self.update_market_overview_from_indicators(update.data))

    def on_strategy_update(self, update: DataUpdate):
        """Handle strategy recommendation updates"""
        if update.success and update.data:
            # Update strategy displays
            self.root.after(0, lambda: self.update_strategy_displays_from_data(update.data))

    def update_stock_cards_from_quotes(self, quotes_data: dict):
        """Update stock cards with real-time quote data"""
        for symbol, quote in quotes_data.items():
            if symbol in self.stock_cards:
                card = self.stock_cards[symbol]

                # Update price
                price = quote.get('price', 0)
                change_pct = quote.get('changesPercentage', 0)
                card.price_label.config(text=f"${price:.2f}")

                # Update change with color
                change_text = f"({change_pct:+.2f}%)"
                change_color = 'green' if change_pct >= 0 else 'red'
                card.change_label.config(text=change_text, foreground=change_color)

                # Update volume
                volume = quote.get('volume', 0)
                card.volume_label.config(text=f"Volume: {volume:,}")

    def update_market_overview_from_indicators(self, indicators):
        """Update market overview with real-time indicators"""
        overview_text = f"VIX: {indicators.vix_level:.1f} ({indicators.vix_percentile:.0f}th percentile)\n"
        overview_text += f"Put/Call Ratio: {indicators.put_call_ratio:.2f}\n"
        overview_text += f"Fear & Greed: {indicators.fear_greed_index:.0f}\n"
        overview_text += f"Market Breadth: {indicators.market_breadth:.2f}"
        self.market_overview.config(text=overview_text)

    def update_strategy_displays_from_data(self, strategy_data):
        """Update strategy displays with new recommendations"""
        if 'strategy_recommendations' in strategy_data:
            recommendations = strategy_data['strategy_recommendations']

            # Update strategy tree
            for item in self.strategy_tree.get_children():
                self.strategy_tree.delete(item)

            for symbol, rec in recommendations.items():
                strategy_name = rec.recommended_strategy.value.replace('_', ' ').title()
                confidence_pct = f"{rec.confidence:.0%}"

                if rec.confidence >= 0.8:
                    strength = "STRONG"
                elif rec.confidence >= 0.6:
                    strength = "MODERATE"
                elif rec.confidence >= 0.4:
                    strength = "WEAK"
                else:
                    strength = "AVOID"

                self.strategy_tree.insert('', 'end', values=(
                    f"{symbol}: {strategy_name}",
                    confidence_pct,
                    strength
                ))

    def show_help(self):
        """Show help documentation"""
        help_window = tk.Toplevel(self.root)
        help_window.title("User Guide")
        help_window.geometry("800x600")

        help_text = scrolledtext.ScrolledText(help_window, font=('Arial', 10))
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = """
AI-POWERED DAILY STOCK INVESTMENT PLANNING SYSTEM - USER GUIDE

GETTING STARTED:
1. Enter your FMP API key in Settings tab
2. Configure your account size and risk parameters
3. Click 'Analyze' to generate recommendations

DASHBOARD TAB:
- View real-time market data for all symbols
- See current prices, changes, and key metrics
- Monitor strategy signals and confidence levels

STRATEGY ANALYSIS TAB:
- Select which strategies to analyze
- View detailed strategy recommendations
- Understand the reasoning behind each signal

DAILY RECOMMENDATIONS TAB:
- Priority action items ranked by confidence
- Complete daily investment report
- Market outlook and key insights

RISK MANAGEMENT TAB:
- Monitor portfolio risk levels
- Adjust risk parameters
- View position sizing recommendations

SETTINGS TAB:
- Configure API key and account settings
- Set auto-refresh preferences
- Customize symbols to analyze

ERICA'S STRATEGIES:
• Covered Calls: Generate income from stock holdings
• Credit Spreads: High-probability income strategies
• LEAPS: Long-term leveraged growth plays
• Premium Selling: Systematic volatility harvesting

For more information, visit Erica's YouTube channel: @AbundantlyErica
        """

        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)

    def show_strategy_guide(self):
        """Show strategy guide"""
        strategy_window = tk.Toplevel(self.root)
        strategy_window.title("Strategy Guide")
        strategy_window.geometry("800x600")

        strategy_text = scrolledtext.ScrolledText(strategy_window, font=('Arial', 10))
        strategy_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        strategy_content = """
ERICA'S TRADING STRATEGIES - DETAILED GUIDE

1. COVERED CALLS
Purpose: Generate monthly income from stock holdings
Rules:
- Own 100 shares of the underlying stock
- Sell calls 30-45 days to expiration (DTE)
- Target 0.30 delta (30% probability of assignment)
- Collect minimum $30 premium per contract
- Close at 50% profit or 21 DTE

When to Use: Neutral to slightly bullish market, own the stock

2. CREDIT SPREADS (PUT CREDIT SPREADS)
Purpose: Profit from time decay and limited price movement
Rules:
- Use in bullish or neutral markets
- Sell put at 0.15-0.20 delta (15-20% probability of assignment)
- Buy put 5-10 strikes lower for protection
- Target 30-45 DTE
- Close at 50% profit or 21 DTE

When to Use: Bullish outlook, high volatility environment

3. LEAPS (Long-term Equity Anticipation Securities)
Purpose: Leveraged exposure to long-term growth
Rules:
- Buy calls with 12+ months to expiration
- Target 0.70-0.80 delta (deep in the money)
- Use as stock replacement with leverage
- Focus on strong growth stocks
- Hold for 6-12 months typically

When to Use: Strong bullish outlook, low volatility environment

4. PREMIUM SELLING
Purpose: Systematic income generation from volatility
Rules:
- Focus on high IV rank periods (volatility > 70th percentile)
- Sell options with 15-30 delta
- Target 30-45 DTE
- Manage at 50% profit or 21 DTE
- Use cash-secured puts or covered calls

When to Use: High volatility periods, range-bound markets

RISK MANAGEMENT PRINCIPLES:
- Never risk more than 2% of account on single trade
- Diversify across strategies and timeframes
- Use systematic profit-taking rules (50% of max profit)
- Adjust position sizes based on volatility
- Maintain cash reserves for opportunities

MARKET TIMING:
- High Volatility: Focus on premium selling strategies
- Low Volatility: Consider directional strategies (LEAPS)
- Bull Market: LEAPS and credit spreads
- Bear Market: Covered calls and defensive strategies
- Neutral Market: Premium selling and income strategies
        """

        strategy_text.insert(tk.END, strategy_content)
        strategy_text.config(state=tk.DISABLED)

    def show_about(self):
        """Show about dialog"""
        about_text = """
AI-Powered Daily Stock Investment Planning System
Enhanced with Erica's Trading Strategies

Version 1.0

This application implements proven trading strategies from
@AbundantlyErica YouTube channel for systematic options trading.

Features:
• Real-time market analysis
• Erica's core trading strategies
• Risk management and position sizing
• Daily actionable recommendations

Built with Python and tkinter
Data provided by Financial Modeling Prep API

For educational purposes only.
Trading involves risk of loss.
        """

        messagebox.showinfo("About", about_text)

    def update_status(self, message):
        """Update the status bar"""
        self.status_text.config(text=message)
        self.root.update_idletasks()

    def run_analysis(self):
        """Run the complete trading analysis"""
        # Get current settings
        api_key = self.api_key_var.get() if hasattr(self, 'api_key_var') else self.api_key
        account_size = self.account_size_var.get() if hasattr(self, 'account_size_var') else self.account_size
        symbols = [s.strip().upper() for s in (self.symbols_var.get().split(',') if hasattr(self, 'symbols_var') else self.symbols)]

        if not api_key:
            messagebox.showerror("Analysis Error", "Please configure your API key in Settings first")
            self.show_settings()
            return

        self.update_status("Running analysis...")
        self.progress.start()

        def analysis_thread():
            try:
                # Import and run analysis
                from daily_outline import fmp_quote, fmp_historical_daily

                # Initialize enhanced analysis engines
                if not self.intelligent_engine:
                    self.intelligent_engine = IntelligentStrategyEngine(api_key)
                    self.market_analyzer = MarketAnalysisEngine(api_key)

                # Fetch market data
                market_data = {}
                historical_data = {}

                for symbol in symbols:
                    quote = fmp_quote(symbol, api_key)
                    if quote:
                        market_data[symbol] = {"quote": quote}

                    hist = fmp_historical_daily(symbol, api_key, limit=60)
                    if hist:
                        historical_data[symbol] = hist

                # Generate enhanced recommendations with error handling
                try:
                    market_report, strategy_recommendations = self.intelligent_engine.generate_daily_recommendations(symbols)
                except Exception as e:
                    print(f"Enhanced recommendations failed: {e}")
                    # Use fallback data
                    market_report = None
                    strategy_recommendations = []

                # Store strategy recommendations for criteria analysis
                self.current_strategy_recommendations = {rec.symbol: rec for rec in strategy_recommendations}

                # Generate traditional recommendations for compatibility
                generator = DailyRecommendationGenerator(account_size, symbols)
                daily_report = generator.generate_daily_report(market_data, historical_data)
                formatted_report = generator.format_daily_report(daily_report)

                # Update UI on main thread
                self.root.after(0, lambda: self.analysis_complete(daily_report, formatted_report, market_data, market_report, strategy_recommendations))

            except Exception as e:
                self.root.after(0, lambda: self.analysis_error(str(e)))

        threading.Thread(target=analysis_thread, daemon=True).start()

    def analysis_complete(self, daily_report, formatted_report, market_data, market_report=None, strategy_recommendations=None):
        """Handle analysis completion"""
        self.progress.stop()
        self.last_report = formatted_report
        self.current_data = market_data
        self.last_update = datetime.now()

        # Update dashboard
        self.update_dashboard(daily_report, market_data)

        # Update recommendations
        self.update_recommendations(daily_report, formatted_report)

        # Update enhanced strategy analysis if available
        if strategy_recommendations:
            self.update_strategy_analysis(strategy_recommendations, market_report)

            # Update best strategy dashboard if available
            if hasattr(self, 'best_strategy_dashboard'):
                try:
                    self.best_strategy_dashboard.refresh_all_strategies()
                except Exception as e:
                    print(f"Error updating best strategy dashboard: {e}")

        # Update status
        self.update_status(f"Analysis complete - {self.last_update.strftime('%H:%M:%S')}")
        self.update_label.config(text=self.last_update.strftime('%H:%M:%S'))
        self.api_status.config(text="Connected", style='Success.TLabel')

        # Update account label
        self.account_label.config(text=f"${daily_report.portfolio_risk.total_risk:,.0f} at risk")

    def analysis_error(self, error_message):
        """Handle analysis error"""
        self.progress.stop()
        self.update_status(f"Analysis failed: {error_message}")
        messagebox.showerror("Analysis Error", f"Analysis failed:\n\n{error_message}")

    def update_dashboard(self, daily_report, market_data):
        """Update the dashboard with new data"""
        # Update market overview
        overview_text = f"Market Environment: {daily_report.market_environment.market_outlook}\n"
        overview_text += f"Volatility Regime: {daily_report.market_environment.volatility_regime}\n"
        overview_text += f"Risk Sentiment: {daily_report.market_environment.risk_sentiment}"
        self.market_overview.config(text=overview_text)

        # Update stock cards
        for rec in daily_report.stock_recommendations:
            if rec.symbol in self.stock_cards:
                card = self.stock_cards[rec.symbol]
                quote = market_data.get(rec.symbol, {}).get('quote', {})

                # Update price
                price = quote.get('price', 0)
                change_pct = quote.get('changesPercentage', 0)
                card.price_label.config(text=f"${price:.2f}")

                # Update change with color
                change_text = f"({change_pct:+.2f}%)"
                change_color = 'green' if change_pct >= 0 else 'red'
                card.change_label.config(text=change_text, foreground=change_color)

                # Update volume
                volume = quote.get('volume', 0)
                card.volume_label.config(text=f"Volume: {volume:,}")

                # Update range
                year_low = quote.get('yearLow', 0)
                year_high = quote.get('yearHigh', 0)
                card.range_label.config(text=f"52W Range: ${year_low:.2f} - ${year_high:.2f}")

                # Update signal
                if rec.primary_signal:
                    signal_text = f"Signal: {rec.primary_signal.action} {rec.primary_signal.strategy.value}"
                    confidence_text = f"Confidence: {rec.confidence_score:.0%}"
                else:
                    signal_text = "Signal: HOLD"
                    confidence_text = f"Confidence: {rec.confidence_score:.0%}"

                card.signal_label.config(text=signal_text)
                card.confidence_label.config(text=confidence_text)

    def update_recommendations(self, daily_report, formatted_report):
        """Update the recommendations tab"""
        # Clear existing action items
        for item in self.actions_tree.get_children():
            self.actions_tree.delete(item)

        # Add new action items
        if hasattr(daily_report, 'action_items') and daily_report.action_items:
            for item in daily_report.action_items[:10]:  # Top 10 items
                self.actions_tree.insert('', 'end', values=(
                    item.priority,
                    item.symbol,
                    item.action,
                    item.reasoning[:50] + "..." if len(item.reasoning) > 50 else item.reasoning
                ))
        else:
            # Add placeholder message
            self.actions_tree.insert('', 'end', values=(
                "INFO",
                "SYSTEM",
                "No action items available",
                "Run analysis to generate recommendations"
            ))

        # Update full report
        self.daily_report.delete(1.0, tk.END)
        self.daily_report.insert(tk.END, formatted_report)

    def update_strategy_analysis(self, strategy_recommendations, market_report):
        """Update the strategy analysis tab with enhanced recommendations"""

        # Clear existing strategy tree
        for item in self.strategy_tree.get_children():
            self.strategy_tree.delete(item)

        # Populate strategy tree with recommendations
        for rec in strategy_recommendations:
            strategy_name = rec.recommended_strategy.value.replace('_', ' ').title()
            confidence_pct = f"{rec.confidence:.0%}"

            # Determine strength based on confidence
            if rec.confidence >= 0.8:
                strength = "STRONG"
            elif rec.confidence >= 0.6:
                strength = "MODERATE"
            elif rec.confidence >= 0.4:
                strength = "WEAK"
            else:
                strength = "AVOID"

            self.strategy_tree.insert('', 'end', values=(
                f"{rec.symbol}: {strategy_name}",
                confidence_pct,
                strength
            ))

        # Auto-select first item if available
        if strategy_recommendations:
            first_item = self.strategy_tree.get_children()[0]
            self.strategy_tree.selection_set(first_item)
            self.on_strategy_selected(None)

    def on_symbol_selected(self, event):
        """Handle symbol selection change"""
        selected_symbol = self.selected_symbol_var.get()

        # Filter strategy tree to show only selected symbol
        for item in self.strategy_tree.get_children():
            values = self.strategy_tree.item(item, 'values')
            if values and selected_symbol in values[0]:
                self.strategy_tree.selection_set(item)
                self.on_strategy_selected(None)
                break

    def on_strategy_selected(self, event):
        """Handle strategy selection in the tree"""
        selection = self.strategy_tree.selection()
        if not selection:
            return

        # Get selected strategy info
        item = selection[0]
        values = self.strategy_tree.item(item, 'values')
        if not values:
            return

        # Extract symbol from the strategy description
        strategy_desc = values[0]
        symbol = strategy_desc.split(':')[0].strip()

        # Find the corresponding strategy recommendation
        if symbol in self.current_strategy_recommendations:
            strategy_rec = self.current_strategy_recommendations[symbol]

            # Get market and stock factors for criteria analysis
            try:
                if self.market_analyzer:
                    market_factors = self.market_analyzer.analyze_market_factors()
                    stock_factors = self.market_analyzer.analyze_stock_factors(symbol)

                    # Create a mock strategy recommendation for criteria analysis
                    from strategy_decision_tree import StrategyRecommendation, StrategyConfidence

                    mock_rec = StrategyRecommendation(
                        symbol=symbol,
                        primary_strategy=strategy_rec.recommended_strategy,
                        confidence=strategy_rec.confidence,
                        confidence_level=strategy_rec.confidence_level,
                        market_environment_score=0.8,  # Would come from actual analysis
                        stock_specific_score=0.7,
                        erica_criteria_score=0.9,
                        risk_adjusted_score=0.6,
                        key_supporting_factors=strategy_rec.entry_criteria,
                        key_risk_factors=strategy_rec.key_risks,
                        alternative_strategies=[],
                        recommended_dte=(30, 45),
                        recommended_delta=(0.15, 0.30),
                        position_size_multiplier=1.0,
                        invalidation_triggers=strategy_rec.invalidation_signals,
                        upgrade_triggers=strategy_rec.upgrade_opportunities
                    )

                    # Update criteria display
                    self.criteria_widget.update_criteria_display(mock_rec, market_factors, stock_factors)

            except Exception as e:
                print(f"Error updating criteria display: {e}")

    def refresh_strategy_analysis(self):
        """Refresh the strategy analysis"""
        if hasattr(self, 'run_analysis'):
            self.run_analysis()

    def export_criteria_analysis(self):
        """Export the current criteria analysis"""
        if not self.criteria_widget.current_analysis:
            messagebox.showwarning("Export", "No criteria analysis to export. Run an analysis first.")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Export Criteria Analysis"
        )

        if filename:
            try:
                analysis = self.criteria_widget.current_analysis

                with open(filename, 'w') as f:
                    f.write(f"Strategy Criteria Analysis - {analysis.symbol}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Strategy: {analysis.strategy.value.replace('_', ' ').title()}\n")
                    f.write(f"Overall Score: {analysis.overall_score:.0%}\n")
                    f.write(f"Recommendation Strength: {analysis.recommendation_strength}\n\n")

                    f.write("Summary:\n")
                    f.write(analysis.summary + "\n\n")

                    # Write criteria details
                    categories = [
                        ("Market Criteria", analysis.market_criteria),
                        ("Stock Criteria", analysis.stock_criteria),
                        ("Erica's Criteria", analysis.erica_criteria),
                        ("Risk Criteria", analysis.risk_criteria)
                    ]

                    for category_name, criteria_list in categories:
                        if criteria_list:
                            f.write(f"{category_name}:\n")
                            f.write("-" * 20 + "\n")
                            for criteria in criteria_list:
                                status_symbol = "✓" if criteria.status.value == "met" else "✗" if criteria.status.value == "not_met" else "⚠"
                                f.write(f"{status_symbol} {criteria.name}: {criteria.description}\n")
                                f.write(f"   Score: {criteria.score:.0%} | {criteria.tooltip}\n\n")

                messagebox.showinfo("Export", f"Criteria analysis exported to {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export criteria analysis: {e}")

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = TradingSystemGUI()
    app.run()
