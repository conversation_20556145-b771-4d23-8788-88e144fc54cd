#!/usr/bin/env python3
"""
Diagnostic script to identify and fix issues with the Erica trading system
This script tests each component individually to isolate problems
"""

import sys
import traceback
from datetime import datetime

def test_api_connectivity():
    """Test FMP API connectivity"""
    print("🔍 Testing FMP API connectivity...")
    
    try:
        from daily_outline import resolve_fmp_key, fmp_quote
        
        api_key = resolve_fmp_key(None)
        if not api_key:
            print("❌ No FMP API key found")
            return False
            
        print(f"✅ API key found: {api_key[:10]}...")
        
        # Test a simple quote
        quote = fmp_quote("AAPL", api_key)
        if quote:
            print(f"✅ API connectivity working - AAPL price: ${quote.get('price', 'N/A')}")
            return True
        else:
            print("❌ API call failed - no data returned")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_analysis_engines():
    """Test analysis engine initialization"""
    print("\n🔍 Testing analysis engines...")
    
    try:
        from daily_outline import resolve_fmp_key
        from intelligent_strategy_engine import IntelligentStrategyEngine
        from market_analysis_engine import MarketAnalysisEngine
        
        api_key = resolve_fmp_key(None)
        if not api_key:
            print("❌ No API key for engine testing")
            return False
            
        # Test intelligent engine
        print("   Testing IntelligentStrategyEngine...")
        intelligent_engine = IntelligentStrategyEngine(api_key)
        print("   ✅ IntelligentStrategyEngine created")
        
        # Test market analyzer
        print("   Testing MarketAnalysisEngine...")
        market_analyzer = MarketAnalysisEngine(api_key)
        print("   ✅ MarketAnalysisEngine created")
        
        return True
        
    except Exception as e:
        print(f"❌ Engine test failed: {e}")
        traceback.print_exc()
        return False

def test_data_generation():
    """Test data generation pipeline"""
    print("\n🔍 Testing data generation...")
    
    try:
        from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
        from daily_recommendations import DailyRecommendationGenerator
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL"]  # Test with just one symbol
        
        print("   Fetching market data...")
        market_data = {}
        historical_data = {}
        
        for symbol in symbols:
            quote = fmp_quote(symbol, api_key)
            if quote:
                market_data[symbol] = {"quote": quote}
                print(f"   ✅ Got quote for {symbol}")
            
            hist = fmp_historical_daily(symbol, api_key, limit=10)
            if hist:
                historical_data[symbol] = hist
                print(f"   ✅ Got historical data for {symbol}")
        
        if not market_data:
            print("❌ No market data retrieved")
            return False
            
        # Test recommendation generation
        print("   Testing recommendation generation...")
        generator = DailyRecommendationGenerator(100000, symbols)
        daily_report = generator.generate_daily_report(market_data, historical_data)
        
        if daily_report:
            print("   ✅ Daily report generated successfully")
            print(f"   📊 Report contains {len(daily_report.recommendations)} recommendations")
            return True
        else:
            print("❌ Failed to generate daily report")
            return False
            
    except Exception as e:
        print(f"❌ Data generation test failed: {e}")
        traceback.print_exc()
        return False

def test_enhanced_analysis():
    """Test enhanced analysis pipeline"""
    print("\n🔍 Testing enhanced analysis...")
    
    try:
        from daily_outline import resolve_fmp_key
        from intelligent_strategy_engine import IntelligentStrategyEngine
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL"]  # Test with just one symbol
        
        print("   Creating intelligent engine...")
        intelligent_engine = IntelligentStrategyEngine(api_key)
        
        print("   Generating enhanced recommendations...")
        market_report, strategy_recommendations = intelligent_engine.generate_daily_recommendations(symbols)
        
        if strategy_recommendations:
            print(f"   ✅ Enhanced analysis successful - {len(strategy_recommendations)} strategies")
            for rec in strategy_recommendations:
                print(f"      {rec.symbol}: {rec.recommended_strategy.value} (confidence: {rec.confidence:.0%})")
            return True
        else:
            print("❌ No enhanced recommendations generated")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced analysis test failed: {e}")
        traceback.print_exc()
        return False

def test_gui_components():
    """Test GUI component creation"""
    print("\n🔍 Testing GUI components...")
    
    try:
        import tkinter as tk
        from desktop_app import TradingSystemGUI
        
        print("   Creating test GUI...")
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        app = TradingSystemGUI()
        
        # Check if key components exist
        components = [
            ('notebook', 'Main notebook'),
            ('dashboard_frame', 'Dashboard frame'),
            ('best_strategy_frame', 'Best strategy frame'),
            ('strategy_analysis_frame', 'Strategy analysis frame'),
            ('recommendations_frame', 'Recommendations frame'),
            ('ai_assistant_frame', 'AI assistant frame')
        ]
        
        for attr, name in components:
            if hasattr(app, attr):
                print(f"   ✅ {name} exists")
            else:
                print(f"   ❌ {name} missing")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        traceback.print_exc()
        return False

def run_full_analysis_test():
    """Run a complete analysis test"""
    print("\n🚀 Running full analysis test...")
    
    try:
        from daily_outline import resolve_fmp_key, fmp_quote, fmp_historical_daily
        from daily_recommendations import DailyRecommendationGenerator
        from intelligent_strategy_engine import IntelligentStrategyEngine
        
        api_key = resolve_fmp_key(None)
        symbols = ["AAPL", "NVDA"]  # Test with two symbols
        
        print("   Step 1: Fetching market data...")
        market_data = {}
        historical_data = {}
        
        for symbol in symbols:
            quote = fmp_quote(symbol, api_key)
            if quote:
                market_data[symbol] = {"quote": quote}
            
            hist = fmp_historical_daily(symbol, api_key, limit=60)
            if hist:
                historical_data[symbol] = hist
        
        print(f"   ✅ Market data: {len(market_data)} symbols")
        
        print("   Step 2: Generating traditional recommendations...")
        generator = DailyRecommendationGenerator(100000, symbols)
        daily_report = generator.generate_daily_report(market_data, historical_data)
        formatted_report = generator.format_daily_report(daily_report)
        
        print(f"   ✅ Traditional analysis: {len(daily_report.recommendations)} recommendations")
        
        print("   Step 3: Generating enhanced recommendations...")
        intelligent_engine = IntelligentStrategyEngine(api_key)
        market_report, strategy_recommendations = intelligent_engine.generate_daily_recommendations(symbols)
        
        print(f"   ✅ Enhanced analysis: {len(strategy_recommendations)} strategies")
        
        print("   Step 4: Testing data structures...")
        print(f"      Daily report type: {type(daily_report)}")
        print(f"      Strategy recommendations type: {type(strategy_recommendations)}")
        print(f"      Market report type: {type(market_report)}")
        
        return True, daily_report, formatted_report, market_data, market_report, strategy_recommendations
        
    except Exception as e:
        print(f"❌ Full analysis test failed: {e}")
        traceback.print_exc()
        return False, None, None, None, None, None

def main():
    """Main diagnostic function"""
    print("🔍 ERICA TRADING SYSTEM DIAGNOSTIC")
    print("=" * 50)
    
    tests = [
        ("API Connectivity", test_api_connectivity),
        ("Analysis Engines", test_analysis_engines),
        ("Data Generation", test_data_generation),
        ("Enhanced Analysis", test_enhanced_analysis),
        ("GUI Components", test_gui_components)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Run full analysis test
    success, daily_report, formatted_report, market_data, market_report, strategy_recommendations = run_full_analysis_test()
    results["Full Analysis"] = success
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC RESULTS")
    print("=" * 50)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("The system components are working correctly.")
        print("The issue may be in the GUI update logic.")
    else:
        print("\n⚠️ SOME TESTS FAILED!")
        print("Issues found in the system components.")
    
    return all_passed, daily_report, formatted_report, market_data, market_report, strategy_recommendations

if __name__ == "__main__":
    main()
