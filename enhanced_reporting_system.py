"""
Enhanced Reporting System
Professional-grade 'Strategy of the Day' recommendations with detailed analysis

This module creates comprehensive, professional reports that explain:
- Why each strategy was selected
- Detailed factor analysis and scoring
- Market environment assessment
- Risk factors and mitigation strategies
- Execution guidance with specific parameters
- Monitoring and alert recommendations

Features:
- Multi-format output (console, HTML, PDF, JSON)
- Interactive charts and visualizations
- Factor importance analysis
- Historical performance context
- Real-time monitoring dashboards
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import json
import html

from intelligent_strategy_engine import StrategyOfTheDay, MarketEnvironmentReport
from strategy_decision_tree import StrategyRecommendation, StrategyConfidence
from market_analysis_engine import MarketFactors, StockSpecificFactors
from daily_outline import StrategyType

class ReportFormat(Enum):
    CONSOLE = "console"
    HTML = "html"
    JSON = "json"
    PDF = "pdf"
    MARKDOWN = "markdown"

class ReportStyle(Enum):
    EXECUTIVE = "executive"      # High-level summary
    DETAILED = "detailed"        # Full analysis
    TECHNICAL = "technical"      # Technical details
    EDUCATIONAL = "educational"  # Learning-focused

@dataclass
class ReportConfiguration:
    """Configuration for report generation"""
    format: ReportFormat = ReportFormat.CONSOLE
    style: ReportStyle = ReportStyle.DETAILED
    include_charts: bool = True
    include_factor_analysis: bool = True
    include_historical_context: bool = True
    include_risk_analysis: bool = True
    include_execution_details: bool = True
    color_coding: bool = True
    emoji_indicators: bool = True

class EnhancedReportingSystem:
    """Professional reporting system for strategy recommendations"""
    
    def __init__(self):
        self.report_templates = self._load_report_templates()
        self.color_schemes = self._load_color_schemes()
        
    def generate_comprehensive_report(self, 
                                    market_report: MarketEnvironmentReport,
                                    daily_strategies: List[StrategyOfTheDay],
                                    config: ReportConfiguration = None) -> str:
        """Generate comprehensive daily report with all analysis"""
        
        if config is None:
            config = ReportConfiguration()
        
        if config.format == ReportFormat.CONSOLE:
            return self._generate_console_report(market_report, daily_strategies, config)
        elif config.format == ReportFormat.HTML:
            return self._generate_html_report(market_report, daily_strategies, config)
        elif config.format == ReportFormat.JSON:
            return self._generate_json_report(market_report, daily_strategies, config)
        elif config.format == ReportFormat.MARKDOWN:
            return self._generate_markdown_report(market_report, daily_strategies, config)
        else:
            return self._generate_console_report(market_report, daily_strategies, config)
    
    def _generate_console_report(self, market_report: MarketEnvironmentReport,
                               daily_strategies: List[StrategyOfTheDay],
                               config: ReportConfiguration) -> str:
        """Generate enhanced console report"""
        
        lines = []
        
        # Header with styling
        lines.append("=" * 100)
        lines.append("🧠 INTELLIGENT STRATEGY RECOMMENDATIONS - ERICA'S FRAMEWORK")
        lines.append(f"📅 {market_report.date.strftime('%A, %B %d, %Y at %I:%M %p')}")
        lines.append("=" * 100)
        lines.append("")
        
        # Executive Summary
        if config.style in [ReportStyle.EXECUTIVE, ReportStyle.DETAILED]:
            lines.extend(self._generate_executive_summary(market_report, daily_strategies))
            lines.append("")
        
        # Market Environment Analysis
        lines.extend(self._generate_market_environment_section(market_report, config))
        lines.append("")
        
        # Strategy Recommendations
        for strategy in daily_strategies:
            lines.extend(self._generate_strategy_section(strategy, config))
            lines.append("")
        
        # Portfolio Summary
        lines.extend(self._generate_portfolio_summary(daily_strategies, config))
        lines.append("")
        
        # Risk Dashboard
        if config.include_risk_analysis:
            lines.extend(self._generate_risk_dashboard(market_report, daily_strategies))
            lines.append("")
        
        # Factor Analysis Summary
        if config.include_factor_analysis:
            lines.extend(self._generate_factor_analysis_summary(daily_strategies))
            lines.append("")
        
        # Monitoring and Alerts
        lines.extend(self._generate_monitoring_section(market_report, daily_strategies))
        lines.append("")
        
        # Educational Insights
        if config.style == ReportStyle.EDUCATIONAL:
            lines.extend(self._generate_educational_insights(daily_strategies))
            lines.append("")
        
        # Footer
        lines.append("=" * 100)
        lines.append("💡 Remember: Systematic approach beats emotional decisions!")
        lines.append("📚 Based on Erica's Millionaire Mentorship Framework (@AbundantlyErica)")
        lines.append("=" * 100)
        
        return "\n".join(lines)
    
    def _generate_executive_summary(self, market_report: MarketEnvironmentReport,
                                  daily_strategies: List[StrategyOfTheDay]) -> List[str]:
        """Generate executive summary section"""
        
        lines = []
        lines.append("📊 EXECUTIVE SUMMARY")
        lines.append("-" * 50)
        
        # Market overview
        lines.append(f"🌍 Market Environment: {market_report.market_regime}")
        lines.append(f"📈 Volatility: {market_report.volatility_environment}")
        lines.append(f"🎭 Sentiment: {market_report.sentiment_reading}")
        lines.append("")
        
        # Strategy distribution
        strategy_counts = {}
        high_confidence_count = 0
        total_confidence = 0
        
        for strategy in daily_strategies:
            strategy_type = strategy.recommended_strategy.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
            
            if strategy.confidence >= 0.7:
                high_confidence_count += 1
            total_confidence += strategy.confidence
        
        avg_confidence = total_confidence / len(daily_strategies) if daily_strategies else 0
        
        lines.append(f"🎯 Today's Focus: {len(daily_strategies)} recommendations, {high_confidence_count} high-confidence")
        lines.append(f"📈 Average Confidence: {avg_confidence:.0%}")
        
        # Top strategy
        if daily_strategies:
            top_strategy = max(daily_strategies, key=lambda s: s.confidence)
            lines.append(f"⭐ Top Recommendation: {top_strategy.symbol} {top_strategy.recommended_strategy.value.replace('_', ' ').title()} ({top_strategy.confidence:.0%})")
        
        return lines
    
    def _generate_market_environment_section(self, market_report: MarketEnvironmentReport,
                                           config: ReportConfiguration) -> List[str]:
        """Generate detailed market environment section"""
        
        lines = []
        lines.append("🌍 MARKET ENVIRONMENT ANALYSIS")
        lines.append("-" * 60)
        
        # Primary indicators
        lines.append(f"📊 {market_report.market_regime}")
        lines.append(f"📈 {market_report.volatility_environment}")
        lines.append(f"🎭 {market_report.sentiment_reading}")
        lines.append("")
        
        # Detailed indicators
        lines.append("🔍 Key Market Indicators:")
        lines.append(f"  • {market_report.vix_analysis}")
        lines.append(f"  • {market_report.put_call_analysis}")
        lines.append(f"  • {market_report.breadth_analysis}")
        lines.append(f"  • Sector rotation: {market_report.sector_rotation_status}")
        lines.append("")
        
        # Today's themes
        if market_report.key_themes:
            lines.append("🎯 Today's Key Themes:")
            for theme in market_report.key_themes:
                lines.append(f"  • {theme}")
            lines.append("")
        
        # Strategy implications
        if market_report.favored_strategies:
            strategy_names = [s.value.replace('_', ' ').title() for s in market_report.favored_strategies]
            lines.append(f"✅ Favored Strategies: {', '.join(strategy_names)}")
        
        if market_report.strategies_to_avoid:
            avoid_names = [s.value.replace('_', ' ').title() for s in market_report.strategies_to_avoid]
            lines.append(f"❌ Strategies to Avoid: {', '.join(avoid_names)}")
        lines.append("")
        
        # Risk factors and opportunities
        if market_report.risk_factors:
            lines.append("⚠️  Key Risk Factors:")
            for risk in market_report.risk_factors:
                lines.append(f"  • {risk}")
            lines.append("")
        
        if market_report.opportunities:
            lines.append("💡 Market Opportunities:")
            for opp in market_report.opportunities:
                lines.append(f"  • {opp}")
            lines.append("")
        
        return lines
    
    def _generate_strategy_section(self, strategy: StrategyOfTheDay,
                                 config: ReportConfiguration) -> List[str]:
        """Generate detailed strategy recommendation section"""
        
        lines = []
        
        # Header with confidence indicator
        confidence_emoji = {
            "very_high": "🟢",
            "high": "🔵",
            "moderate": "🟡", 
            "low": "🟠",
            "very_low": "🔴"
        }.get(strategy.confidence_level.value, "⚪")
        
        lines.append(f"📈 {strategy.symbol} - STRATEGY OF THE DAY")
        lines.append("-" * 70)
        lines.append(f"{confidence_emoji} **{strategy.recommended_strategy.value.replace('_', ' ').upper()}** "
                    f"(Confidence: {strategy.confidence:.0%} - {strategy.confidence_level.value.replace('_', ' ').title()})")
        lines.append("")
        
        # Analysis summary
        if config.style in [ReportStyle.DETAILED, ReportStyle.TECHNICAL]:
            lines.append("🔍 Analysis Summary:")
            lines.append(f"  📊 Market: {strategy.market_environment_summary}")
            lines.append(f"  📈 Stock: {strategy.stock_analysis_summary}")
            lines.append(f"  ✅ Erica's Criteria: {strategy.erica_criteria_alignment}")
            lines.append("")
        
        # Trade setup
        lines.append("🎯 Specific Trade Setup:")
        lines.append(f"  {strategy.specific_trade_setup}")
        lines.append("")
        
        # Entry criteria
        if strategy.entry_criteria and config.include_execution_details:
            lines.append("📥 Entry Criteria:")
            for criteria in strategy.entry_criteria:
                lines.append(f"  ✓ {criteria}")
            lines.append("")
        
        # Position sizing
        lines.append("💰 Position Sizing:")
        lines.append(f"  {strategy.position_sizing_guidance}")
        lines.append("")
        
        # Risk management
        if strategy.key_risks:
            lines.append("⚠️  Key Risks:")
            for risk in strategy.key_risks:
                lines.append(f"  • {risk}")
            lines.append("")
        
        if strategy.risk_mitigation and config.include_risk_analysis:
            lines.append("🛡️ Risk Mitigation:")
            for mitigation in strategy.risk_mitigation:
                lines.append(f"  • {mitigation}")
            lines.append("")
        
        # Exit strategy
        if strategy.exit_criteria:
            lines.append("📤 Exit Strategy:")
            for exit_rule in strategy.exit_criteria:
                lines.append(f"  • {exit_rule}")
            lines.append("")
        
        # Factor breakdown
        if config.include_factor_analysis:
            lines.append("📊 Decision Factor Breakdown:")
            lines.append(f"  • Market Environment: {strategy.factor_breakdown['market_environment']:.0%}")
            lines.append(f"  • Stock-Specific: {strategy.factor_breakdown['stock_specific']:.0%}")
            lines.append(f"  • Erica's Criteria: {strategy.factor_breakdown['erica_criteria']:.0%}")
            lines.append(f"  • Risk-Adjusted: {strategy.factor_breakdown['risk_adjusted']:.0%}")
            lines.append("")
        
        # Alternative strategies
        if strategy.backup_strategies and config.style == ReportStyle.DETAILED:
            lines.append("🔄 Alternative Strategies:")
            for alt_strategy, alt_confidence, reason in strategy.backup_strategies:
                lines.append(f"  • {alt_strategy.value.replace('_', ' ').title()} "
                           f"({alt_confidence:.0%}): {reason}")
            lines.append("")
        
        # Monitoring alerts
        if strategy.conditions_to_watch:
            lines.append("👀 Conditions to Monitor:")
            for condition in strategy.conditions_to_watch[:3]:  # Top 3
                lines.append(f"  • {condition}")
            lines.append("")
        
        lines.append("-" * 70)
        
        return lines
    
    def _generate_portfolio_summary(self, daily_strategies: List[StrategyOfTheDay],
                                  config: ReportConfiguration) -> List[str]:
        """Generate portfolio-level summary"""
        
        lines = []
        lines.append("📋 PORTFOLIO SUMMARY")
        lines.append("-" * 40)
        
        # Strategy distribution
        strategy_counts = {}
        confidence_levels = {"very_high": 0, "high": 0, "moderate": 0, "low": 0, "very_low": 0}
        
        for strategy in daily_strategies:
            strategy_type = strategy.recommended_strategy.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1
            confidence_levels[strategy.confidence_level.value] += 1
        
        lines.append("📊 Strategy Distribution:")
        for strategy_type, count in strategy_counts.items():
            lines.append(f"  • {strategy_type.replace('_', ' ').title()}: {count}")
        lines.append("")
        
        lines.append("🎯 Confidence Distribution:")
        for level, count in confidence_levels.items():
            if count > 0:
                lines.append(f"  • {level.replace('_', ' ').title()}: {count}")
        lines.append("")
        
        # Risk assessment
        total_strategies = len(daily_strategies)
        high_confidence = sum(1 for s in daily_strategies if s.confidence >= 0.7)
        
        lines.append(f"📈 Portfolio Quality: {high_confidence}/{total_strategies} high-confidence recommendations")
        
        # Diversification check
        unique_strategies = len(strategy_counts)
        if unique_strategies >= 3:
            lines.append("✅ Good strategy diversification")
        elif unique_strategies == 2:
            lines.append("⚠️  Moderate strategy diversification")
        else:
            lines.append("❌ Limited strategy diversification")
        
        return lines
    
    def _generate_risk_dashboard(self, market_report: MarketEnvironmentReport,
                               daily_strategies: List[StrategyOfTheDay]) -> List[str]:
        """Generate risk dashboard section"""
        
        lines = []
        lines.append("🛡️ RISK DASHBOARD")
        lines.append("-" * 30)
        
        # Market risk factors
        market_risk_score = 0
        if market_report.risk_factors:
            market_risk_score = min(len(market_report.risk_factors) * 0.25, 1.0)
        
        risk_level = "Low" if market_risk_score < 0.3 else "Medium" if market_risk_score < 0.7 else "High"
        lines.append(f"📊 Market Risk Level: {risk_level}")
        
        # Strategy-specific risks
        high_risk_strategies = [s for s in daily_strategies if len(s.key_risks) >= 3]
        if high_risk_strategies:
            lines.append(f"⚠️  High-Risk Positions: {len(high_risk_strategies)}")
            for strategy in high_risk_strategies:
                lines.append(f"  • {strategy.symbol}: {', '.join(strategy.key_risks[:2])}")
        
        # Risk mitigation summary
        lines.append("")
        lines.append("🛡️ Active Risk Mitigations:")
        all_mitigations = set()
        for strategy in daily_strategies:
            all_mitigations.update(strategy.risk_mitigation[:2])
        
        for mitigation in list(all_mitigations)[:5]:
            lines.append(f"  • {mitigation}")
        
        return lines
    
    def _generate_factor_analysis_summary(self, daily_strategies: List[StrategyOfTheDay]) -> List[str]:
        """Generate factor analysis summary"""
        
        lines = []
        lines.append("📊 FACTOR ANALYSIS SUMMARY")
        lines.append("-" * 40)
        
        # Calculate average factor scores
        if daily_strategies:
            avg_market = sum(s.factor_breakdown['market_environment'] for s in daily_strategies) / len(daily_strategies)
            avg_stock = sum(s.factor_breakdown['stock_specific'] for s in daily_strategies) / len(daily_strategies)
            avg_erica = sum(s.factor_breakdown['erica_criteria'] for s in daily_strategies) / len(daily_strategies)
            avg_risk = sum(s.factor_breakdown['risk_adjusted'] for s in daily_strategies) / len(daily_strategies)
            
            lines.append("📈 Average Factor Scores:")
            lines.append(f"  • Market Environment: {avg_market:.0%}")
            lines.append(f"  • Stock-Specific: {avg_stock:.0%}")
            lines.append(f"  • Erica's Criteria: {avg_erica:.0%}")
            lines.append(f"  • Risk-Adjusted: {avg_risk:.0%}")
            lines.append("")
            
            # Factor insights
            strongest_factor = max([
                ("Market Environment", avg_market),
                ("Stock-Specific", avg_stock),
                ("Erica's Criteria", avg_erica),
                ("Risk-Adjusted", avg_risk)
            ], key=lambda x: x[1])
            
            lines.append(f"💪 Strongest Factor Today: {strongest_factor[0]} ({strongest_factor[1]:.0%})")
            
            if avg_erica > 0.8:
                lines.append("✅ Strong alignment with Erica's criteria across recommendations")
            elif avg_erica < 0.6:
                lines.append("⚠️  Mixed alignment with Erica's criteria - review recommendations")
        
        return lines
    
    def _generate_monitoring_section(self, market_report: MarketEnvironmentReport,
                                   daily_strategies: List[StrategyOfTheDay]) -> List[str]:
        """Generate monitoring and alerts section"""
        
        lines = []
        lines.append("👀 MONITORING & ALERTS")
        lines.append("-" * 35)
        
        # Market monitoring
        lines.append("🌍 Market Conditions to Watch:")
        if market_report.risk_factors:
            for risk in market_report.risk_factors[:3]:
                lines.append(f"  • {risk}")
        lines.append("")
        
        # Strategy monitoring
        lines.append("📈 Strategy Monitoring:")
        for strategy in daily_strategies:
            if strategy.invalidation_signals:
                lines.append(f"  {strategy.symbol}: Watch for {strategy.invalidation_signals[0]}")
        lines.append("")
        
        # Upgrade opportunities
        upgrade_opportunities = []
        for strategy in daily_strategies:
            if strategy.upgrade_opportunities:
                upgrade_opportunities.extend(strategy.upgrade_opportunities[:1])
        
        if upgrade_opportunities:
            lines.append("🚀 Upgrade Opportunities:")
            for opp in upgrade_opportunities[:3]:
                lines.append(f"  • {opp}")
        
        return lines
    
    def _generate_educational_insights(self, daily_strategies: List[StrategyOfTheDay]) -> List[str]:
        """Generate educational insights section"""
        
        lines = []
        lines.append("🎓 EDUCATIONAL INSIGHTS")
        lines.append("-" * 35)
        
        lines.append("💡 Today's Learning Points:")
        lines.append("  • Strategy selection based on market volatility regime")
        lines.append("  • Importance of IV rank in premium selling strategies")
        lines.append("  • Risk management through systematic profit taking")
        lines.append("  • Market regime influence on strategy effectiveness")
        lines.append("")
        
        lines.append("📚 Erica's Key Principles Applied:")
        lines.append("  • Systematic approach over emotional decisions")
        lines.append("  • 50% profit targets for premium selling")
        lines.append("  • Position sizing based on confidence and volatility")
        lines.append("  • Never let a winner become a loser")
        
        return lines
    
    def _generate_html_report(self, market_report: MarketEnvironmentReport,
                            daily_strategies: List[StrategyOfTheDay],
                            config: ReportConfiguration) -> str:
        """Generate HTML report with styling and interactivity"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Strategy Recommendations - {market_report.date.strftime('%Y-%m-%d')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; }}
                .section {{ background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
                .confidence-high {{ color: #28a745; font-weight: bold; }}
                .confidence-moderate {{ color: #ffc107; font-weight: bold; }}
                .confidence-low {{ color: #dc3545; font-weight: bold; }}
                .factor-bar {{ background: #e9ecef; height: 20px; border-radius: 10px; margin: 5px 0; }}
                .factor-fill {{ height: 100%; border-radius: 10px; background: linear-gradient(90deg, #28a745, #ffc107, #dc3545); }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧠 Intelligent Strategy Recommendations</h1>
                <h2>📅 {market_report.date.strftime('%A, %B %d, %Y')}</h2>
            </div>
        """
        
        # Add market environment section
        html_content += f"""
            <div class="section">
                <h2>🌍 Market Environment</h2>
                <p><strong>Market Regime:</strong> {html.escape(market_report.market_regime)}</p>
                <p><strong>Volatility:</strong> {html.escape(market_report.volatility_environment)}</p>
                <p><strong>Sentiment:</strong> {html.escape(market_report.sentiment_reading)}</p>
            </div>
        """
        
        # Add strategy sections
        for strategy in daily_strategies:
            confidence_class = f"confidence-{strategy.confidence_level.value.replace('_', '-')}"
            
            html_content += f"""
                <div class="section">
                    <h2>📈 {strategy.symbol} - {strategy.recommended_strategy.value.replace('_', ' ').title()}</h2>
                    <p class="{confidence_class}">Confidence: {strategy.confidence:.0%}</p>
                    <p><strong>Setup:</strong> {html.escape(strategy.specific_trade_setup)}</p>
                    
                    <h3>Factor Analysis</h3>
                    <div>Market Environment: 
                        <div class="factor-bar">
                            <div class="factor-fill" style="width: {strategy.factor_breakdown['market_environment']*100}%"></div>
                        </div>
                    </div>
                    <div>Erica's Criteria: 
                        <div class="factor-bar">
                            <div class="factor-fill" style="width: {strategy.factor_breakdown['erica_criteria']*100}%"></div>
                        </div>
                    </div>
                </div>
            """
        
        html_content += """
        </body>
        </html>
        """
        
        return html_content
    
    def _generate_json_report(self, market_report: MarketEnvironmentReport,
                            daily_strategies: List[StrategyOfTheDay],
                            config: ReportConfiguration) -> str:
        """Generate JSON report for API consumption"""
        
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "market_environment": asdict(market_report),
            "strategies": [asdict(strategy) for strategy in daily_strategies],
            "summary": {
                "total_recommendations": len(daily_strategies),
                "high_confidence_count": sum(1 for s in daily_strategies if s.confidence >= 0.7),
                "average_confidence": sum(s.confidence for s in daily_strategies) / len(daily_strategies) if daily_strategies else 0,
                "strategy_distribution": self._get_strategy_distribution(daily_strategies)
            }
        }
        
        return json.dumps(report_data, indent=2, default=str)
    
    def _generate_markdown_report(self, market_report: MarketEnvironmentReport,
                                daily_strategies: List[StrategyOfTheDay],
                                config: ReportConfiguration) -> str:
        """Generate Markdown report for documentation"""
        
        lines = []
        lines.append(f"# Strategy Recommendations - {market_report.date.strftime('%Y-%m-%d')}")
        lines.append("")
        lines.append("## Market Environment")
        lines.append(f"- **Market Regime:** {market_report.market_regime}")
        lines.append(f"- **Volatility:** {market_report.volatility_environment}")
        lines.append(f"- **Sentiment:** {market_report.sentiment_reading}")
        lines.append("")
        
        for strategy in daily_strategies:
            lines.append(f"## {strategy.symbol} - {strategy.recommended_strategy.value.replace('_', ' ').title()}")
            lines.append(f"**Confidence:** {strategy.confidence:.0%}")
            lines.append(f"**Setup:** {strategy.specific_trade_setup}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _get_strategy_distribution(self, daily_strategies: List[StrategyOfTheDay]) -> Dict[str, int]:
        """Get strategy distribution for summary"""
        
        distribution = {}
        for strategy in daily_strategies:
            strategy_type = strategy.recommended_strategy.value
            distribution[strategy_type] = distribution.get(strategy_type, 0) + 1
        
        return distribution
    
    def _load_report_templates(self) -> Dict[str, str]:
        """Load report templates"""
        return {
            "executive_summary": "Executive summary template",
            "detailed_analysis": "Detailed analysis template",
            "risk_assessment": "Risk assessment template"
        }
    
    def _load_color_schemes(self) -> Dict[str, Dict[str, str]]:
        """Load color schemes for different output formats"""
        return {
            "console": {
                "success": "\033[92m",
                "warning": "\033[93m", 
                "error": "\033[91m",
                "info": "\033[94m",
                "reset": "\033[0m"
            },
            "html": {
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8"
            }
        }
